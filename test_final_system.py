#!/usr/bin/env python3
"""
测试最终系统配置
验证日志格式和默认参数设置
"""

import json
import glob
import os
from datetime import datetime

def test_log_format():
    """测试日志格式是否正确"""
    
    print("🔍 测试日志格式")
    print("=" * 50)
    
    # 查找最新的多智能体日志文件
    log_files = glob.glob('logs/multiagent_*.json')
    if not log_files:
        print("❌ 未找到多智能体日志文件")
        return False
    
    # 按修改时间排序，获取最新的文件
    latest_log = max(log_files, key=os.path.getmtime)
    print(f"📄 测试文件: {os.path.basename(latest_log)}")
    
    try:
        with open(latest_log, 'r', encoding='utf-8') as f:
            log_data = json.load(f)
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")
        return False
    
    # 验证顶级结构
    required_fields = ['metadata', 'metrics', 'results']
    for field in required_fields:
        if field not in log_data:
            print(f"❌ 缺少顶级字段: {field}")
            return False
    
    # 验证元数据结构
    metadata = log_data['metadata']
    required_metadata = ['run_id', 'dataset', 'model', 'system_type', 'num_samples', 'created_at']
    for field in required_metadata:
        if field not in metadata:
            print(f"❌ 缺少元数据字段: {field}")
            return False
    
    # 验证系统类型
    if metadata['system_type'] != 'intelligent_coordination':
        print(f"❌ 系统类型错误: {metadata['system_type']}, 期望: intelligent_coordination")
        return False
    
    # 验证结果结构
    if not log_data['results']:
        print("❌ 结果为空")
        return False
    
    sample_result = log_data['results'][0]
    required_result_fields = [
        'sample_id', 'text', 'true_label', 'final_verdict', 
        'final_confidence', 'agent_predictions', 'consensus_type',
        'coordination_method', 'weights', 'processing_time'
    ]
    
    for field in required_result_fields:
        if field not in sample_result:
            print(f"❌ 结果缺少字段: {field}")
            return False
    
    # 验证智能体预测结构
    agent_predictions = sample_result['agent_predictions']
    expected_agents = ['deberta', 'llm_single', 'llm_retrieval']
    
    for agent in expected_agents:
        if agent not in agent_predictions:
            print(f"❌ 缺少智能体: {agent}")
            return False
        
        pred = agent_predictions[agent]
        required_pred_fields = ['verdict', 'confidence', 'processing_time']
        for field in required_pred_fields:
            if field not in pred:
                print(f"❌ 智能体 {agent} 缺少字段: {field}")
                return False
    
    # 验证权重结构
    weights = sample_result['weights']
    if 'original' not in weights or 'optimized' not in weights:
        print("❌ 权重结构不完整")
        return False
    
    print("✅ 日志格式验证通过")
    print(f"   📊 样本数: {len(log_data['results'])}")
    print(f"   🎯 准确率: {log_data['metrics']['accuracy']:.3f}")
    print(f"   🤖 智能体: {', '.join(expected_agents)}")
    print(f"   🔄 协调方法: {sample_result['coordination_method']}")
    print(f"   💾 文件大小: {os.path.getsize(latest_log):,} bytes")
    
    return True

def test_file_naming():
    """测试文件命名是否正确"""
    
    print("\n🏷️  测试文件命名")
    print("=" * 50)
    
    # 查找最新的多智能体日志文件
    log_files = glob.glob('logs/multiagent_*.json')
    if not log_files:
        print("❌ 未找到多智能体日志文件")
        return False
    
    latest_log = max(log_files, key=os.path.getmtime)
    filename = os.path.basename(latest_log)
    
    # 验证文件名格式
    if not filename.startswith('multiagent_'):
        print(f"❌ 文件名格式错误: {filename}")
        print("   期望格式: multiagent_<model>_<dataset>_log_<run_id>_<timestamp>.json")
        return False
    
    if filename.startswith('cleaned_multiagent_'):
        print(f"❌ 文件名包含'cleaned'前缀: {filename}")
        print("   应该直接使用 multiagent_ 前缀")
        return False
    
    print(f"✅ 文件命名正确: {filename}")
    return True

def test_system_defaults():
    """测试系统默认配置"""
    
    print("\n⚙️  测试系统默认配置")
    print("=" * 50)
    
    # 查找最新的多智能体日志文件
    log_files = glob.glob('logs/multiagent_*.json')
    if not log_files:
        print("❌ 未找到多智能体日志文件")
        return False
    
    latest_log = max(log_files, key=os.path.getmtime)
    
    with open(latest_log, 'r', encoding='utf-8') as f:
        log_data = json.load(f)
    
    metadata = log_data['metadata']
    
    # 验证默认系统类型
    if metadata['system_type'] != 'intelligent_coordination':
        print(f"❌ 默认系统类型错误: {metadata['system_type']}")
        return False
    
    # 验证系统配置存在
    if 'system_config' not in metadata:
        print("❌ 缺少系统配置")
        return False
    
    config = metadata['system_config']
    expected_config = [
        'enable_dynamic_weights', 'enable_performance_tracking',
        'enable_online_learning', 'coordination_method',
        'learning_rate', 'confidence_threshold'
    ]
    
    for field in expected_config:
        if field not in config:
            print(f"❌ 缺少配置字段: {field}")
            return False
    
    print("✅ 系统默认配置正确")
    print(f"   🎯 系统类型: {metadata['system_type']}")
    print(f"   🔄 协调方法: {config['coordination_method']}")
    print(f"   📈 动态权重: {config['enable_dynamic_weights']}")
    print(f"   📊 性能追踪: {config['enable_performance_tracking']}")
    print(f"   🧠 在线学习: {config['enable_online_learning']}")
    print(f"   🎓 学习率: {config['learning_rate']}")
    
    return True

def compare_file_sizes():
    """比较新旧格式文件大小"""
    
    print("\n💾 文件大小对比")
    print("=" * 50)
    
    # 查找旧格式文件（如果存在）
    old_files = glob.glob('logs_cleaned/cleaned_multiagent_*.json')
    new_files = glob.glob('logs/multiagent_*.json')
    
    if old_files and new_files:
        old_sizes = [os.path.getsize(f) for f in old_files]
        new_sizes = [os.path.getsize(f) for f in new_files]
        
        avg_old_size = sum(old_sizes) / len(old_sizes)
        avg_new_size = sum(new_sizes) / len(new_sizes)
        
        print(f"📊 文件大小对比:")
        print(f"   旧清理后格式: {avg_old_size:,.0f} bytes (平均)")
        print(f"   新直接格式: {avg_new_size:,.0f} bytes (平均)")
        
        if abs(avg_old_size - avg_new_size) < 1000:  # 差异小于1KB
            print("✅ 文件大小基本一致，格式转换成功")
        else:
            print(f"⚠️  文件大小差异较大: {abs(avg_old_size - avg_new_size):,.0f} bytes")
    else:
        print("📝 无法进行对比（缺少对比文件）")
        if new_files:
            new_size = os.path.getsize(new_files[-1])
            print(f"   当前文件大小: {new_size:,} bytes")

def main():
    """运行所有测试"""
    
    print("🧪 最终系统测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("日志格式测试", test_log_format),
        ("文件命名测试", test_file_naming),
        ("系统默认配置测试", test_system_defaults)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    # 文件大小对比（不计入测试结果）
    compare_file_sizes()
    
    print("\n" + "=" * 60)
    print(f"📋 测试总结:")
    print(f"   通过: {passed}/{total}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统配置正确")
        print("✅ 日志格式: 直接生成清理后格式")
        print("✅ 文件命名: 去除'cleaned'前缀")
        print("✅ 默认系统: intelligent_coordination")
        print("✅ 参数简化: 无需指定--system-type")
    else:
        print(f"\n⚠️  {total-passed} 个测试失败，请检查配置")

if __name__ == "__main__":
    main()
