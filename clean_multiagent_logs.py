#!/usr/bin/env python3
"""
多智能体日志清理工具
清理冗余字段，保留核心必要信息
"""

import json
import os
import glob
from datetime import datetime
from typing import Dict, List, Any

def clean_log_entry(entry: Dict[str, Any], sample_id: int) -> Dict[str, Any]:
    """清理单个日志条目，保留核心信息"""
    
    # 提取智能体预测结果（简化版）
    agent_predictions = {}
    if 'agent_results' in entry:
        for agent_name, agent_data in entry['agent_results'].items():
            agent_predictions[agent_name] = {
                'verdict': agent_data.get('verdict'),
                'confidence': round(agent_data.get('confidence', 0), 3),
                'processing_time': round(agent_data.get('processing_time', 0), 3)
            }
    
    # 构建清理后的条目
    cleaned_entry = {
        'sample_id': sample_id,
        'text': entry.get('text', ''),
        'true_label': entry.get('true_label'),
        'final_verdict': entry.get('verdict'),
        'final_confidence': round(entry.get('confidence', 0), 3),
        'agent_predictions': agent_predictions,
        'consensus_type': entry.get('agent_consensus', 'unknown'),
        'coordination_method': entry.get('coordination_method', 'unknown'),
        'weights': entry.get('learned_weights', entry.get('agent_weights', {})),
        'processing_time': round(entry.get('processing_time', 0), 3)
    }
    
    # 四舍五入权重值
    if isinstance(cleaned_entry['weights'], dict) and cleaned_entry['weights']:
        cleaned_entry['weights'] = {
            k: round(v, 3) for k, v in cleaned_entry['weights'].items()
        }
    
    return cleaned_entry

def clean_multiagent_log(log_file_path: str) -> Dict[str, Any]:
    """清理多智能体日志文件"""
    
    with open(log_file_path, 'r', encoding='utf-8') as f:
        original_log = json.load(f)
    
    # 构建清理后的日志结构
    cleaned_log = {
        'metadata': {
            'run_id': original_log.get('run_id'),
            'dataset': original_log.get('dataset'),
            'model': original_log.get('model'),
            'system_type': original_log.get('system_type'),
            'num_samples': original_log.get('num_samples'),
            'cleaned_at': datetime.now().isoformat()
        },
        'metrics': original_log.get('metrics', {}),
        'results': []
    }
    
    # 清理每个结果条目
    if 'results' in original_log:
        for i, entry in enumerate(original_log['results']):
            cleaned_entry = clean_log_entry(entry, i + 1)
            cleaned_log['results'].append(cleaned_entry)
    
    return cleaned_log

def clean_all_logs(logs_dir: str = 'logs', output_dir: str = 'logs_cleaned'):
    """清理所有多智能体日志文件"""
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 查找所有多智能体日志文件
    log_pattern = os.path.join(logs_dir, 'multiagent_*.json')
    log_files = glob.glob(log_pattern)
    
    if not log_files:
        print(f"❌ 在 {logs_dir} 目录中未找到多智能体日志文件")
        return
    
    print(f"🔍 找到 {len(log_files)} 个多智能体日志文件")
    
    for log_file in log_files:
        try:
            print(f"📝 正在清理: {os.path.basename(log_file)}")
            
            # 清理日志
            cleaned_log = clean_multiagent_log(log_file)
            
            # 生成输出文件名
            base_name = os.path.basename(log_file)
            cleaned_name = base_name.replace('multiagent_', 'cleaned_multiagent_')
            output_path = os.path.join(output_dir, cleaned_name)
            
            # 保存清理后的日志
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(cleaned_log, f, ensure_ascii=False, indent=2)
            
            # 计算文件大小减少
            original_size = os.path.getsize(log_file)
            cleaned_size = os.path.getsize(output_path)
            reduction = (1 - cleaned_size / original_size) * 100
            
            print(f"✅ 清理完成: {cleaned_name}")
            print(f"   原始大小: {original_size:,} bytes")
            print(f"   清理后大小: {cleaned_size:,} bytes")
            print(f"   减少: {reduction:.1f}%")
            print()
            
        except Exception as e:
            print(f"❌ 清理失败 {log_file}: {e}")

def analyze_cleaned_log(log_file_path: str):
    """分析清理后的日志文件"""
    
    with open(log_file_path, 'r', encoding='utf-8') as f:
        log_data = json.load(f)
    
    print("📊 清理后日志分析:")
    print(f"   数据集: {log_data['metadata']['dataset']}")
    print(f"   模型: {log_data['metadata']['model']}")
    print(f"   样本数: {log_data['metadata']['num_samples']}")
    print(f"   准确率: {log_data['metrics']['accuracy']:.3f}")
    print(f"   F1分数: {log_data['metrics']['f1']:.3f}")
    print()
    
    # 分析智能体性能
    if log_data['results']:
        agent_names = list(log_data['results'][0]['agent_predictions'].keys())
        print("🤖 智能体性能分析:")
        
        for agent in agent_names:
            correct = 0
            total = 0
            avg_confidence = 0
            avg_time = 0
            
            for result in log_data['results']:
                if agent in result['agent_predictions']:
                    pred = result['agent_predictions'][agent]
                    if pred['verdict'] == result['true_label']:
                        correct += 1
                    total += 1
                    avg_confidence += pred['confidence']
                    avg_time += pred['processing_time']
            
            if total > 0:
                accuracy = correct / total
                avg_confidence /= total
                avg_time /= total
                
                print(f"   {agent}:")
                print(f"     准确率: {accuracy:.3f}")
                print(f"     平均置信度: {avg_confidence:.3f}")
                print(f"     平均处理时间: {avg_time:.3f}s")
        
        print()
    
    # 分析协调方法
    coordination_methods = {}
    consensus_types = {}
    
    for result in log_data['results']:
        method = result['coordination_method']
        consensus = result['consensus_type']
        
        coordination_methods[method] = coordination_methods.get(method, 0) + 1
        consensus_types[consensus] = consensus_types.get(consensus, 0) + 1
    
    print("🔄 协调方法分析:")
    for method, count in coordination_methods.items():
        print(f"   {method}: {count} 次 ({count/len(log_data['results'])*100:.1f}%)")
    
    print("\n🤝 共识类型分析:")
    for consensus, count in consensus_types.items():
        print(f"   {consensus}: {count} 次 ({count/len(log_data['results'])*100:.1f}%)")

if __name__ == "__main__":
    print("🧹 多智能体日志清理工具")
    print("=" * 50)
    
    # 清理所有日志文件
    clean_all_logs()
    
    # 分析第一个清理后的文件
    cleaned_files = glob.glob('logs_cleaned/cleaned_multiagent_*.json')
    if cleaned_files:
        print("\n" + "=" * 50)
        analyze_cleaned_log(cleaned_files[0])
