#!/usr/bin/env python3
"""
验证新生成的清理后格式日志文件
"""

import json
import glob
import os
from typing import Dict, Any

def validate_cleaned_log_format(log_file_path: str) -> Dict[str, Any]:
    """验证清理后格式的日志文件"""
    
    validation_results = {
        'file_path': log_file_path,
        'valid': True,
        'errors': [],
        'warnings': [],
        'stats': {}
    }
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            log_data = json.load(f)
    except Exception as e:
        validation_results['errors'].append(f"无法读取文件: {e}")
        validation_results['valid'] = False
        return validation_results
    
    # 验证顶级结构
    required_top_level = ['metadata', 'metrics', 'results']
    for field in required_top_level:
        if field not in log_data:
            validation_results['errors'].append(f"缺少顶级字段: {field}")
            validation_results['valid'] = False
    
    # 验证元数据结构
    if 'metadata' in log_data:
        required_metadata = ['run_id', 'dataset', 'model', 'system_type', 'num_samples', 'created_at']
        for field in required_metadata:
            if field not in log_data['metadata']:
                validation_results['errors'].append(f"缺少元数据字段: {field}")
                validation_results['valid'] = False
        
        # 验证系统配置
        if 'system_config' in log_data['metadata']:
            config = log_data['metadata']['system_config']
            expected_config_fields = [
                'enable_dynamic_weights', 'enable_performance_tracking', 
                'enable_online_learning', 'coordination_method', 
                'learning_rate', 'confidence_threshold'
            ]
            for field in expected_config_fields:
                if field not in config:
                    validation_results['warnings'].append(f"缺少系统配置字段: {field}")
    
    # 验证指标结构
    if 'metrics' in log_data:
        required_metrics = ['accuracy', 'precision', 'recall', 'f1', 'confusion_matrix', 'avg_processing_time']
        for field in required_metrics:
            if field not in log_data['metrics']:
                validation_results['errors'].append(f"缺少指标字段: {field}")
                validation_results['valid'] = False
    
    # 验证结果条目结构
    if 'results' in log_data:
        results = log_data['results']
        validation_results['stats']['total_samples'] = len(results)
        
        required_result_fields = [
            'sample_id', 'text', 'true_label', 'final_verdict', 
            'final_confidence', 'agent_predictions', 'consensus_type',
            'coordination_method', 'weights', 'processing_time'
        ]
        
        for i, result in enumerate(results):
            # 验证必需字段
            for field in required_result_fields:
                if field not in result:
                    validation_results['errors'].append(
                        f"样本 {i+1} 缺少字段: {field}"
                    )
                    validation_results['valid'] = False
            
            # 验证智能体预测结构
            if 'agent_predictions' in result:
                for agent, pred in result['agent_predictions'].items():
                    required_pred_fields = ['verdict', 'confidence', 'processing_time']
                    for field in required_pred_fields:
                        if field not in pred:
                            validation_results['errors'].append(
                                f"样本 {i+1} 智能体 {agent} 缺少字段: {field}"
                            )
                            validation_results['valid'] = False
            
            # 验证权重结构
            if 'weights' in result:
                if 'original' not in result['weights'] or 'optimized' not in result['weights']:
                    validation_results['warnings'].append(
                        f"样本 {i+1} 权重信息不完整"
                    )
            
            # 验证sample_id连续性
            expected_id = i + 1
            if result.get('sample_id') != expected_id:
                validation_results['warnings'].append(
                    f"样本ID不连续: 期望 {expected_id}, 实际 {result.get('sample_id')}"
                )
    
    # 计算统计信息
    if validation_results['valid'] and 'results' in log_data:
        results = log_data['results']
        
        # 验证准确率计算
        correct = sum(1 for r in results if r['final_verdict'] == r['true_label'])
        calculated_accuracy = correct / len(results) if results else 0
        reported_accuracy = log_data['metrics']['accuracy']
        
        if abs(calculated_accuracy - reported_accuracy) > 0.001:
            validation_results['warnings'].append(
                f"准确率不匹配: 计算值 {calculated_accuracy:.3f} vs 报告值 {reported_accuracy:.3f}"
            )
        
        validation_results['stats']['calculated_accuracy'] = calculated_accuracy
        validation_results['stats']['reported_accuracy'] = reported_accuracy
        
        # 智能体统计
        if results:
            agent_names = list(results[0]['agent_predictions'].keys())
            validation_results['stats']['agents'] = agent_names
            validation_results['stats']['agent_count'] = len(agent_names)
            
            # 协调方法统计
            coordination_methods = [r['coordination_method'] for r in results]
            validation_results['stats']['coordination_methods'] = list(set(coordination_methods))
            
            # 共识类型统计
            consensus_types = [r['consensus_type'] for r in results]
            validation_results['stats']['consensus_types'] = list(set(consensus_types))
    
    return validation_results

def validate_all_new_logs():
    """验证所有新生成的清理后格式日志文件"""
    
    print("🔍 验证新生成的清理后格式日志文件")
    print("=" * 60)
    
    # 查找所有新生成的清理后格式日志文件
    log_pattern = 'logs/cleaned_multiagent_*.json'
    log_files = glob.glob(log_pattern)
    
    if not log_files:
        print("❌ 未找到新生成的清理后格式日志文件")
        return
    
    print(f"找到 {len(log_files)} 个新生成的日志文件")
    
    all_valid = True
    total_errors = 0
    total_warnings = 0
    
    for log_file in log_files:
        print(f"\n🔍 验证: {os.path.basename(log_file)}")
        validation = validate_cleaned_log_format(log_file)
        
        if validation['valid']:
            print("   ✅ 验证通过")
            if validation['stats']:
                print(f"   📊 样本数: {validation['stats']['total_samples']}")
                print(f"   🤖 智能体: {', '.join(validation['stats']['agents'])}")
                print(f"   🎯 准确率: {validation['stats']['calculated_accuracy']:.3f}")
                print(f"   🔄 协调方法: {', '.join(validation['stats']['coordination_methods'])}")
                print(f"   🤝 共识类型: {', '.join(validation['stats']['consensus_types'])}")
        else:
            print("   ❌ 验证失败")
            all_valid = False
            for error in validation['errors']:
                print(f"     错误: {error}")
        
        total_errors += len(validation['errors'])
        total_warnings += len(validation['warnings'])
        
        for warning in validation['warnings']:
            print(f"     警告: {warning}")
        
        # 显示文件大小
        file_size = os.path.getsize(log_file)
        print(f"   💾 文件大小: {file_size:,} bytes")
    
    print(f"\n" + "=" * 60)
    print(f"📋 验证总结:")
    print(f"   总文件数: {len(log_files)}")
    print(f"   验证通过: {'✅ 全部' if all_valid else '❌ 部分'}")
    print(f"   总错误数: {total_errors}")
    print(f"   总警告数: {total_warnings}")
    
    if all_valid:
        print(f"\n🎉 所有新生成的日志文件都符合清理后格式规范！")
        print(f"   ✅ 直接生成清理后格式")
        print(f"   ✅ 无需后处理步骤")
        print(f"   ✅ 文件大小已优化")
        print(f"   ✅ 保留所有核心信息")
    else:
        print(f"\n⚠️  部分文件存在问题，请检查错误信息")

def compare_with_old_format():
    """比较新格式和旧格式的差异"""
    
    print(f"\n" + "=" * 60)
    print("📊 新旧格式对比分析")
    print("=" * 60)
    
    # 查找旧格式文件
    old_files = glob.glob('logs/multiagent_*.json')
    old_files = [f for f in old_files if not f.startswith('logs/cleaned_')]
    
    # 查找新格式文件
    new_files = glob.glob('logs/cleaned_multiagent_*.json')
    
    print(f"旧格式文件: {len(old_files)} 个")
    print(f"新格式文件: {len(new_files)} 个")
    
    if old_files and new_files:
        # 比较文件大小
        old_sizes = [os.path.getsize(f) for f in old_files]
        new_sizes = [os.path.getsize(f) for f in new_files]
        
        avg_old_size = sum(old_sizes) / len(old_sizes)
        avg_new_size = sum(new_sizes) / len(new_sizes)
        reduction = (1 - avg_new_size / avg_old_size) * 100
        
        print(f"\n💾 文件大小对比:")
        print(f"   旧格式平均大小: {avg_old_size:,.0f} bytes")
        print(f"   新格式平均大小: {avg_new_size:,.0f} bytes")
        print(f"   大小减少: {reduction:.1f}%")
        
        print(f"\n🔧 格式改进:")
        print(f"   ✅ 直接生成清理后格式")
        print(f"   ✅ 无需额外清理步骤")
        print(f"   ✅ 统一的元数据结构")
        print(f"   ✅ 优化的字段组织")
        print(f"   ✅ 保留所有核心信息")

if __name__ == "__main__":
    validate_all_new_logs()
    compare_with_old_format()
