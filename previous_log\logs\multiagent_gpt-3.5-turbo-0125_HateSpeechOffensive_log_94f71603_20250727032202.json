{"run_id": "94f71603", "dataset": "HateSpeechOffensive", "model": "gpt-3.5-turbo-0125", "detector_type": "intelligent_coordination", "system_type": "intelligent_coordination", "num_samples": 100, "metrics": {"accuracy": 0.88, "precision": 0.9285714285714286, "recall": 0.9285714285714286, "f1": 0.9285714285714286, "confusion_matrix": [[10, 6], [6, 78]], "avg_processing_time": 6.1851261353492735}, "results": [{"verdict": 0, "confidence": 0.561086680740118, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9886934459209442, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.989 confidence", "processing_time": 0.684110164642334}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Referring to a person as a 'monkey' is derogatory and offensive.", "processing_time": 1.4726579189300537}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is not offensive as it refers to a monkey from a film in the context of a CNN panel discussion about Ebola.", "processing_time": 2.957260847091675, "retrieved_examples": "Example 1: \"What the monkey... http://t.co/h76rptL8Wq\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"I ate a monkey. #AndNowIHaveEbola\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"@EmmaLouisaSuttl @timmyn90 Anyone bring in a monkey.\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.9612553119659424, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 1, "dynamic_weight_predictions": 1, "consensus_predictions": 0, "avg_coordination_time": 2.9612553119659424, "system_type": "intelligent_coordination"}, "text": "RT @McNarstle: ...and rounding out our CNN Ebola panel tonight, the monkey from the 1995 film, OUTBREAK.", "true_label": 0, "processing_time": 2.9612553119659424, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5416401252150536, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.83); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8331210017204285, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.833 confidence", "processing_time": 0.06308102607727051}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit language and aggressive tone towards recycling practices", "processing_time": 0.8009819984436035}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech contains offensive language and aggressive tone towards not caring about proper trash disposal.", "processing_time": 2.2117648124694824, "retrieved_examples": "Example 1: \"@gregorious13 I don't understand it, there's trash cans/dumpsters everywhere and if there's not, just wait till you go somewhere with one.\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"I hate takin out the trash, I always be expecting a big ass dog to be in the alley or like a raccoon to jump out the dumpster\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"i thought garbage men were supposed to bring trash to the dump but they wouldn't take me ha ha ha. self depreciation ftw.\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.219740629196167, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 2, "dynamic_weight_predictions": 2, "consensus_predictions": 0, "avg_coordination_time": 2.5904979705810547, "system_type": "intelligent_coordination"}, "text": "If you arent going to stick to one garbage can for recycling Im not going to give a single fuck about where Im putting my trash.", "true_label": 1, "processing_time": 2.219740629196167, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5416829138994217, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.83); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8334633111953735, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.833 confidence", "processing_time": 0.06226921081542969}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language and objectification of individuals, which is offensive and inappropriate.", "processing_time": 0.9406759738922119}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to women as 'hoes' and expresses frustration over not receiving affectionate texts.", "processing_time": 1.9424829483032227, "retrieved_examples": "Example 1: \"I be lonely&#128514;&#128514; I have no hoes &#128553;&#128514;&#9995;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I ain't got no hoes&#128530;&#128074;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Let me text my hoes gm or sumpin\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 1.9481532573699951, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 3, "dynamic_weight_predictions": 3, "consensus_predictions": 0, "avg_coordination_time": 2.376383066177368, "system_type": "intelligent_coordination"}, "text": "I need sum hoes ah sum, cuz I wake up all I have is hella emails &#128557;&#128299; but not a Goodmoring or goodnight text. Shit not even a single text &#9995;&#128529;", "true_label": 1, "processing_time": 1.9481532573699951, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5594626888632774, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.98); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9757015109062195, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.976 confidence", "processing_time": 0.05762004852294922}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit and offensive language with a derogatory and sexually violent connotation.", "processing_time": 0.808396577835083}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains explicit and offensive language referring to a violent and degrading act towards a woman.", "processing_time": 125.73990964889526, "retrieved_examples": "Example 1: \"Fuck her right in the pussy.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"fuck her rite in the pussy\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Kick that bitch right in the pussy.\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 125.74307942390442, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 4, "dynamic_weight_predictions": 4, "consensus_predictions": 0, "avg_coordination_time": 33.21805715560913, "system_type": "intelligent_coordination"}, "text": "Run it in her pussy like a crash dummy.", "true_label": 1, "processing_time": 125.74307942390442, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5487971864640713, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.89); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8903774917125702, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.890 confidence", "processing_time": 0.061395883560180664}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains derogatory language towards women", "processing_time": 0.8962781429290771}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it refers to women in a derogatory manner.", "processing_time": 3.126106023788452, "retrieved_examples": "Example 1: \"@CarelessOne92 weeknd make muzik for the hoes b, im tryna meet these women he be talkin bout\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"The weeknd is why bitches love dark skin fellas now.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Modern day singers talk about the same shit rappers talk about lol....hoes\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 3.132883071899414, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 5, "dynamic_weight_predictions": 5, "consensus_predictions": 0, "avg_coordination_time": 27.201022338867187, "system_type": "intelligent_coordination"}, "text": "weeknd makes songs for the hoes", "true_label": 1, "processing_time": 3.132883071899414, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5577939078211784, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.96); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9623512625694275, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.962 confidence", "processing_time": 0.05688285827636719}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and derogatory terms towards individuals", "processing_time": 0.916426420211792}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms.", "processing_time": 2.8617348670959473, "retrieved_examples": "Example 1: \"A nicca is hungry ... AS FUCK!!\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"A nicca is hungry as fuk!!\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"A nicca is hungry as hell\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.8672142028808594, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 6, "dynamic_weight_predictions": 6, "consensus_predictions": 0, "avg_coordination_time": 23.145387649536133, "system_type": "intelligent_coordination"}, "text": "young buck wanna eat!!.. dat nigguh like I aint fuckin dis up again", "true_label": 1, "processing_time": 2.8672142028808594, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5540926828980446, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.93); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9327414631843567, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.933 confidence", "processing_time": 0.06087636947631836}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and derogatory language towards young individuals", "processing_time": 1.6540439128875732}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms towards young individuals.", "processing_time": 2.457859516143799, "retrieved_examples": "Example 1: \"Fuck them kids bitch\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"cant fuck around wit young niggas cause they be acting like hoes\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Kids a bitch\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.462632417678833, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 7, "dynamic_weight_predictions": 7, "consensus_predictions": 0, "avg_coordination_time": 20.190708330699376, "system_type": "intelligent_coordination"}, "text": "These kids man real life kids not even over 18 n bucking bitch sit yo stupid ass down", "true_label": 1, "processing_time": 2.462632417678833, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5546278022229671, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.94); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9370224177837372, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.937 confidence", "processing_time": 0.06199288368225098}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of the term 'hoes' is derogatory and offensive towards women.", "processing_time": 0.8051164150238037}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to women as 'hoes' and instructing them to 'grow up'. This is disrespectful and offensive.", "processing_time": 2.6290154457092285, "retrieved_examples": "Example 1: \"&#8220;@prissyneeer: @WhereYoHussleAt smh they gotta grow up &#128514; at least you know these bitches want to be you &#128524;&#8221; I swear &#9994;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@<PERSON><PERSON><PERSON><PERSON> lol tell that hoe grow up ahaha\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"you gotta understand that these bitches are childish\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.6349949836730957, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 8, "dynamic_weight_predictions": 8, "consensus_predictions": 0, "avg_coordination_time": 17.99624416232109, "system_type": "intelligent_coordination"}, "text": "Sometimes, even I gotta tell these hoes to grow up smh", "true_label": 0, "processing_time": 2.6355462074279785, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5621417686343193, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:1.00); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9971341490745544, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.997 confidence", "processing_time": 0.061295509338378906}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and derogatory language directed at an individual", "processing_time": 0.9751195907592773}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms, making it offensive.", "processing_time": 1.966080904006958, "retrieved_examples": "Example 1: \"Lol u mad motherfuckin hoe\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"shit piss fuck cunt cocksucker motherfucker tits\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@leetreble_ quit bitching faggot, happy you cant see your faggot family cry baby little girl crying in the parking lot, you aint a man sissy\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 1.9719841480255127, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 9, "dynamic_weight_predictions": 9, "consensus_predictions": 0, "avg_coordination_time": 16.21577082739936, "system_type": "intelligent_coordination"}, "text": "ha fucking twat. go cry about mommy some more", "true_label": 1, "processing_time": 1.9719841480255127, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5598493181169033, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.98); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9787945449352264, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.979 confidence", "processing_time": 0.060881614685058594}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and objectifying language towards women, referring to their physical appearance in a disrespectful manner.", "processing_time": 2.120709180831909}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and objectifies women based on their physical appearance.", "processing_time": 2.0798964500427246, "retrieved_examples": "Example 1: \"I was in the club and there was this good lookin bitch. ..she had gold teeth tho. I was confused smh\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"ooh girl cute face but yellow teef. lol\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Cocky bitches with front teeth that stick out from suckin they thumb &lt;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.1236109733581543, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 10, "dynamic_weight_predictions": 10, "consensus_predictions": 0, "avg_coordination_time": 14.80655484199524, "system_type": "intelligent_coordination"}, "text": "Why is it everytime I go to cracker barrel the girl with great tits that seats you who is your waitress nope its the chick with 2 teeth", "true_label": 1, "processing_time": 2.1236109733581543, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.548621691763401, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.89); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8889735341072083, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.889 confidence", "processing_time": 0.059157371520996094}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and derogatory language towards individuals", "processing_time": 1.7182621955871582}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms towards individuals.", "processing_time": 1.898926019668579, "retrieved_examples": "Example 1: \"@<PERSON><PERSON><PERSON><PERSON> @FuckTheMallcops @UncleUnco @MrMooncricket fuck off bitch.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@FuckTheMallcops @theharris338 @MrMooncricket @BobbyBotsods @JimTalbot1963 @UncleUnco aids fuck? Smdh u punk bitch.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"You ain't got no job, no diploma, no money, bitch at your parents when they don't give you money for weed, yet We need to act like adults??\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 1.903627872467041, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 11, "dynamic_weight_predictions": 11, "consensus_predictions": 0, "avg_coordination_time": 13.633561481129039, "system_type": "intelligent_coordination"}, "text": "@MrMooncricket @FuckTheMallcops @JimTalbot1963 @theharris338 work, is something adults do for money, not borrow from money... Broke bitch", "true_label": 1, "processing_time": 1.903627872467041, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5497229968508084, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8977839748064677, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.898 confidence", "processing_time": 0.06199073791503906}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and offensive language towards women", "processing_time": 2.1693062782287598}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it degrades and insults women based on their personal choices and behaviors.", "processing_time": 2.0973501205444336, "retrieved_examples": "Example 1: \"RT @StevStiffler: If her bio says \"Only God can judge me\" she's a hoe.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"If her bio says \"Only God can judge me\" she's a hoe.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @IamMrEchols: 9 times outta 10 a bitch gonna know if she a hoe or not\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.173839807510376, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 12, "dynamic_weight_predictions": 12, "consensus_predictions": 0, "avg_coordination_time": 12.67858467499415, "system_type": "intelligent_coordination"}, "text": "RT @TrapicalGod: If she has \"You know my name not my story\" in her bio you know she a hoe", "true_label": 1, "processing_time": 2.173839807510376, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5587728694081306, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.97); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9701829552650452, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.970 confidence", "processing_time": 0.06196928024291992}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text contains explicit content but does not include offensive speech as it is a description of adult content.", "processing_time": 1.5457522869110107}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains explicit and sexually suggestive language, describing a sexual act involving a dildo and a wet pussy.", "processing_time": 122.82996654510498, "retrieved_examples": "Example 1: \"http://t.co/bPU86qzYFt Lola Foxx &#8211; <PERSON><PERSON><PERSON> co-ed shows off her pink pussy while she fondles&#160;herself http://t.co/9QJPbdyzTY\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"http://t.co/bPU86qzYFt Lilith Lee &#8211; Hot blonde teen fucks her tender pussy with a&#160;dildo http://t.co/FpU2FAy9CO\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"http://t.co/bPU86qzYFt Ioana &#8211; Tiny teen I<PERSON>a fucks her tender pussy with a&#160;vibrator http://t.co/bRzLCfy7xE\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 122.83483505249023, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 13, "dynamic_weight_predictions": 13, "consensus_predictions": 0, "avg_coordination_time": 21.152142396340004, "system_type": "intelligent_coordination"}, "text": "http://t.co/bPU86qzYFt Lola Foxx &#8211; Steaming hot Nubile <PERSON> shoves her dildo deep into her wet&#160;pussy http://t.co/br1Mjkk8dz", "true_label": 1, "processing_time": 122.83483505249023, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5614655539393425, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.99172443151474, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.992 confidence", "processing_time": 0.062259674072265625}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and offensive language directed at others.", "processing_time": 0.7895925045013428}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and disrespectful tone towards others.", "processing_time": 2.678618907928467, "retrieved_examples": "Example 1: \"Shawty pussy wet, the dope drippn wet &#128166;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Fuccn yo bitch and its wet like a pool&#128166;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Falls in that pussy like quick sand &#128524;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.684580087661743, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 14, "dynamic_weight_predictions": 14, "consensus_predictions": 0, "avg_coordination_time": 19.833030802862986, "system_type": "intelligent_coordination"}, "text": "It already Soaked, Sinked, &amp; Melted so dropped that's shit bitch IDGAF linc up or get caught &#128175;", "true_label": 1, "processing_time": 2.684580087661743, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5507339015603065, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9058712124824524, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.906 confidence", "processing_time": 0.061655521392822266}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains explicit derogatory content directed at <PERSON><PERSON><PERSON>", "processing_time": 0.8131608963012695}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains derogatory language towards Rih<PERSON>.", "processing_time": 2.2172207832336426, "retrieved_examples": "Example 1: \"RT @BroganWebber: Beyonc&#233; is trash.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @Great_Bryt: Riff Raff is trash\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"RT @_lvrking: chris brown is trash.\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.219841480255127, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 15, "dynamic_weight_predictions": 15, "consensus_predictions": 0, "avg_coordination_time": 18.658818181355795, "system_type": "intelligent_coordination"}, "text": "RT @Comp10Tx: <PERSON><PERSON><PERSON> really is trash", "true_label": 0, "processing_time": 2.219841480255127, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5605004243552685, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.98); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9840033948421478, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.984 confidence", "processing_time": 0.06397652626037598}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech contains offensive language and derogatory terms directed at individuals, which is considered offensive.", "processing_time": 0.8712267875671387}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it uses derogatory language towards women.", "processing_time": 3.1550164222717285, "retrieved_examples": "Example 1: \"If you a bad bitch, put ya hands up high\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"\"If you're a bad bitch put your hands up high\" \nEvery girl in the room starts waving there hands around.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Put your hands down bitch, I ain't gon shoot you\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 3.1609079837799072, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 16, "dynamic_weight_predictions": 16, "consensus_predictions": 0, "avg_coordination_time": 17.6901987940073, "system_type": "intelligent_coordination"}, "text": "Put ya hands up if you a Grade A bitch", "true_label": 1, "processing_time": 3.1609079837799072, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.555072370916605, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.94); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.94057896733284, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.941 confidence", "processing_time": 0.06497430801391602}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and sexually inappropriate comments towards a freshman student, which is offensive and disrespectful.", "processing_time": 0.7893693447113037}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and objectifies a young woman, referring to her as a 'ho' and making inappropriate comments about her appearance.", "processing_time": 2.3381097316741943, "retrieved_examples": "Example 1: \"Saw my dog humpin this female dog the other day. Now the female is waiting outside my door for a quick hook up. My dog gets bitches!\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Freshman year I remember like it was yesterday. I was lowkey plotting on the baddest bitch. But some clown ass nigguh swooped dat up lol\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"The girl skipping school ain't nothing but 16 y/o and just made me say in my head \"bitch u supposed to be in school\" had to catch myself\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.342794418334961, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 17, "dynamic_weight_predictions": 17, "consensus_predictions": 0, "avg_coordination_time": 16.787410301320694, "system_type": "intelligent_coordination"}, "text": "Saw this little freshman ho with what looked like spiked dog collar (stupidest shit ever) so everytime I saw her I said \"so sexy\" lmao", "true_label": 1, "processing_time": 2.342794418334961, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5617406368255615, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9939250946044922, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.994 confidence", "processing_time": 0.06451106071472168}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of the term 'hoes' is derogatory and offensive towards women", "processing_time": 0.854362964630127}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is considered offensive as it uses derogatory language towards women.", "processing_time": 2.050114631652832, "retrieved_examples": "Example 1: \"RT @young_thrill3r: These hoes ain't loyal\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @StephenGoetz2: These hoes ain't loyal\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @UrbanEngIish: \"These hoes ain't loyal\" = Women of this generation cannot be trusted.\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.052769422531128, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 18, "dynamic_weight_predictions": 18, "consensus_predictions": 0, "avg_coordination_time": 15.96881914138794, "system_type": "intelligent_coordination"}, "text": "RT @collegefession: \"The only thing that <PERSON><PERSON><PERSON> has taught me is that in fact, these hoes ain't loyal\" - <PERSON><PERSON> Brook", "true_label": 1, "processing_time": 2.052769422531128, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5619975253939629, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:1.00); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9959802031517029, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.996 confidence", "processing_time": 0.06411170959472656}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and derogatory term directed at a specific group", "processing_time": 0.7929384708404541}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms.", "processing_time": 2.698094129562378, "retrieved_examples": "Example 1: \"RT @DaDonherself_: Niggas will fuck over a pretty girl w her head on right , for a ho who only got 3 pair of good panties &#128514;&#9996;&#65039; .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @shikeattike: Pussy niggas don't get no pussy nigga\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @_BredDIFFERENT: Niggas will fuck yah good , eat ya pussy then go home to they bitch like ain't shit happen &#128553;&#128530;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.7015023231506348, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 19, "dynamic_weight_predictions": 19, "consensus_predictions": 0, "avg_coordination_time": 15.270539308849134, "system_type": "intelligent_coordination"}, "text": "RT @maNIJEEtwa_: you can ride for a nigga ya whole life new pussy fuck it up in one night", "true_label": 1, "processing_time": 2.7015023231506348, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5440592418114345, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.85); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8524739344914755, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.852 confidence", "processing_time": 0.06315255165100098}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains a racial slur, which is offensive and derogatory towards a specific group", "processing_time": 0.8073322772979736}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it is a positive and non-derogatory comment about the artist <PERSON><PERSON><PERSON><PERSON><PERSON>.", "processing_time": 2.552914619445801, "retrieved_examples": "Example 1: \"<PERSON> went ape shit on tht \"Krazy\"\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I know im not the only niggah that saw that\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"&#8220;@alicia_688: He did go in i was actually surprised he when that hard RT @Vice504fly: N the crib listen 2 my nig G-Eazy&#8221;don't diss my homie\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.558417558670044, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 20, "dynamic_weight_predictions": 20, "consensus_predictions": 0, "avg_coordination_time": 14.63493322134018, "system_type": "intelligent_coordination"}, "text": "I didn't know my nig G-<PERSON><PERSON><PERSON> had reached a million views on one of his songs, haha that's crazy", "true_label": 1, "processing_time": 2.558417558670044, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5616384521126747, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9931076169013977, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.993 confidence", "processing_time": 0.056494712829589844}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of the term 'bitches' is derogatory and offensive towards women.", "processing_time": 0.9187979698181152}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it uses derogatory language towards women.", "processing_time": 2.3686916828155518, "retrieved_examples": "Example 1: \"RT @GuyCodes: Flirting with bitches on Twitter like http://t.co/UBFtGH262O\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @CauseWereGuys: Flirting with bitches on a Twitter like http://t.co/03PgoQQjYS\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @lesbolife: when some bitch flirts with bae http://t.co/vDDth2Mq1I\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.3747663497924805, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 21, "dynamic_weight_predictions": 21, "consensus_predictions": 0, "avg_coordination_time": 14.05111575126648, "system_type": "intelligent_coordination"}, "text": "RT @awkwardposts: Flirting with bitches on Twitter like http://t.co/eV2QUYsbpO", "true_label": 1, "processing_time": 2.3747663497924805, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5574765019118786, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.96); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9598120152950287, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.960 confidence", "processing_time": 0.06844592094421387}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of the term 'retarded' is derogatory and offensive towards individuals with intellectual disabilities.", "processing_time": 0.8786787986755371}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language, specifically the term 'retarded'.", "processing_time": 2.420319080352783, "retrieved_examples": "Example 1: \"Shawty pussy wet, the dope drippn wet &#128166;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"This shit is retarded &#8220;@_llVlllVlX: &#128128;&#128553;&#128557; http://t.co/se2xiDTmIF&#8221;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"&#8220;@_honeysays: My phone charging backwards &#128533;&#8221; retarded\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.4259064197540283, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 22, "dynamic_weight_predictions": 22, "consensus_predictions": 0, "avg_coordination_time": 13.52269714528864, "system_type": "intelligent_coordination"}, "text": "My flow retarded &#128526;", "true_label": 0, "processing_time": 2.4259064197540283, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5581048205494881, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.96); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9648385643959045, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.965 confidence", "processing_time": 0.06182861328125}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and profanity directed at an individual, making it offensive.", "processing_time": 0.8246312141418457}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to a person as a 'stupid bitch' and includes profanity.", "processing_time": 2.2390992641448975, "retrieved_examples": "Example 1: \"Gotta bitch with some church's chicken thighs\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Stupid bitch &#128545;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"This bitch thought the hot sauce was BBQ sauce. Who tf puts BBQ sauce in their tacos to begin with?! &#128514;&#128514;&#128514;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.2434327602386475, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 23, "dynamic_weight_predictions": 23, "consensus_predictions": 0, "avg_coordination_time": 13.032294345938642, "system_type": "intelligent_coordination"}, "text": "Stupid bitch at Church's didn't give me my fucking BBQ sauce so now I gotta eat some dry ass chicken strips &#128531;", "true_label": 1, "processing_time": 2.2434327602386475, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5616288147866726, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9930305182933807, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.993 confidence", "processing_time": 0.06280016899108887}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and derogatory language towards individuals based on their race and sexual orientation.", "processing_time": 0.8158044815063477}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms, including 'nigga' and 'faggot'. It promotes violence and discrimination based on sexual orientation.", "processing_time": 3.7501723766326904, "retrieved_examples": "Example 1: \"Any nicca gettin it n da ass is a fag! &#8220;@CruellaDeTrill: Howw? Please explain this twitter logic. I'm confused. &#128553; @Im_Yung_Jay @YuTwatching&#8221;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @realist_iLLest: &#8220;@187XO_: Before I run from a niggah I'll lose the fight&#8221;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"&#8220;@waymoflydenu Them Jew Lawyers and Doctors say \"Nigga\" ran? Lol&#8221;nope they say shit like queer though\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 3.7537970542907715, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 24, "dynamic_weight_predictions": 24, "consensus_predictions": 0, "avg_coordination_time": 12.64569029211998, "system_type": "intelligent_coordination"}, "text": "RT @SariyaD<PERSON><PERSON>e: &#8220;@JayJuice22: The nigga who got slept is a faggot if he sues just run one again and watch ya back&#8221;LOL JUST LIKE THE PE&#8230;", "true_label": 1, "processing_time": 3.7537970542907715, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.560868889093399, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9869511127471924, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.987 confidence", "processing_time": 0.058834075927734375}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and derogatory terms directed at individuals", "processing_time": 0.7655832767486572}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language with the use of 'bitch ass niggas'.", "processing_time": 2.182267904281616, "retrieved_examples": "Example 1: \"They some bitch ass niggas\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"bitch ass nigga\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Only fat bitches and flat booty hoes today wackkkkk\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.187671422958374, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 25, "dynamic_weight_predictions": 25, "consensus_predictions": 0, "avg_coordination_time": 12.227369537353516, "system_type": "intelligent_coordination"}, "text": "GTA online today bitch ass niggas", "true_label": 1, "processing_time": 2.187671422958374, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5558087415993214, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.95); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9464699327945709, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.946 confidence", "processing_time": 0.06561684608459473}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit threat and derogatory language directed at an individual", "processing_time": 0.775644063949585}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to someone as a 'bitch'.", "processing_time": 2.019489288330078, "retrieved_examples": "Example 1: \"@Gorgeous<PERSON><PERSON>ry_ fight or nah dumb bitch &#128530;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@GorgeousCamry_ bitch fck all lay wat you wanna do\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@GorgeousCamry_ @itsDeSha__ man get yo bitch ass out my mf mentions man with all tht bs u trippin\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.0257961750030518, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 26, "dynamic_weight_predictions": 26, "consensus_predictions": 0, "avg_coordination_time": 11.835001331109266, "system_type": "intelligent_coordination"}, "text": "@Gorgeous<PERSON><PERSON>ry_ ight bitch ima see u", "true_label": 1, "processing_time": 2.0257961750030518, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5473606735467911, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.88); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8788853883743286, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.879 confidence", "processing_time": 0.06479978561401367}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text contains informal language but does not contain explicit insults, threats, or derogatory content. It expresses personal feelings and concerns.", "processing_time": 1.5556066036224365}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it uses the term 'pussy' in a derogatory manner.", "processing_time": 3.399806499481201, "retrieved_examples": "Example 1: \"Im the biggest pussy when it comes to scary stuff\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Don't be a pussy.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"You scared I ain't scared to beat the pussy up\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 3.4046993255615234, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 27, "dynamic_weight_predictions": 27, "consensus_predictions": 0, "avg_coordination_time": 11.522767923496387, "system_type": "intelligent_coordination"}, "text": "Jk. I'm going to be a pussy about it. I don't want the love of my life seein me all scared and shit.", "true_label": 1, "processing_time": 3.4046993255615234, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5616502538323402, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9932020306587219, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.993 confidence", "processing_time": 0.0678248405456543}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language and insults towards individuals based on their sexual orientation.", "processing_time": 0.7726082801818848}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to individuals using the term 'fags'.", "processing_time": 2.3442318439483643, "retrieved_examples": "Example 1: \"RT @jakesiwy: @Ryan_Murphy3 http://t.co/i1rnKjMKeU fag\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @MichalaSmith4: shout out to the faggots who took a million and one selfies on my phone. &#128530;&#128514;&#10084;&#65039; @zzachbarness @michealallen72 http://t.co/&#8230;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"gay ass fags http://t.co/eqn4gvW42V\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.350175619125366, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 28, "dynamic_weight_predictions": 28, "consensus_predictions": 0, "avg_coordination_time": 11.195175341197423, "system_type": "intelligent_coordination"}, "text": "RT @mitchman<PERSON>o: @BrantPrintup:What straight guys take a picture of themselves naked in a hot tun.What fags @jakesiwy @Ryan_Murphy3 @Randy&#8230;", "true_label": 1, "processing_time": 2.350175619125366, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5362761182089646, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.79); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7902089456717173, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.790 confidence", "processing_time": 0.05916857719421387}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains a racial slur directed at an individual", "processing_time": 0.8140907287597656}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language, specifically the use of the word 'nig'.", "processing_time": 2.3631751537323, "retrieved_examples": "Example 1: \"@<PERSON><PERSON><PERSON><PERSON><PERSON> text me niggah!\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@wodaeeex3 bitch i ainn hiden i go str8 to class my teacher lame af\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Hit me up with plans nigs\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.3674960136413574, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 29, "dynamic_weight_predictions": 29, "consensus_predictions": 0, "avg_coordination_time": 10.890772605764456, "system_type": "intelligent_coordination"}, "text": "@Aram2432 I have class nig", "true_label": 1, "processing_time": 2.3674960136413574, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5620618723332882, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:1.00); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9964949786663055, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.996 confidence", "processing_time": 0.0590822696685791}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and references to violence and derogatory terms towards women.", "processing_time": 0.7877225875854492}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains references to violence and objectifies women.", "processing_time": 2.7686288356781006, "retrieved_examples": "Example 1: \"RT @VictorSmith90: @vnksmith16 be out here fighting bitches like its legal\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @xNikiaaa_: @1stBlocJeremiah damn where you on house arrest at ? &amp; yup in tht bitch like &#128080;&#128131;&#128527;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"&#8220;@salsteelo: I got 1 felony!!!&#8221; I got bitches\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.7718019485473633, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 30, "dynamic_weight_predictions": 30, "consensus_predictions": 0, "avg_coordination_time": 10.620140250523885, "system_type": "intelligent_coordination"}, "text": "RT @VictorSmith90: Fresh out of jail and I got my chopper bitches be like <PERSON><PERSON> your number 1 like the BK whopper", "true_label": 1, "processing_time": 2.7718019485473633, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5610939748585224, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9887517988681793, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.989 confidence", "processing_time": 0.06261444091796875}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of derogatory language and insults towards a specific group makes the speech offensive.", "processing_time": 0.7953531742095947}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms.", "processing_time": 2.2628839015960693, "retrieved_examples": "Example 1: \"All you pussies is losers, all my niggas is winnin\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"These niggas pure pussy I see it in they faces\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Look at them hoes, man fuck them hoes niggah lets get smoked\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.269991636276245, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 31, "dynamic_weight_predictions": 31, "consensus_predictions": 0, "avg_coordination_time": 10.350780617806219, "system_type": "intelligent_coordination"}, "text": "Look at you.. Now look at us.. All my niggas look pussy as fuck", "true_label": 1, "processing_time": 2.269991636276245, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5490631759166718, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.89); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.892505407333374, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.893 confidence", "processing_time": 0.062436580657958984}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and insults directed at an individual", "processing_time": 0.7550594806671143}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to someone as a 'bitch'.", "processing_time": 2.093808889389038, "retrieved_examples": "Example 1: \"I'm too much of a bitch to say anything about that\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"wouldn't be such a bitch if people didn't piss me off 24/7\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"If you're acting like more of a bitch than your bitch... there's a problem\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.098029613494873, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 32, "dynamic_weight_predictions": 32, "consensus_predictions": 0, "avg_coordination_time": 10.09288214892149, "system_type": "intelligent_coordination"}, "text": "I would probably still feel bad if you weren't such a bitch about it...", "true_label": 1, "processing_time": 2.098029613494873, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5378581061959267, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.80); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8028648495674133, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.803 confidence", "processing_time": 0.06036496162414551}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it describes a type of cake and its traditional ingredients without containing any insults, threats, or derogatory content.", "processing_time": 0.8676788806915283}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it describes a type of cake and its traditional icing.", "processing_time": 2.2154486179351807, "retrieved_examples": "Example 1: \"RT @FoodTabloid: Oreo cake http://t.co/iJ0idCAQMl\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"These brownies http://t.co/iyqRBn2AMf\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"@Krbfarms @Cornfrmr Homemade Angel Food, yellow cake with half-inch chocolate frosting, Devil's Food to die for, Pecan Pie\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.2180497646331787, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 33, "dynamic_weight_predictions": 33, "consensus_predictions": 0, "avg_coordination_time": 9.854250864549117, "system_type": "intelligent_coordination"}, "text": "A light, tender yellow butter cake with a classic boiled icing, traditionally made from burnt sugar http://t.co/XPQKTlTNkH", "true_label": 0, "processing_time": 2.2180497646331787, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5482435238858063, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.89); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8859481910864512, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.886 confidence", "processing_time": 0.06033158302307129}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and a threat of physical harm", "processing_time": 0.7423570156097412}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive due to the use of derogatory language and the violent intent expressed towards another individual.", "processing_time": 4.8791704177856445, "retrieved_examples": "Example 1: \"I'm bout to hop off this hoe they Tryna get on my ass\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I'm about to hit a bitch\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"This fucking little bitch landed on me in the car and really thought I wouldn't kill him.. http://t.co/nOuHxhLsHA\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 4.885899543762207, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 34, "dynamic_weight_predictions": 34, "consensus_predictions": 0, "avg_coordination_time": 9.708122884525972, "system_type": "intelligent_coordination"}, "text": "I'm about to push this niggah off my seat trying to squish me and shit", "true_label": 1, "processing_time": 4.885899543762207, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5417910255491734, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.83); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8343282043933868, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.834 confidence", "processing_time": 0.06611013412475586}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The mention of 'ZOG' (Zionist Occupied Government) is a derogatory term often associated with anti-Semitic conspiracy theories, making the speech offensive.", "processing_time": 1.0308082103729248}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it promotes conspiracy theories and uses derogatory language ('ZOG'). It implies a discriminatory view towards certain demographics.", "processing_time": 2.351067543029785, "retrieved_examples": "Example 1: \"@bnp We are being overrun by mongrels because ZOG or the Jews that run our countries want the white man to perish.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"National Review calls #gay marriage 'lawlessness' http://t.co/QpTreDa6yv #oregon #tcot #gop #NOM #lol #teabaggers\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"I am for equality in the law for all people white black red yellow and blue. <PERSON><PERSON>'s <PERSON><PERSON>'s well I draw the line... http://t.co/UGZyRtkAbw\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.357440233230591, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 35, "dynamic_weight_predictions": 35, "consensus_predictions": 0, "avg_coordination_time": 9.498103380203247, "system_type": "intelligent_coordination"}, "text": "Any solution to ZOG which doesn't deal with demographics - only laws - is no solution.\n#NorthwestImperative\nhttp://t.co/Vhy5s2apCn", "true_label": 0, "processing_time": 2.357440233230591, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5604583732783794, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.98); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9836669862270355, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.984 confidence", "processing_time": 0.06023526191711426}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit language and derogatory terms towards a woman", "processing_time": 1.3328869342803955}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to women in a derogatory manner.", "processing_time": 2.7056894302368164, "retrieved_examples": "Example 1: \"She know that im that niggah\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"She fuckin wit a young niggah\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Since a youngin bitch we been them niggas\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.711329221725464, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 36, "dynamic_weight_predictions": 36, "consensus_predictions": 0, "avg_coordination_time": 9.309581875801086, "system_type": "intelligent_coordination"}, "text": "She used to fuck wit A$AP, Too Shortsaid he know that bitch from way back", "true_label": 1, "processing_time": 2.711329221725464, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5620784610509872, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:1.00); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.996627688407898, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.997 confidence", "processing_time": 0.05927848815917969}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The tweet contains explicit insults and derogatory language towards a woman, which is offensive and inappropriate.", "processing_time": 1.1156938076019287}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language by using the derogatory term 'cunt' towards a woman, which is highly inappropriate and offensive.", "processing_time": 2.5553674697875977, "retrieved_examples": "Example 1: \"RT @SantosVictorero: RT @peddoc63 Lib Hypocrisy alert&#128680; bans \"bossy\" but \"bitch\" is okay! @AmyMek @michellemalkin @jjauthor #tcot http://t.c&#8230;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@BarneyFranken @jenny<PERSON><PERSON><PERSON> @Salon salon is is chuck full of butthurt liberal retards\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@WendyGAv says the pedophile promoting dolt. LMAO #teabagger\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.559696912765503, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 37, "dynamic_weight_predictions": 37, "consensus_predictions": 0, "avg_coordination_time": 9.1271525524758, "system_type": "intelligent_coordination"}, "text": "@twittwith<PERSON>h @cher oh, see cher? see what your particular brand vitriol creates? A boy who thinks it's ok to call a woman a cunt. #libtard", "true_label": 1, "processing_time": 2.559696912765503, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5616648383438587, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9933187067508698, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.993 confidence", "processing_time": 0.061007022857666016}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of the term 'retarded' is derogatory and offensive towards individuals with intellectual disabilities.", "processing_time": 0.8308351039886475}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to someone as 'retarded'.", "processing_time": 3.1394715309143066, "retrieved_examples": "Example 1: \"Your retarded.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"This guy is 100% retarded.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"This is fucking retarded\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 3.145482301712036, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 38, "dynamic_weight_predictions": 38, "consensus_predictions": 0, "avg_coordination_time": 8.969740177455702, "system_type": "intelligent_coordination"}, "text": "@TerrorFranchise Nope, definitely retarded.", "true_label": 1, "processing_time": 3.145482301712036, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5610956512391567, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9887652099132538, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.989 confidence", "processing_time": 0.06044435501098633}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and derogatory terms towards women", "processing_time": 0.968329668045044}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it uses derogatory language towards women.", "processing_time": 2.0489401817321777, "retrieved_examples": "Example 1: \"If I can't make no money off of you then bitch I don't even want to you.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Yall live for bitches and blunts. We live for weed and money!!!!!!\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I dont fuck wit hoes or broke bitches\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.0521554946899414, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 39, "dynamic_weight_predictions": 39, "consensus_predictions": 0, "avg_coordination_time": 8.792366211230938, "system_type": "intelligent_coordination"}, "text": "If u not talking bout money or weed bitch I don't even wanna talk to you.", "true_label": 1, "processing_time": 2.0521554946899414, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5509370379149914, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.907496303319931, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.907 confidence", "processing_time": 0.06312441825866699}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language and insults towards a specific group (hoes)", "processing_time": 0.7771339416503906}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to women in a derogatory manner.", "processing_time": 2.373976945877075, "retrieved_examples": "Example 1: \"I'm so mad cause hoes think it's funny\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"If everybody already hit, Of course a nigga can't take you seriously.. Idk why hoes be so surprised..\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"These hoes cant fuck wit you\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.378999948501587, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 40, "dynamic_weight_predictions": 40, "consensus_predictions": 0, "avg_coordination_time": 8.632032054662705, "system_type": "intelligent_coordination"}, "text": "The nerve of people though... Cant take nobody serious, i continuously laugh at you hoes.", "true_label": 1, "processing_time": 2.378999948501587, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5590155869722366, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.97); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9721246957778931, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.972 confidence", "processing_time": 0.06183910369873047}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 0.899526834487915}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is considered offensive due to the use of derogatory language towards women.", "processing_time": 2.5981051921844482, "retrieved_examples": "Example 1: \"All I want is a 100 million dollars and a bad bitch\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"all i wanted was a hundred million dollars and a bad bitch\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"All I want is a 100 million dollars and no bad bitch. I'm selfish lol\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.6038949489593506, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 41, "dynamic_weight_predictions": 41, "consensus_predictions": 0, "avg_coordination_time": 8.485004320377257, "system_type": "intelligent_coordination"}, "text": "All I need is a 100 million dollars Bc I got that bad bitch", "true_label": 1, "processing_time": 2.6038949489593506, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5590611547231674, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.97); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9724892377853394, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.972 confidence", "processing_time": 0.06952261924743652}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit offensive language and derogatory terms towards women and a specific racial group", "processing_time": 0.8362629413604736}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and objectifies women.", "processing_time": 2.411094903945923, "retrieved_examples": "Example 1: \"Fucked the shit out yo bitch earlier she said she couldn't stand straight\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Yo bitch a freak fucked ha to sleep and dat was the last time I was slept on. . .Shit I woke ha up nd fucked ha again so she aint sleep long\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"That's yo bitch..... I was all up in her guts\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.416483163833618, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 42, "dynamic_weight_predictions": 42, "consensus_predictions": 0, "avg_coordination_time": 8.340515721411933, "system_type": "intelligent_coordination"}, "text": "Fuccn yo bitch last night she say \"you the nigga!!'", "true_label": 1, "processing_time": 2.416483163833618, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5561677068471909, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.95); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9493416547775269, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.949 confidence", "processing_time": 0.06267595291137695}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and derogatory terms towards women and a specific racial group", "processing_time": 0.8046875}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it uses derogatory language and promotes negative attitudes towards women and relationships.", "processing_time": 3.125974178314209, "retrieved_examples": "Example 1: \"Alotta people say they loyal to they niggas and bitches .... Yall aint shiiittt . Someone throw a few dollas in ya face you'll be snitchn &#128175;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @BlazeTheGreat_: I hate when niggas get mad cuz you fucked they girl when really he should be happy you showed him that bitch unloyal\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Don't make me take yo bitch nigga &#128527;&#128080;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 3.132091522216797, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 43, "dynamic_weight_predictions": 43, "consensus_predictions": 0, "avg_coordination_time": 8.219389577244604, "system_type": "intelligent_coordination"}, "text": "' If you get yo girl took don't be mad at the niggah who did it &#1041204; Be mad at her , Cause he don't owe you loyalty , she does .", "true_label": 1, "processing_time": 3.132091522216797, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5610519796609879, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9884158372879028, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.988 confidence", "processing_time": 0.0619206428527832}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit derogatory terms and insults directed at an individual, making it offensive.", "processing_time": 0.8310871124267578}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms.", "processing_time": 2.400308132171631, "retrieved_examples": "Example 1: \"@AustinG1135 go to sleep faggot!\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@JustWordsx dont favorite me you faggot\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@austin_farrar your a fucking queer faggot bitch\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.4047839641571045, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 44, "dynamic_weight_predictions": 44, "consensus_predictions": 0, "avg_coordination_time": 8.087239449674433, "system_type": "intelligent_coordination"}, "text": "@AustinG1135 I do not like talking to you faggot and I did but in a nicely way fag", "true_label": 1, "processing_time": 2.4047839641571045, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5517843216657639, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9142745733261108, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.914 confidence", "processing_time": 0.05738639831542969}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language towards women, deeming them as 'bitches'", "processing_time": 0.8838498592376709}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to women as 'bitches'. It also implies a negative attitude towards pregnancy.", "processing_time": 2.3114664554595947, "retrieved_examples": "Example 1: \"Bitches be trying to squash all beef wen they find out they pregnant! Some hoes dont give a fuck!\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"This bitch pregnant again &#128064;&#128514;&#128514;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I'll have these bitches havin baby's every week\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.314570188522339, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 45, "dynamic_weight_predictions": 45, "consensus_predictions": 0, "avg_coordination_time": 7.95895791053772, "system_type": "intelligent_coordination"}, "text": "pregnant at the same time and bitches think that's cute", "true_label": 1, "processing_time": 2.314570188522339, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5609904192388058, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9879233539104462, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.988 confidence", "processing_time": 0.06155538558959961}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and derogatory terms towards individuals", "processing_time": 0.7938318252563477}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech referring to women as 'hoes'.", "processing_time": 2.3249292373657227, "retrieved_examples": "Example 1: \"@<PERSON><PERSON><PERSON><PERSON>hh @CapoDaAssHole lol cause these hoes love me\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@CapoDaAssHole @Alejandro_Bihhh damn that hoe just came out but look at how many hoes said his shit jamming on my shit\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@I_GotOne he had the hoes too\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.3292477130889893, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 46, "dynamic_weight_predictions": 46, "consensus_predictions": 0, "avg_coordination_time": 7.836572906245356, "system_type": "intelligent_coordination"}, "text": "@CapoDaAssHole @Alejandro_Bihhh lmaooo I wanted them hoes she was suppose to hold em", "true_label": 1, "processing_time": 2.3292477130889893, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5616915188729763, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9935321509838104, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.994 confidence", "processing_time": 0.0662074089050293}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit reference to violence and terrorism, with a photo of a severed head, promoting hatred and threats", "processing_time": 0.8861954212188721}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it describes a British rapper turned ISIS Jihadi posing with a severed head, which is a graphic and violent image.", "processing_time": 2.6346282958984375, "retrieved_examples": "Example 1: \"HOT ISIS SOCIAL MEDIA PHOTO: Laughing Jihadis Get Baby To Kick Severed Head &#8211; ***warning, graphic*** http://t.co/P4MiSIV5A1 #tcot #pjnet\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"HOT ISIS SOCIAL MEDIA PHOTO: Laughing Jihadis Get Baby To Kick Severed Head &#8211; ***warning, graphic*** http://t.co/7Zr8daxNg4 #tcot #pjnet\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"HOT #ISIS SOCIAL MEDIA PHOTO: Laughing Jihadis Get Baby To Kick Severed Head &#8211; ***warning, graphic*** http://t.co/XdpSzwj6sK #lnyhbt #tgdn\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.6399145126342773, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 47, "dynamic_weight_predictions": 47, "consensus_predictions": 0, "avg_coordination_time": 7.72600570638129, "system_type": "intelligent_coordination"}, "text": "&#8216;<PERSON><PERSON>&#8217; With <PERSON> Homie Or What&#8217;s Left Of Him&#8217;: British Rapper Turned ISIS Jihadi Poses With Severed Head http://t.co/L9vMdNOXPg #tcot", "true_label": 0, "processing_time": 2.6399145126342773, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.542760506272316, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.84); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8420840501785278, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.842 confidence", "processing_time": 0.060837507247924805}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of a derogatory term makes the speech offensive and inappropriate.", "processing_time": 0.7840416431427002}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language, specifically the use of the term 'niggah'.", "processing_time": 2.0546207427978516, "retrieved_examples": "Example 1: \"Emoji ass nigguh\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@_Marzzz nigger\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@nightpurrowler niggah das gay\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.0595591068267822, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 48, "dynamic_weight_predictions": 48, "consensus_predictions": 0, "avg_coordination_time": 7.607954735557239, "system_type": "intelligent_coordination"}, "text": "@slings_x_arrows Equality niggah", "true_label": 1, "processing_time": 2.060094118118286, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5398782268166542, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.82); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8190258145332336, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.819 confidence", "processing_time": 0.05971550941467285}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of a racial slur 'nig nog' is derogatory and offensive towards individuals of African descent.", "processing_time": 0.8237826824188232}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language commonly used as a racial slur.", "processing_time": 1.9683096408843994, "retrieved_examples": "Example 1: \"@joshkline1992 that's my nig nog\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@Dre_Day200 bitch\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@DutchBrosPlease you're welcome, I miss you too nig nog\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 1.9716014862060547, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 49, "dynamic_weight_predictions": 49, "consensus_predictions": 0, "avg_coordination_time": 7.492927118223541, "system_type": "intelligent_coordination"}, "text": "@Dre_Day200 nig nog", "true_label": 1, "processing_time": 1.9716014862060547, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5577127660314243, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.96); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9617021282513937, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.962 confidence", "processing_time": 0.06403303146362305}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit and derogatory language towards an individual", "processing_time": 0.9977326393127441}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech suggesting a sexual act as a stress reliever.", "processing_time": 2.0392391681671143, "retrieved_examples": "Example 1: \"@felicity182 i think u need a massage or a pussy in your mouth to make u feel less stressed out don't u think?\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @LiveAndUncut: Good pussy takes your mind off the struggles and stresses of everyday life\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Eating pussy is fun\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.0460407733917236, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 50, "dynamic_weight_predictions": 50, "consensus_predictions": 0, "avg_coordination_time": 7.383989391326904, "system_type": "intelligent_coordination"}, "text": "@felicity182 a pussy in ur mouth is a really good stress reliever doctors recommend", "true_label": 1, "processing_time": 2.0460407733917236, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5416936328013737, "reasoning": "激进优化协调决策: deberta(原权重:0.35→优化权重:0.12, 预测:0, 置信度:0.83); llm_single(原权重:0.35→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.30→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8335490624109904, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.834 confidence", "processing_time": 0.059639930725097656}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language towards women", "processing_time": 0.818056583404541}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to women as 'bitches'.", "processing_time": 2.238778829574585, "retrieved_examples": "Example 1: \"These bitches love me. These niggas copy\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"all the bitches love me http://t.co/vDnuFGJ37p\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I like bitches that like bitches\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.35, "llm_single": 0.35, "llm_retrieval": 0.3}, "coordination_time": 2.24363374710083, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 51, "dynamic_weight_predictions": 51, "consensus_predictions": 0, "avg_coordination_time": 7.2831981041852165, "system_type": "intelligent_coordination"}, "text": "Them pretty bitches love me them bitches love me #GUCCCCCI", "true_label": 1, "processing_time": 2.244148015975952, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5608224496245384, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9865795969963074, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.987 confidence", "processing_time": 0.06489872932434082}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insult and derogatory term directed at an individual", "processing_time": 0.820852518081665}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and aggressive behavior towards the person mentioned.", "processing_time": 2.361427068710327, "retrieved_examples": "Example 1: \"&#8220;@Kxng_Jase: <PERSON><PERSON><PERSON>, I talk shit on twitter and so that bitch blocked me&#8221;@bremarie17\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@QuantRyse @HairyPotterZ @Hockeypuck55 if I get blocked I'll be on that bitches porch drunk and yelling. Holy shit.she was right..I am kanye\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@oJvrdy i have that bitch bloked lmao\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.364560842514038, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 52, "dynamic_weight_predictions": 52, "consensus_predictions": 0, "avg_coordination_time": 7.188608926076156, "system_type": "intelligent_coordination"}, "text": "RT @ShadowBeatz_Inc: I know you have me blocked, but next week bitch @KYR_SP33DY", "true_label": 1, "processing_time": 2.364560842514038, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.550522396961848, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9041791756947836, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.904 confidence", "processing_time": 0.06685328483581543}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit derogatory language and insults towards an individual", "processing_time": 0.7243673801422119}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to a person in a derogatory manner.", "processing_time": 2.4016177654266357, "retrieved_examples": "Example 1: \"I hate a stuck up bitch. <PERSON><PERSON><PERSON> stfu. You ain't shit special.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Can't stand a sob ass bitch\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I hate bitches that have attitudes all the time\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.4071075916290283, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 53, "dynamic_weight_predictions": 53, "consensus_predictions": 0, "avg_coordination_time": 7.098391919765833, "system_type": "intelligent_coordination"}, "text": "I can't stand a snobby stuck up bitch", "true_label": 1, "processing_time": 2.4071075916290283, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5485770776867867, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.89); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8886166214942932, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.889 confidence", "processing_time": 0.06784749031066895}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insult ('bitch') directed at an individual", "processing_time": 0.7893400192260742}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to someone as 'bitch'.", "processing_time": 2.022143840789795, "retrieved_examples": "Example 1: \"@chase_wickett @LangKeaton bitch im waitin on my chicken nuggets\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @scott1023: \"I said thin mints bitch\". http://t.co/yRVQjxeyqO\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @uMadddt: #FixItJesus &#128588; \n&#8220;@MyAssholePops: This bitch is the nugget filling at McDonald's http://t.co/QBdbMsHjG7&#8221;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.0292465686798096, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 54, "dynamic_weight_predictions": 54, "consensus_predictions": 0, "avg_coordination_time": 7.004518857708684, "system_type": "intelligent_coordination"}, "text": "RT @MIKEB_IN_THE_D: @_thomkins_ I got 11 nuggets this time bitch. #CouldntBeHappier", "true_label": 1, "processing_time": 2.0292465686798096, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5571561853090923, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.96); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9572494824727377, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.957 confidence", "processing_time": 0.060924530029296875}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of derogatory language ('bitches') directed at a specific group (women) is offensive and disrespectful.", "processing_time": 0.8795642852783203}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to women in a derogatory manner.", "processing_time": 2.6571125984191895, "retrieved_examples": "Example 1: \"I hate dumb bitches\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I hate bitches. Thirsty ass bitches\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I hate hoes\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.663339853286743, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 55, "dynamic_weight_predictions": 55, "consensus_predictions": 0, "avg_coordination_time": 6.925588330355557, "system_type": "intelligent_coordination"}, "text": "I actually just hate the bitches on here", "true_label": 1, "processing_time": 2.663339853286743, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5508669478197892, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.906935582558314, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.907 confidence", "processing_time": 0.06310200691223145}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Expresses frustration over a missed haircut appointment and desire to go out, but does not contain explicit insults, threats, or derogatory content", "processing_time": 0.8559360504150391}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it refers to women in a derogatory manner.", "processing_time": 2.1778249740600586, "retrieved_examples": "Example 1: \"Gotta go get a haircut ASAP tomorrow ain't no bitches looking at me right now. And today they all was out. The frustration.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Now niccas won't haircuts\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"That bitch cur my hair way to short\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.184281587600708, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 56, "dynamic_weight_predictions": 56, "consensus_predictions": 0, "avg_coordination_time": 6.840922138520649, "system_type": "intelligent_coordination"}, "text": "My barber not bein able to cut my hair yesterday really prevented me from goin to the club and gettin mad bitches b he owe me 3 free cuts", "true_label": 1, "processing_time": 2.184281587600708, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5539881512522697, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.931905210018158, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.932 confidence", "processing_time": 0.06736993789672852}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and derogatory terms towards women", "processing_time": 0.8104574680328369}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and objectifies women.", "processing_time": 2.014007091522217, "retrieved_examples": "Example 1: \"RT @Mryungone: &#8220;@viaNAWF I got some lightskin pussy one time and the bitch damn near had me bout to propose.&#8221; had some I had to immediately&#8230;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Got a red ass bitch wid a red ass pussayyy\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"This lil Thot bitch really tried to run game on me\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.018798828125, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 57, "dynamic_weight_predictions": 57, "consensus_predictions": 0, "avg_coordination_time": 6.756323483952305, "system_type": "intelligent_coordination"}, "text": "I got some lightskin pussy one time and the bitch damn near had me bout to propose.", "true_label": 1, "processing_time": 2.018798828125, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.551843199878931, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.91); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9147455990314484, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.915 confidence", "processing_time": 0.06589460372924805}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insult directed at an individual", "processing_time": 0.7114055156707764}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it uses the term 'bitch' in a derogatory manner.", "processing_time": 2.133589029312134, "retrieved_examples": "Example 1: \"&#8220;@joytooawesome: @im_jessicaaaaa @vintage_monroe_ haha bitch &#128523;&#128518;&#8221; http://t.co/n5uuCDTDxT\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @im_jessicaaaaa: @joytooawesome @vintage_monroe_ eat my pussy&#128539;&#128570;&#128166;&#128166;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @spiffytwiley: @vintage_monroe_ bitch you gorgeous\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.1366395950317383, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 58, "dynamic_weight_predictions": 58, "consensus_predictions": 0, "avg_coordination_time": 6.676673761729536, "system_type": "intelligent_coordination"}, "text": "RT @joytooawesome: @im_jessicaaaaa @vintage_monroe_ bitch no I'm talking to her !", "true_label": 1, "processing_time": 2.1366395950317383, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5533193051815033, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9265544414520264, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.927 confidence", "processing_time": 0.06271529197692871}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and derogatory terms towards women", "processing_time": 0.7516360282897949}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it refers to women in a derogatory and disrespectful manner.", "processing_time": 2.6625325679779053, "retrieved_examples": "Example 1: \"Who doesn't want a bad bitch?\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Sluts and hoes are needed.Respect their services.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"&#8220;@MarieHippy: Bitch niggas pay for hoes just to lay with hoes&#8221; relax one night you'll pay to stay with hoes\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.6681320667266846, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 59, "dynamic_weight_predictions": 59, "consensus_predictions": 0, "avg_coordination_time": 6.608732377068471, "system_type": "intelligent_coordination"}, "text": "@Buy_Mortimer @WiigJosh @iowa51005 @TC_live2014 @todd_calfee Hookers are always complimentary-who wants to be with a bitch?", "true_label": 1, "processing_time": 2.6681320667266846, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5471837967634201, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.88); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8774703741073608, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.877 confidence", "processing_time": 0.05955982208251953}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains an explicit insult directed at an individual", "processing_time": 0.7679355144500732}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it uses the term 'greaser' in a neutral or positive context.", "processing_time": 2.8004953861236572, "retrieved_examples": "Example 1: \"@FunnyPicsDepot he's a greaser\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"@Steven_ratata bye youre a queer\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@justinbieber nice wigger stache\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.8048255443573, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 60, "dynamic_weight_predictions": 60, "consensus_predictions": 0, "avg_coordination_time": 6.545333929856619, "system_type": "intelligent_coordination"}, "text": "@EricBaetsle your a greaser", "true_label": 0, "processing_time": 2.8048255443573, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5417911373078823, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.83); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8343290984630585, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.834 confidence", "processing_time": 0.06115913391113281}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Expresses opinion about Chick-fil-A's stance on First Amendment rights, does not contain explicit insults, threats, or hatred", "processing_time": 1.0802180767059326}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by using derogatory language and targeting a specific group of people based on their sexual orientation.", "processing_time": 3.055899143218994, "retrieved_examples": "Example 1: \"We bout to be the next Chik Fil A, l<PERSON>o. <PERSON><PERSON><PERSON> of fags said we told them if they didn't like their food to get out.\nNever happened &#128514;&#128514;&#128514;&#128514;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@kitlange stop oppressing me! Have you heard of free speech? Stop shoving your lifestyle down my throat! 1st AMENDMENT! #tcot #teabaggers\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"What America teaches people: if you bitch about something enough, you'll get what you want.\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 3.061188220977783, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 61, "dynamic_weight_predictions": 61, "consensus_predictions": 0, "avg_coordination_time": 6.4882167870881124, "system_type": "intelligent_coordination"}, "text": "Chick fil a wasn't about anti gay. It was about the first ammendment. If you didn't get that then you have no right to bitch about rights.", "true_label": 1, "processing_time": 3.061188220977783, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5518155135214329, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.914524108171463, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.915 confidence", "processing_time": 0.05856037139892578}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of a derogatory term towards an individual is offensive and inappropriate.", "processing_time": 0.818169116973877}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to an individual using a derogatory term.", "processing_time": 2.312300682067871, "retrieved_examples": "Example 1: \"@<PERSON><PERSON><PERSON><PERSON>_ pussy ass nigga.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I really don't like that nigguh Paul tho\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"My nigguh\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.3171138763427734, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 62, "dynamic_weight_predictions": 62, "consensus_predictions": 0, "avg_coordination_time": 6.420940933688994, "system_type": "intelligent_coordination"}, "text": "RT @FAAMMoverALL: This nigguh <PERSON>", "true_label": 1, "processing_time": 2.3171138763427734, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5535787815848987, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9286302526791891, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.929 confidence", "processing_time": 0.057543039321899414}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains sexually explicit and derogatory language", "processing_time": 0.8455507755279541}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech referring to sexual activity.", "processing_time": 2.16302490234375, "retrieved_examples": "Example 1: \"I ain't got no pussy or weed, I'm on that bitch suck my dick diet\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Eattin all this pussy im startin to get chubby\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I eats the pussy.\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.169328451156616, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 63, "dynamic_weight_predictions": 63, "consensus_predictions": 0, "avg_coordination_time": 6.353455021267846, "system_type": "intelligent_coordination"}, "text": "I was gunna eat the pussy but I'm on a diet !", "true_label": 1, "processing_time": 2.169328451156616, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5614210404455662, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9913683235645294, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.991 confidence", "processing_time": 0.060296058654785156}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language and insults towards an individual", "processing_time": 0.7570066452026367}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it refers to someone as a 'hoe' and accuses them of 'catfishin'.", "processing_time": 2.704906702041626, "retrieved_examples": "Example 1: \"Oh no tht hoe ode fake !!!\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @YaBoyKip: \"I hope this bitch ain't a catfish\" http://t.co/56JKqioEPE\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"These bitches really be out here on backpage hoe-ing &#128557;&#128557;&#128557; watch yo bitch she could be on there !\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.710409164428711, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 64, "dynamic_weight_predictions": 64, "consensus_predictions": 0, "avg_coordination_time": 6.296532429754734, "system_type": "intelligent_coordination"}, "text": "I know this one hoe on twitter right now that's catfishin shit !!!!!", "true_label": 1, "processing_time": 2.710409164428711, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5467222929000854, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.87); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8737783432006836, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.874 confidence", "processing_time": 0.06152939796447754}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit language and derogatory content towards women", "processing_time": 0.739588737487793}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and promotes infidelity and disrespect towards women.", "processing_time": 2.6668386459350586, "retrieved_examples": "Example 1: \"I cheated on 10% of the bitches I had tf lol nobody even wanted to be all committed and shit\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"You better watch your hoe she fuckin everybody\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Never trust a bitch cause these bitches fuckin' anyone. Say you know a faithful bitch where she at? I Gotta get me one\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.671736717224121, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 65, "dynamic_weight_predictions": 65, "consensus_predictions": 0, "avg_coordination_time": 6.240766341869648, "system_type": "intelligent_coordination"}, "text": "A man will cheat on his woman.. Fuck 20 different bitches in 1 year.", "true_label": 1, "processing_time": 2.671736717224121, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5481391288340092, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.89); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8851130306720734, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.885 confidence", "processing_time": 0.06617856025695801}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language towards a woman", "processing_time": 0.784759521484375}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech referring to someone as a 'hoe'.", "processing_time": 2.699549436569214, "retrieved_examples": "Example 1: \"RT @sofancyy_: A hoe gone be a hoe &#128564;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@<PERSON><PERSON>y_J happy birthday bitch&#10084;&#65039;&#127881;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @_That<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: Yo hoe will get slayed &#128514;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.7046656608581543, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 66, "dynamic_weight_predictions": 66, "consensus_predictions": 0, "avg_coordination_time": 6.187189058824019, "system_type": "intelligent_coordination"}, "text": "We know what this hoe gonna do for her bday now cc:@TheShugAvery RT @OMGitsKariC: @moyizzy I plan on it &#128520;", "true_label": 1, "processing_time": 2.7046656608581543, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5474203247576952, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.88); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8793625980615616, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.879 confidence", "processing_time": 0.06728863716125488}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Derogatory and objectifying language towards women based on their physical appearance", "processing_time": 0.7885487079620361}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it expresses a personal preference for women with certain physical attributes.", "processing_time": 3.1419670581817627, "retrieved_examples": "Example 1: \"RT @davegetnmoney: We don't want no skinny bitches get out! WE WANT BIG BOOTY BITCHES!\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @TweetBelow_: The person below is into big booty bitches&#128525;&#127825;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @wishfulll: Big booty bitches with... \"@StagBeChillin: Ass RT @iDocLyfe: Tits\"\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 3.146745204925537, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 67, "dynamic_weight_predictions": 67, "consensus_predictions": 0, "avg_coordination_time": 6.141809299810609, "system_type": "intelligent_coordination"}, "text": "RT @MosoKnowso: RT @TroyAve I got no interest in tall skinny model bitches! I like my women with some ass &amp; hips ...", "true_label": 1, "processing_time": 3.146745204925537, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5340072959661484, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.77); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.53)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.772058367729187, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.772 confidence", "processing_time": 0.06542253494262695}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and derogatory remarks towards the individual, using the term 'bitch out'.", "processing_time": 0.8955457210540771}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it encourages taking action and not backing out, without using derogatory language.", "processing_time": 2.736600637435913, "retrieved_examples": "Example 1: \"@iiTweet_YuLurk &#128514;&#128514;&#128128;&#128128;&#128128;&#128128;bitch you outta line\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@Myalynn__x3 mhm bitch don't play with your life &#128299;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"At least ima a faithful bitch &#128524;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.7425055503845215, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 68, "dynamic_weight_predictions": 68, "consensus_predictions": 0, "avg_coordination_time": 6.091819538789637, "system_type": "intelligent_coordination"}, "text": "@_<PERSON><PERSON>_baby_ well at least If you're going to do somthing you do it.. And don't bitch out.. Even when it's the wrong thing &#128527;", "true_label": 1, "processing_time": 2.7425055503845215, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5593516640365124, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.97); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.974813312292099, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.975 confidence", "processing_time": 0.06345033645629883}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit racial slur and derogatory language towards a specific group, violating community standards.", "processing_time": 0.8728764057159424}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech with racial slurs and dehumanizing language towards black individuals.", "processing_time": 2.503619432449341, "retrieved_examples": "Example 1: \"Black people like @<PERSON><PERSON><PERSON><PERSON> disgust me laughing at someone who died dumb monkey ass niggas like you need to be locked up just for stupidity\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@drboy<PERSON>watkins1 Coons and monkeys like you disgust me, you minstrel show negro. Crying about shit all the time you're a disgrace everywhere\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@AtBlackface @MrMooncricket @UncleUnco @BobbyBotsods @FuckTheMallcops @RacistNegro69 ape? Fuck u broke ass racist\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.5082197189331055, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 69, "dynamic_weight_predictions": 69, "consensus_predictions": 0, "avg_coordination_time": 6.0398833095163535, "system_type": "intelligent_coordination"}, "text": "@DecodnLyfe @LupeFiasco @Larellj another black man? What does that have to do with anything? Once a monkey, always a monkey, Chicago idiot", "true_label": 1, "processing_time": 2.5082197189331055, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5406106933951378, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.82); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8248855471611023, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.825 confidence", "processing_time": 0.06989622116088867}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and racial stereotyping towards white individuals", "processing_time": 1.011949062347412}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by using the term 'whitey' in a derogatory manner.", "processing_time": 3.058056354522705, "retrieved_examples": "Example 1: \"\"poor whitey\" http://t.co/3UkKeyznz8\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@infidelpamelaLC I'm going to blame the black man, since they always blame \"whitey\" I'm an equal opportunity hater.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @NYTMinusContext: kill whitey\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 3.063603162765503, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 70, "dynamic_weight_predictions": 70, "consensus_predictions": 0, "avg_coordination_time": 5.997365021705628, "system_type": "intelligent_coordination"}, "text": "@clinchmtn316 @sixonesixband AMERICA today, the rule of thumb is: when in doubt, blame \"whitey\"", "true_label": 1, "processing_time": 3.063603162765503, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5597828775644302, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.98); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9782630205154419, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.978 confidence", "processing_time": 0.059140920639038086}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it simply reports on a decision made by the city of Chicago regarding red light tickets.", "processing_time": 1.0471606254577637}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it discusses a decision related to red light tickets and refunds, without containing any offensive language or intent.", "processing_time": 2.6988160610198975, "retrieved_examples": "Example 1: \"There's a special place in hell for people who slam on their brakes for yellow lights &#128532;&#128299;\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"People who slam on the brakes at yellow lights should not be allowed to drive.\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"RT @Duke27_: Mistakes are showing up late for work, running red lights, or even forgetting 2 take the trash out. This here ain't 1 http://t&#8230;\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.7023706436157227, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 71, "dynamic_weight_predictions": 71, "consensus_predictions": 0, "avg_coordination_time": 5.950956650183234, "system_type": "intelligent_coordination"}, "text": "RT @chicagotribune: Chicago will not refund $7.7 million made from red light tickets issued after yellow lights were shortened http://t.co/&#8230;", "true_label": 0, "processing_time": 2.7023706436157227, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5613735690712929, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.990988552570343, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.991 confidence", "processing_time": 0.06278252601623535}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit and derogatory language directed at an individual", "processing_time": 0.7465062141418457}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to someone's body in a derogatory manner.", "processing_time": 2.161611557006836, "retrieved_examples": "Example 1: \"@Huntermoore so does my juicy pussy\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@Huntermoore my pussy is aching for ur follow bae\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@Huntermoore finger my throbbing pussy\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.1664257049560547, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 72, "dynamic_weight_predictions": 72, "consensus_predictions": 0, "avg_coordination_time": 5.8983937203884125, "system_type": "intelligent_coordination"}, "text": "@Huntermoore @shelbyritner shelby your pussy is beat up", "true_label": 1, "processing_time": 2.1664257049560547, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5593849532306194, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.98); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9750796258449554, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.975 confidence", "processing_time": 0.07220673561096191}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and offensive language towards women", "processing_time": 0.769110918045044}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it compares pound cake to a vulgar term, 'pussy'.", "processing_time": 1.6800081729888916, "retrieved_examples": "Example 1: \"&#128518;\"@QbF150: On a leash &#128555;RT @80sbaby4life: Just pussy &#128553;\"\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"&#127829;&#128049; RT @TAN1AAA: RT @PeachDulce: A piece of pussy http://t.co/BcWiT2nuz5\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @O3_Millz: Black pussy remind me of roast beef, white pussy remind me of cheesecake and Mixed girl pussy remind me of Apple Pie.. &#128175;&#128175;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 1.6859755516052246, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 73, "dynamic_weight_predictions": 73, "consensus_predictions": 0, "avg_coordination_time": 5.840689361911931, "system_type": "intelligent_coordination"}, "text": "\"Lmao @BlackBeauty515: &#128514;&#128514; RT @80sbaby4life: Pound cake look like pussy &#128514; http://t.co/UFYwCC9xCI\"", "true_label": 1, "processing_time": 1.6859755516052246, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5618367418646812, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.99469393491745, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.995 confidence", "processing_time": 0.06927299499511719}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insult directed at an individual", "processing_time": 0.8997156620025635}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to someone as a 'bitch'.", "processing_time": 2.1145823001861572, "retrieved_examples": "Example 1: \"@Mijo_LGI you're such a bitch\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@Ricky___m you bitch -.-\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@__angieex3 Ik you were pussy\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.119926929473877, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 74, "dynamic_weight_predictions": 74, "consensus_predictions": 0, "avg_coordination_time": 5.790408788500606, "system_type": "intelligent_coordination"}, "text": "@__<PERSON><PERSON><PERSON><PERSON> you a real bitch for that", "true_label": 1, "processing_time": 2.119926929473877, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5528473258018494, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.92); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9227786064147949, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.923 confidence", "processing_time": 0.06130504608154297}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains a derogatory term ('coon') directed at an individual, making it offensive.", "processing_time": 0.7781248092651367}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains a racial slur ('coon'), which is offensive.", "processing_time": 123.27691602706909, "retrieved_examples": "Example 1: \"@MilesM_05 shut up coon.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Everyone tells me the exact same thing but I won't listen, coon\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"coons\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 123.28361105918884, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 75, "dynamic_weight_predictions": 75, "consensus_predictions": 0, "avg_coordination_time": 7.356984818776448, "system_type": "intelligent_coordination"}, "text": "@STACCS_WNT_FOLD you tell me, coon.", "true_label": 1, "processing_time": 123.28361105918884, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5564281915624936, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.95); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9514255324999491, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.951 confidence", "processing_time": 0.06702542304992676}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and self-deprecation, referring to oneself in a derogatory manner", "processing_time": 1.0316848754882812}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to oneself as a 'little bitch'.", "processing_time": 2.790759325027466, "retrieved_examples": "Example 1: \"Ugh I'm such a little bitch &#128514;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"&#8220;@<PERSON>_<PERSON><PERSON><PERSON>: I can't stop being a lil bitch&#8221;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"what a little bitch lol\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.794433832168579, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 76, "dynamic_weight_predictions": 76, "consensus_predictions": 0, "avg_coordination_time": 7.296951253163187, "system_type": "intelligent_coordination"}, "text": "RT @oliviadesanfoot: this is very annoying to me im gonna vomit out of annoyance bc i am a little bitch", "true_label": 1, "processing_time": 2.794433832168579, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5613232813775539, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9905862510204315, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.991 confidence", "processing_time": 0.0603795051574707}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and derogatory language towards a specific individual, indicating offensive speech.", "processing_time": 0.8963005542755127}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to a woman as a 'bitch' in a derogatory manner.", "processing_time": 2.8113138675689697, "retrieved_examples": "Example 1: \"The bitch got some nerve\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Ha dat bitch in jail\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Lmfao bitch got her shit rocked\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.81683611869812, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 77, "dynamic_weight_predictions": 77, "consensus_predictions": 0, "avg_coordination_time": 7.238767939728576, "system_type": "intelligent_coordination"}, "text": "She got arrested for domestic violence against him!! Ugh bitch -_-", "true_label": 1, "processing_time": 2.81683611869812, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5593665912747383, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.97); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9749327301979065, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.975 confidence", "processing_time": 0.06203627586364746}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and derogatory language towards an individual based on their weight and gender", "processing_time": 0.7866594791412354}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to a woman in a derogatory manner based on her weight and behavior. It also includes a disrespectful hashtag.", "processing_time": 2.3244435787200928, "retrieved_examples": "Example 1: \"This biatch just called me a bitch &#128514;&#128514;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"So some bitch flipped me off for letting her infront of mee... Okay.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"The bitch got some nerve\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.329955816268921, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 78, "dynamic_weight_predictions": 78, "consensus_predictions": 0, "avg_coordination_time": 7.175834450966272, "system_type": "intelligent_coordination"}, "text": "This fat bitch got all huffy cause she took too long to make her turn, so I took it, so she mouthed off to me&#128514;&#128075;&#128075; #girlbye&#128129;", "true_label": 1, "processing_time": 2.329955816268921, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5605606995522976, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.98); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9844855964183807, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.984 confidence", "processing_time": 0.06467747688293457}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit description of violence and harm towards a specific group (hobbits)", "processing_time": 0.7801423072814941}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech contains violent and graphic content, describing the act of slitting someone's throat and vomiting in a disturbing manner.", "processing_time": 2.6302711963653564, "retrieved_examples": "Example 1: \"Then I went inside, in order to hide from mutant bird\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"some kid in an angry birds costume bumped into me and I fell over making something else fall over into something else which wobbled and..\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"\"I'll slit your throat-neck\" http://t.co/ChFFSDeDdd\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.633601427078247, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 79, "dynamic_weight_predictions": 79, "consensus_predictions": 0, "avg_coordination_time": 7.118337830410728, "system_type": "intelligent_coordination"}, "text": "I traveled back in time to Middle-earth and crept up on my enemy. I slit the hobbit's throat and by reflex vomited into my trembling hand.", "true_label": 0, "processing_time": 2.633601427078247, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5613337233662605, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9906697869300842, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.991 confidence", "processing_time": 0.06344723701477051}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and derogatory term directed at an individual", "processing_time": 0.8801064491271973}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech as it does not include derogatory language or explicit content.", "processing_time": 3.0721938610076904, "retrieved_examples": "Example 1: \"I'm pussy whipping these hoes\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"got my bitch in her whip coming to pick me up\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Yo bitch gaggin she jet laggin\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 3.076786756515503, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 80, "dynamic_weight_predictions": 80, "consensus_predictions": 0, "avg_coordination_time": 7.067818441987038, "system_type": "intelligent_coordination"}, "text": "yo bitch whippin in the early morning", "true_label": 1, "processing_time": 3.076786756515503, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5567110851407051, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.95); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9536886811256409, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.954 confidence", "processing_time": 0.05988669395446777}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and derogatory language directed at an individual", "processing_time": 0.8180649280548096}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms like 'dumb bitch' and 'fucking bitch'.", "processing_time": 2.372978448867798, "retrieved_examples": "Example 1: \"@<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bitch shut the fuck up goddam your a slut bitch whore nigga\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@wodaeeex3 dumb ass hoe\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@wodaeeex3 yeah right hoe\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.****************, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 81, "dynamic_weight_predictions": 81, "consensus_predictions": 0, "avg_coordination_time": 7.**************, "system_type": "intelligent_coordination"}, "text": "@<PERSON><PERSON><PERSON>by_xoxo ya but you can't because its a real account dumb bitch I go to wams fucking bitch", "true_label": 1, "processing_time": 2.****************, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.****************, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.****************, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.985 confidence", "processing_time": 0.*****************}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it simply refers to upgrading the music in a car.", "processing_time": 0.8461952209472656}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it talks about upgrading the interior of a car with music, without any offensive language or derogatory remarks.", "processing_time": 2.636723279953003, "retrieved_examples": "Example 1: \"One man's trash is another's Transformer &#8594; http://t.co/lpYNVqWXXx\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"<PERSON><PERSON><PERSON><PERSON> tryna hate on my whip acting like they got cooler interior/light up speakers hell nah bitch boys http://t.co/trbopWKAug\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @King____J: One nigga from the past told me one thing I'd never forget \"one mans trash is another mans treasure\"\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.641777515411377, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 82, "dynamic_weight_predictions": 82, "consensus_predictions": 0, "avg_coordination_time": 6.956662262358317, "system_type": "intelligent_coordination"}, "text": "RT @<PERSON>_<PERSON>_R: Remember all the trash on the floor of your car? We upgraded it &amp; put Wale's The Gifted so it feels like your old car htt&#8230;", "true_label": 0, "processing_time": 2.641777515411377, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5479410936435063, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.88); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8835287491480509, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.884 confidence", "processing_time": 0.06681609153747559}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The term 'retarded' is derogatory and offensive when used to insult or belittle someone.", "processing_time": 0.8212571144104004}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language that is derogatory towards individuals with intellectual disabilities.", "processing_time": 2.285841464996338, "retrieved_examples": "Example 1: \"Dis nigga look retarded\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"This is fucking retarded\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Your retarded.\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.291383981704712, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 83, "dynamic_weight_predictions": 83, "consensus_predictions": 0, "avg_coordination_time": 6.900454090302249, "system_type": "intelligent_coordination"}, "text": "<PERSON><PERSON> is retarded", "true_label": 1, "processing_time": 2.291383981704712, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.562032513320446, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:1.00); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9962601065635681, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.996 confidence", "processing_time": 0.06993651390075684}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory terms and insults towards women, which is offensive.", "processing_time": 0.8396904468536377}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech referring to individuals in a derogatory manner based on their gender and sexual orientation.", "processing_time": 2.0479087829589844, "retrieved_examples": "Example 1: \"4 old hoes, a ponk, and a dyke outchea having \"girl talk\"\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"\"@ronald_iongi: Ummmmmm okay http://t.co/4BVLsci5K5\" you got hoes from left to right &#128514;&#128514; do yah thang!\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"&#8220;@Ditto_Guwop: All these hoes been passed around to a Cleveland niggah&#8221;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.051603078842163, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 84, "dynamic_weight_predictions": 84, "consensus_predictions": 0, "avg_coordination_time": 6.842729673499153, "system_type": "intelligent_coordination"}, "text": "jooooooked out RT @topmodelciara: &#8220;@11wdNICK: 4 old hoes, a ponk, and a dyke outchea having \"girl talk\"&#8221;LMAO", "true_label": 1, "processing_time": 2.051603078842163, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5420057401061058, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.84); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8360459208488464, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.836 confidence", "processing_time": 0.0610198974609375}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms directed at individuals based on race and gender.", "processing_time": 0.858055591583252}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language with derogatory terms.", "processing_time": 5.080210208892822, "retrieved_examples": "Example 1: \"I don't want a bitch that a nigga already had I want sumn new ... Or a runner it's too easy nigga I accept the challenge\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"\"But I need a bad bitch that's hotter than me\" might be the realist shit PartyNextDoor ever said. That's how I be feelin everyday\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I need a bad bitch thats gon treat me good\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 5.082732915878296, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 85, "dynamic_weight_predictions": 85, "consensus_predictions": 0, "avg_coordination_time": 6.822023829291848, "system_type": "intelligent_coordination"}, "text": "Run that nigga, you don't want that nigga, but I need a bad bitch that's hotter than me", "true_label": 1, "processing_time": 5.082732915878296, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.561081163585186, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.988649308681488, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.989 confidence", "processing_time": 0.0630800724029541}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of the term 'faggot' is derogatory and offensive towards the individual mentioned, making the speech offensive.", "processing_time": 0.8384819030761719}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it does not contain any derogatory language or slurs.", "processing_time": 4.524838924407959, "retrieved_examples": "Example 1: \"@gezus__ @MANIAC3X @Buckm00se but Never to moose lol log off faggot your finished\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@NoBeeetch faggot ass fucking snitch\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@AGreasyMonkey @Huntermoore faggot\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 4.5279481410980225, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 86, "dynamic_weight_predictions": 86, "consensus_predictions": 0, "avg_coordination_time": 6.79534853059192, "system_type": "intelligent_coordination"}, "text": "@gezus__ @Buckm00se lol this faggot hid from the moon boss we i was helping him", "true_label": 1, "processing_time": 4.5279481410980225, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5539657101035118, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9317256808280945, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.932 confidence", "processing_time": 0.06189227104187012}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit derogatory language towards a specific individual, classifying it as offensive speech.", "processing_time": 0.9993836879730225}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language directed towards a specific individual.", "processing_time": 2.9720299243927, "retrieved_examples": "Example 1: \"<PERSON><PERSON><PERSON> b pissing me off RT @Tristine214: @Thowd214 ugh I cannot stand bitches like that smh\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Hate this dyke\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Hate on what bitch http://t.co/QzTnw6cdKi\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.977489948272705, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 87, "dynamic_weight_predictions": 87, "consensus_predictions": 0, "avg_coordination_time": 6.75146509861124, "system_type": "intelligent_coordination"}, "text": "Hate that bitch RT @NYSAnimal Ray <PERSON>'s wife is pissing me off", "true_label": 1, "processing_time": 2.977489948272705, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5435877777636051, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.85); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8487022221088409, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.849 confidence", "processing_time": 0.0642709732055664}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and offensive language towards black women", "processing_time": 0.7583229541778564}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it uses derogatory language towards a specific group of people based on their race and implies a negative stereotype.", "processing_time": 3.8412628173828125, "retrieved_examples": "Example 1: \"I need a bitch\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I need a new bitch\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I need me a bitch\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 3.846637487411499, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 88, "dynamic_weight_predictions": 88, "consensus_predictions": 0, "avg_coordination_time": 6.718455693938515, "system_type": "intelligent_coordination"}, "text": "I need a girl from Jamaica I can't fuck with these basic black bitches", "true_label": 1, "processing_time": 3.846637487411499, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5544034019112587, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.94); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9352272152900696, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.935 confidence", "processing_time": 0.06598019599914551}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insult directed at an individual", "processing_time": 0.7599914073944092}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to someone as a 'bitch'.", "processing_time": 2.0244576930999756, "retrieved_examples": "Example 1: \"@TheErniie You're a little bitch.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"@NjzFinest_ your such a bitch lol&#128564;&#128564;&#128564;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@FukknTyler u a bitch in niggas clothing shut yo ass up &#128514;&#128514;&#128514;&#128514;&#128514;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.0315542221069336, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 89, "dynamic_weight_predictions": 89, "consensus_predictions": 0, "avg_coordination_time": 6.665793879648273, "system_type": "intelligent_coordination"}, "text": "@Capt<PERSON><PERSON>ott NFN you bitch to much.", "true_label": 1, "processing_time": 2.0315542221069336, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5588979795575142, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.97); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9711838364601135, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.971 confidence", "processing_time": 0.061499595642089844}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it simply mentions two well-known individuals in a positive context.", "processing_time": 0.8219473361968994}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply praises <PERSON> and <PERSON> for their contributions to the Yankees.", "processing_time": 2.6625583171844482, "retrieved_examples": "Example 1: \"'2' good: <PERSON><PERSON> hero for Yankees in send-off http://t.co/oStFoBGuFa via @ESPNNewYork\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"RT: Thank you, Captain. It'll never be the same. @Yankees #Jeter#2 http://t.co/EIe8D8C5cj&#8221;\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"RT @SportsCenter: <PERSON> leaves baseball as one of the greatest Yankees ever. (via @ESPNMag) http://t.co/MSUwxc3tYx\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.668023109436035, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 90, "dynamic_weight_predictions": 90, "consensus_predictions": 0, "avg_coordination_time": 6.6213742044236925, "system_type": "intelligent_coordination"}, "text": "&#8220;@CaptainYankee2: Two of the best Yankees <PERSON> and <PERSON> #JoeTorreDay http://t.co/XMyxfDBKOX&#8221; @jordan_luree", "true_label": 0, "processing_time": 2.668023109436035, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.556882306933403, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.96); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9550584554672241, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.955 confidence", "processing_time": 0.05788898468017578}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive, no explicit insults, threats, or derogatory content", "processing_time": 0.849031925201416}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply mentions seeing a character named pom-pom <PERSON>.", "processing_time": 2.3228490352630615, "retrieved_examples": "Example 1: \"@peteyhoti that funky monkey\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"@<PERSON><PERSON><PERSON><PERSON><PERSON> at least he went up and wasn't a bitch\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@<PERSON><PERSON>Edwards why were there penguins and a bird in the puppy bowl???\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.3276758193969727, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 91, "dynamic_weight_predictions": 91, "consensus_predictions": 0, "avg_coordination_time": 6.574190705687135, "system_type": "intelligent_coordination"}, "text": "@Ants_SNEweather Well you got to see pom-pom Pete", "true_label": 0, "processing_time": 2.3276758193969727, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5621613338589668, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:1.00); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9972906708717346, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.997 confidence", "processing_time": 0.07036948204040527}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit derogatory language towards individuals based on sexual orientation", "processing_time": 0.9099726676940918}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms.", "processing_time": 2.0006561279296875, "retrieved_examples": "Example 1: \"RT @wishfulll: Big booty bitches with... \"@StagBeChillin: Ass RT @iDocLyfe: Tits\"\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @BLVKOUT: &#8220;@EMPRESSRAW: &#8220;@Naezus: Best man ever RT @TRILL_A_G: \"I'd fuck her right in the pussy!!!!\" http://t.co/Xfn5gaURXm&#8221;&#128514;&#128514;&#128514;&#8221; my nigg&#8230;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @InkMyHoleBody: This nigga is a fuckin faggot https://t.co/e3m6tujnhF\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.0056557655334473, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 92, "dynamic_weight_predictions": 92, "consensus_predictions": 0, "avg_coordination_time": 6.524532717207204, "system_type": "intelligent_coordination"}, "text": "*G<PERSON><PERSON> RT @Naezus What a big faggot. RT @MIllionDolrBaby: <PERSON><PERSON> bout to <PERSON>.", "true_label": 1, "processing_time": 2.0056557655334473, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5610655434429646, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9885243475437164, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.989 confidence", "processing_time": 0.06073117256164551}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text mentions a development in Jihadi circles regarding a request for relief and aid workers' release, without containing any explicit insults, threats, or derogatory content.", "processing_time": 1.****************}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it discusses a major development in Jihadi circles regarding a request for the release of relief and aid workers.", "processing_time": 2.557795524597168, "retrieved_examples": "Example 1: \"Now I am seeing chatter among Jihadi fans taking credit for an alledged kidnapping of a Frenchman in #Algeria #France\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"\"@hxhassan: Jihadi patron <PERSON><PERSON><PERSON><PERSON> is arrested at Kuwait airport https://t.co/ZmloKfWDQl\"\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Now infuriated Jihadis are putting out murder requests on #Saudi fighter pilots who participated in the bombings in #Syria #ISIS\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.5633044242858887, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 93, "dynamic_weight_predictions": 93, "consensus_predictions": 0, "avg_coordination_time": 6.***************, "system_type": "intelligent_coordination"}, "text": "Another major development in the Jihadi circles: <PERSON>, hardcore jihadi theorist asks specifically for relief &amp; aid workers&#8217; release", "true_label": 0, "processing_time": 2.5633044242858887, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5412892140448093, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.83); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8303137123584747, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.830 confidence", "processing_time": 0.06216168403625488}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The statement expresses admiration for <PERSON>'s actions and humorously suggests he would be the perfect boyfriend if he also took out the trash. It does not contain offensive speech.", "processing_time": 1.047778606414795}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it praises <PERSON> for his actions and does not contain any derogatory language or intent.", "processing_time": 2.5190494060516357, "retrieved_examples": "Example 1: \"Corey is trash\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @MensHumor: One man's trash is another man's girlfriend.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"@JLlVE looks prime for some trash pick up\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.521679639816284, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 94, "dynamic_weight_predictions": 94, "consensus_predictions": 0, "avg_coordination_time": 6.4398084473102655, "system_type": "intelligent_coordination"}, "text": ".@<PERSON><PERSON><PERSON><PERSON> is running around town delivering food to people. If he takes out the trash too he is the perfect boyfriend.", "true_label": 0, "processing_time": 2.521679639816284, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5608296990394592, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9866375923156738, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.987 confidence", "processing_time": 0.06693482398986816}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it is a promotional tweet about Nike beanies.", "processing_time": 0.8336691856384277}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply mentions Nike beanies and includes a hashtag related to Nike products.", "processing_time": 2.3426029682159424, "retrieved_examples": "Example 1: \"RT @cwissi: beanies for life &#9697;&#776;&#8413; http://t.co/iywPwmPGTu\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"beanies cumming real soon http://t.co/GlLjaQb6ty\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"It's beanie weather bitches &#127744; http://t.co/uy012Hkz9Q\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.3480398654937744, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 95, "dynamic_weight_predictions": 95, "consensus_predictions": 0, "avg_coordination_time": 6.396737199080618, "system_type": "intelligent_coordination"}, "text": "RT @BestOfNike: Nike beanies #NikePorn http://t.co/nN4Vo90htj", "true_label": 1, "processing_time": 2.3480398654937744, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5611557774245739, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9892462193965912, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.989 confidence", "processing_time": 0.06027531623840332}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insult directed at a specific group (NBC)", "processing_time": 0.7703990936279297}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it uses derogatory language towards a group of people.", "processing_time": 2.141202449798584, "retrieved_examples": "Example 1: \"RT @UrsTruly_Jessie: These bitches is actors out here\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"RT @badlvck: bitches &lt;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"RT @rebecca7054: &#8220;@CaitlinBartlett: yeah bitches http://t.co/2PP9RboHy4&#8221;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.1439125537872314, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 96, "dynamic_weight_predictions": 96, "consensus_predictions": 0, "avg_coordination_time": 6.352436942358811, "system_type": "intelligent_coordination"}, "text": "RT @bijoudulcet: Y'all some bitches @nbc", "true_label": 1, "processing_time": 2.1439125537872314, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.561657190322876, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9932575225830078, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.993 confidence", "processing_time": 0.06476402282714844}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive language and derogatory term 'bitch'", "processing_time": 1.101064682006836}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language with the use of the word 'bitch'.", "processing_time": 2.4096903800964355, "retrieved_examples": "Example 1: \"This biatch just called me a bitch &#128514;&#128514;\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"He said bitch boy &#128514;\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Almost just said some bitch shit &#128527;\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.41231369972229, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 97, "dynamic_weight_predictions": 97, "consensus_predictions": 0, "avg_coordination_time": 6.3118171151151365, "system_type": "intelligent_coordination"}, "text": "Left a nice little message in his inbox so he deleted the comment where he called me a bitch &#128514; these&#8230; http://t.co/qRUvCVNepv", "true_label": 1, "processing_time": 2.41231369972229, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5600281953811646, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.98); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9802255630493164, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.980 confidence", "processing_time": 0.056609392166137695}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The phrase contains derogatory language and is offensive towards women.", "processing_time": 0.7686605453491211}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to women in a derogatory manner.", "processing_time": 1.885613203048706, "retrieved_examples": "Example 1: \"High thenna bitch that's y I'm tweeting\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"High then a hoe with bitch ass Benny\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Ha bitch\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 1.8914837837219238, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 98, "dynamic_weight_predictions": 98, "consensus_predictions": 0, "avg_coordination_time": 6.2667116729580625, "system_type": "intelligent_coordination"}, "text": "Higher den a bitch", "true_label": 1, "processing_time": 1.8914837837219238, "dataset": "HateSpeechOffensive"}, {"verdict": 0, "confidence": 0.5447998816768329, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.86); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8583990534146627, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.858 confidence", "processing_time": 0.06247997283935547}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply celebrates a sports victory and mentions a player as a hero.", "processing_time": 0.8219759464263916}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply celebrates a sports victory and mentions the team's current standing.", "processing_time": 2.305691957473755, "retrieved_examples": "Example 1: \"RT @Yankees: Ballgame over, #Yankees WIN! Yanks beat the White Sox in 10 innings, 4-3.\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"RT @Yankees: #Yankees win! Final score: 7-4.\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"RT @SportsCenter: Yankees survive 9th-inning scare to beat Rangers, 12-11.\n&#8226; <PERSON>: 4-5, <PERSON><PERSON>, <PERSON>, 3 R\n&#8226; <PERSON><PERSON><PERSON>: 1-2, <PERSON><PERSON>, 2 RBI, 3 R htt&#8230;\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.311227321624756, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 99, "dynamic_weight_predictions": 99, "consensus_predictions": 0, "avg_coordination_time": 6.226757285570858, "system_type": "intelligent_coordination"}, "text": "RT @Yankees: VICTORY! #Yankees outlast A<PERSON><PERSON>, 10-9 in 14 innings. <PERSON> the hero, and oh yeah, FIRST PLACE still resides in the Bronx.", "true_label": 0, "processing_time": 2.311227321624756, "dataset": "HateSpeechOffensive"}, {"verdict": 1, "confidence": 0.5533849423130353, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9270795385042826, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.927 confidence", "processing_time": 0.06403303146362305}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit and derogatory language towards a specific individual, which is offensive.", "processing_time": 0.8139922618865967}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains sexually explicit and offensive language.", "processing_time": 2.0583794116973877, "retrieved_examples": "Example 1: \"My dick, your bitch on it.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"She suckin on my dick, cuz I'm a pretty bitch\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"My bitch bad\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.062041759490967, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 100, "dynamic_weight_predictions": 100, "consensus_predictions": 0, "avg_coordination_time": 6.185110130310059, "system_type": "intelligent_coordination"}, "text": "Your bitch got her lipstick on my dick...", "true_label": 1, "processing_time": 2.062041759490967, "dataset": "HateSpeechOffensive"}], "system_config": {"enable_dynamic_weights": true, "enable_performance_tracking": true, "enable_online_learning": true, "coordination_method": "dynamic_weighted", "learning_rate": 0.01, "confidence_threshold": 0.6}}