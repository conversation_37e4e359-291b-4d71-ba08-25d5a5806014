{"test_results": [{"test_case": {"text": "这是一个正常的测试文本", "expected_label": 0, "description": "中文正常文本"}, "result": {"verdict": 0, "confidence": 0.558609526604414, "reasoning": "激进优化协调决策: deberta(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.97); llm_single(原权重:0.37→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9688762128353119, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.969 confidence", "processing_time": 0.7574796676635742}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it appears to be a normal test text without any explicit insults, threats, or derogatory content.", "processing_time": 6.342963695526123}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it appears to be a normal test text in Chinese.", "processing_time": 3.39045786857605, "retrieved_examples": "Example 1: \"nothing to see here, goyess\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"( kkk )\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"hello white\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.225, "llm_single": 0.37499999999999994, "llm_retrieval": 0.39999999999999997}, "coordination_time": 6.346201419830322, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 1, "dynamic_weight_predictions": 1, "consensus_predictions": 0, "avg_coordination_time": 6.346201419830322, "system_type": "intelligent_coordination"}}, "duration": 15.294633150100708, "success": true}, {"test_case": {"text": "jews don't want to live close to the non-white invaders they welcome", "expected_label": 1, "description": "英文仇恨言论"}, "result": {"verdict": 1, "confidence": 0.5419923439621925, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.84); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8359387516975403, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.836 confidence", "processing_time": 0.11876916885375977}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and promotes hatred towards Jewish and non-white individuals.", "processing_time": 3.2502033710479736}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it refers to non-white individuals as 'invaders', which is dehumanizing and derogatory.", "processing_time": 4.870401859283447, "retrieved_examples": "Example 1: \"jews are white\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"jews need to evacuate white nations and live in israel . that's something i'm not compromising on .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"jews don't consider themselves white .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 4.874403476715088, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 1, "dynamic_weight_predictions": 1, "consensus_predictions": 0, "avg_coordination_time": 4.874403476715088, "system_type": "intelligent_coordination"}}, "duration": 7.273469686508179, "success": true}, {"test_case": {"text": "I love spending time with my family and friends", "expected_label": 0, "description": "英文正常文本"}, "result": {"verdict": 0, "confidence": 0.5529135502874851, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.92); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.923308402299881, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.923 confidence", "processing_time": 0.11624264717102051}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive speech expressing positive feelings towards loved ones", "processing_time": 0.7981545925140381}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it expresses positive feelings towards spending time with loved ones.", "processing_time": 3.4614784717559814, "retrieved_examples": "Example 1: \"i love being white .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"i love you guys and i love your work\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"I don’t think I’m racist I love black people I think every family should have one\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 3.464137315750122, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 1, "dynamic_weight_predictions": 1, "consensus_predictions": 0, "avg_coordination_time": 3.464137315750122, "system_type": "intelligent_coordination"}}, "duration": 5.849063158035278, "success": true}], "network_requests": [], "analysis": {"total_tests": 3, "successful_tests": 3, "accuracy": 1.0, "has_real_processing": true, "has_real_reasoning": true, "has_network_activity": false, "has_retrieval_examples": true, "total_score": 3, "agent_analysis": {"deberta": {"count": 3, "total_time": 0.9924914836883545, "has_reasoning": 3}, "llm_single": {"count": 3, "total_time": 10.391321659088135, "has_reasoning": 3}, "llm_retrieval": {"count": 3, "total_time": 11.722338199615479, "has_reasoning": 3, "has_examples": 3}}, "url_stats": {}}}