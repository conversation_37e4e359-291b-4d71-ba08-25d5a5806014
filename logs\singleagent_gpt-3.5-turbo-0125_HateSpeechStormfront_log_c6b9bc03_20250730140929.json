{"run_id": "c6b9bc03", "dataset": "HateSpeechStormfront", "model": "gpt-3.5-turbo-0125", "detector_type": "singleagent", "num_samples": 478, "metrics": {"accuracy": 0.8319327731092437, "precision": 0.8152610441767069, "recall": 0.8565400843881856, "f1": 0.8353909465020576, "confusion_matrix": [[193, 46], [34, 203]], "avg_processing_time": 1.4649645509081406}, "results": [{"verdict": 1, "explanation": "Contains derogatory and discriminatory language towards non-white immigrants, inciting hatred and promoting racial division.", "processing_time": 2.7541630268096924, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory language towards non-white immigrants, inciting hatred and promoting racial division.\"\n}", "text": "The white man has no future in Canada , that is for sure , because the country is being flooded with non-white immigrants .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insult and derogatory content towards a specific group (a nation)", "processing_time": 0.9908275604248047, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insult and derogatory content towards a specific group (a nation)\"\n}", "text": "I take the assumption that your Nation is so crap is not worth remembering .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"error": "Empty response from model", "processing_time": 1.0466465950012207, "raw_model_output": "{\"error\": \"Empty response from model\"}", "text": "If you want to hear what a black `` intellectual '' ( lol ) sounds like , check out the woman in this video trying to debate Dr. <PERSON> .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains derogatory and offensive language towards a specific group of individuals, referring to children in a disrespectful and dehumanizing manner.", "processing_time": 1.0851211547851562, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains derogatory and offensive language towards a specific group of individuals, referring to children in a disrespectful and dehumanizing manner.\"\n}", "text": "I 've always considered teaching as one of the professions I would like to get into , but not in a neighbourhood like that ... never. kids like that disgust me .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses an observation about behavior without containing explicit insults, threats, or hatred. It does not target specific groups or individuals.", "processing_time": 0.9916610717773438, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses an observation about behavior without containing explicit insults, threats, or hatred. It does not target specific groups or individuals.\"\n}", "text": "And the sad thing is the white students at those schools will act like that too .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains offensive speech with derogatory content towards Jewish individuals.", "processing_time": 0.8975536823272705, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains offensive speech with derogatory content towards Jewish individuals.\"\n}", "text": "And my job lets me monitor all of Jewish supremacist control of talk radio .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The mention of 'stormfront.org' indicates potential offensive content as it is a known platform for hate speech and white supremacy.", "processing_time": 2.2221124172210693, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The mention of 'stormfront.org' indicates potential offensive content as it is a known platform for hate speech and white supremacy.\"\n}", "text": "See the new thread @ https : //www.stormfront.org/forum/t989503/", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses a political opinion without containing explicit insults, threats, or hatred. Non-offensive speech.", "processing_time": 1.0354533195495605, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses a political opinion without containing explicit insults, threats, or hatred. Non-offensive speech.\"\n}", "text": "I read a few things about him awhile back and he is far to the left as so many of them are .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing confusion or strangeness without insults or derogatory content", "processing_time": 0.979738712310791, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing confusion or strangeness without insults or derogatory content\"\n}", "text": "This whole situation starts to become really bizarre .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms and promotes racial hatred and discrimination.", "processing_time": 1.8571481704711914, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms and promotes racial hatred and discrimination.\"\n}", "text": "They need the white male distracted as much as possible and of course the glorification of the groid ; two birds one stone .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and anti-Semitic remarks", "processing_time": 2.137051582336426, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and anti-Semitic remarks\"\n}", "text": "I will at least never willingly pay to see this propaganda , maybe just watch it for free to show the influence the jews have got in the white genocide to people .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it expresses a concern about running out of options or space.", "processing_time": 0.9998009204864502, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it expresses a concern about running out of options or space.\"\n}", "text": "Pretty soon , there 's not going to be anywhere left to run .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of the term 'negroes' in a derogatory and stereotypical manner is offensive and promotes negative stereotypes.", "processing_time": 6.242315769195557, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of the term 'negroes' in a derogatory and stereotypical manner is offensive and promotes negative stereotypes.\"\n}", "text": "I reported some time ago that negroes will even strip the bricks off your home while you are away at work .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory remarks and incitement of hatred towards the LGBTQ+ community and individuals in San Francisco", "processing_time": 1.670689344406128, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory remarks and incitement of hatred towards the LGBTQ+ community and individuals in San Francisco\"\n}", "text": "This family that wants to complain about gays should come to San Francisco and raise some hell at these gay pride events .... a much better target ..... and these people in San Francisco openly blasphemied <PERSON> with their parody of ' The Last Supper `` as their poster for their last event .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist remarks towards Black people based on false stereotypes and pseudoscience.", "processing_time": 2.51682710647583, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist remarks towards Black people based on false stereotypes and pseudoscience.\"\n}", "text": "That or it might be that Blacks have smaller brains and an IQ 15-30 points lower than that of White people on average .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content towards Mexicans and sexism towards a female employee", "processing_time": 0.9117884635925293, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content towards Mexicans and sexism towards a female employee\"\n}", "text": "We have two things wrong here , Mexcicans in a White homeland and a Pizza company sending a female in harms way .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it merely expresses impatience or frustration", "processing_time": 1.0513904094696045, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it merely expresses impatience or frustration\"\n}", "text": "How much longer are we going to wait for them to take care of themselves ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing a positive opinion about <PERSON>'s assistance", "processing_time": 0.901111364364624, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing a positive opinion about <PERSON>'s assistance\"\n}", "text": "I hear that <PERSON><PERSON> is coming to their aid .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing satisfaction or completion", "processing_time": 0.9666883945465088, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing satisfaction or completion\"\n}", "text": "That's all I needed to hear .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit hatred and derogatory content towards a specific racial group.", "processing_time": 0.8916597366333008, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit hatred and derogatory content towards a specific racial group.\"\n}", "text": "Blacks hate whites .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful language towards a specific group based on sexual orientation", "processing_time": 2.2421276569366455, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful language towards a specific group based on sexual orientation\"\n}", "text": "The media wants to bury the story because they care more about homo killers not getting their sick fetish for young boys fufilled .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content. It appears to be a comparison based on diversity.", "processing_time": 1.9749484062194824, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content. It appears to be a comparison based on diversity.\"\n}", "text": "The white churches ' billboards look like lunch break at the UN .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains a derogatory term and implies blame towards a specific racial group, which is offensive.", "processing_time": 1.9846339225769043, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains a derogatory term and implies blame towards a specific racial group, which is offensive.\"\n}", "text": "I'm sure the negro had it coming .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement promotes hatred and discrimination against white people, making it offensive.", "processing_time": 1.707533359527588, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement promotes hatred and discrimination against white people, making it offensive.\"\n}", "text": "ZOG is getting rid of every White-dominated area in the world .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language directed at a specific individual", "processing_time": 1.0256109237670898, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language directed at a specific individual\"\n}", "text": "Except , of course , for those that pander to the filthy sons and daughters of <PERSON> like ' ol <PERSON> here .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is not offensive as it expresses concern for the well-being of monkeys.", "processing_time": 1.9017620086669922, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is not offensive as it expresses concern for the well-being of monkeys.\"\n}", "text": "What did those poor monkeys ever do to you ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insult and derogatory language towards an individual", "processing_time": 2.048215866088867, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insult and derogatory language towards an individual\"\n}", "text": "Do ` F * ck with our families , buckwheat !!", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit suggestion of a violent and destructive act, along with derogatory language towards specific groups", "processing_time": 2.1189699172973633, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit suggestion of a violent and destructive act, along with derogatory language towards specific groups\"\n}", "text": "I always thought it would be cool if somebody covertly set off a nuke in Kashmir , and then we all could sit back and watch as the Paki 's and Indians nuke the scheiss out of each other .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"error": "Empty response from model", "processing_time": 1.1073212623596191, "raw_model_output": "{\"error\": \"Empty response from model\"}", "text": "If it 's black , send it back .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of the term 'savages' is derogatory and dehumanizing, inciting violence and hatred towards a specific group of people.", "processing_time": 1.7079741954803467, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of the term 'savages' is derogatory and dehumanizing, inciting violence and hatred towards a specific group of people.\"\n}", "text": "May they just need to turn these savages loose , justice will take care of itself .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit encouragement of neglect and endangerment of children", "processing_time": 1.1849896907806396, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit encouragement of neglect and endangerment of children\"\n}", "text": "Just let <PERSON><PERSON><PERSON> leave her kids home to burn the house down while she goes to da club .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and promotes fear and stereotypes based on neighborhoods", "processing_time": 0.9561777114868164, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and promotes fear and stereotypes based on neighborhoods\"\n}", "text": "Then we will see who is afraid to leave thier homes after dark or venture into the wrong ' hood .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement describes a situation without containing explicit insults, threats, or hatred. It expresses an opinion about a decision made by individuals.", "processing_time": 1.2050371170043945, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement describes a situation without containing explicit insults, threats, or hatred. It expresses an opinion about a decision made by individuals.\"\n}", "text": "I mean they intentionally chose this house knowing there were white people living there .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses positive sentiment towards the article and raises a question about why certain individuals were not in jail. It does not contain offensive speech.", "processing_time": 1.052605390548706, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses positive sentiment towards the article and raises a question about why certain individuals were not in jail. It does not contain offensive speech.\"\n}", "text": "Excellent Article !! * * * * * Why Were n't They In Jail ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it discusses a sensitive topic without using explicit insults or derogatory language.", "processing_time": 1.065425157546997, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it discusses a sensitive topic without using explicit insults or derogatory language.\"\n}", "text": "They do not want all of us to find out how she was raped and tortured .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit suggestion of violence and harm towards individuals, indicating offensive speech.", "processing_time": 2.337043523788452, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit suggestion of violence and harm towards individuals, indicating offensive speech.\"\n}", "text": "A public execution for these animals would go a LONG way to point to justice being done ; it would never bring the girl back ; but I guarantee it would send a message ............................", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit threat and derogatory term towards a group of individuals", "processing_time": 1.5925219058990479, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit threat and derogatory term towards a group of individuals\"\n}", "text": "God please bless with wisdom the judge and jury against this group of thugs that they all might be hanged .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement promotes racial discrimination and hatred towards white individuals.", "processing_time": 1.0182499885559082, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement promotes racial discrimination and hatred towards white individuals.\"\n}", "text": "It seems that what they are trying to say is that if you are white you are in the wrong place at the wrong time anywhere in the World .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of the derogatory term 'groid' and the mention of rape are offensive and inappropriate.", "processing_time": 0.904376745223999, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of the derogatory term 'groid' and the mention of rape are offensive and inappropriate.\"\n}", "text": "Nevermind, found the new page Knew it had to be a groid .... any report of rape ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit threats and violence towards a specific group, which is highly offensive and inappropriate.", "processing_time": 1.2494916915893555, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit threats and violence towards a specific group, which is highly offensive and inappropriate.\"\n}", "text": "Perhaps I should walk back into my H.S. when it starts Monday and get on the P.A. system and say I want to tie all * * * * * * s to the back of my car and drag them across the ground till they die , and then say I 'm joking .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains dehumanizing language and expresses a derogatory attitude towards a specific group of people, suggesting they have no value.", "processing_time": 1.3716590404510498, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains dehumanizing language and expresses a derogatory attitude towards a specific group of people, suggesting they have no value.\"\n}", "text": "Let mother nature evolve these people naturally because no amount of money or aid is going to drag these ' things ' into the civilized world where they serve absolutely no purpose .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is neutral and does not contain any offensive content.", "processing_time": 1.649535894393921, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is neutral and does not contain any offensive content.\"\n}", "text": "Right.", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit racist and derogatory language towards a specific group of individuals.", "processing_time": 1.234422206878662, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit racist and derogatory language towards a specific group of individuals.\"\n}", "text": "I'm heading down to DC to put a sign on the White House gates that will read : ' No savage negro BEASTS in the White House ! '", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The provided text is a URL link and does not contain any offensive speech.", "processing_time": 1.3769450187683105, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The provided text is a URL link and does not contain any offensive speech.\"\n}", "text": "http://www.amv3.com/forum/viewtopic.php ? p = 5542#5542", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply expresses a positive opinion about the link.", "processing_time": 1.6344356536865234, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply expresses a positive opinion about the link.\"\n}", "text": "Great link .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it merely expresses an opinion about the content resembling science fiction.", "processing_time": 1.57550048828125, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it merely expresses an opinion about the content resembling science fiction.\"\n}", "text": "It almost reads like something out of a science fiction novel .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains offensive language and derogatory terms towards individuals based on their race and mental abilities.", "processing_time": 1.5777196884155273, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains offensive language and derogatory terms towards individuals based on their race and mental abilities.\"\n}", "text": "He's a retarded feral negro ( yes , feral humans who cannot talk occasionally pop up , having been abandoned as children. ) .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, or derogatory content. It expresses an opinion about a behavior without targeting specific groups or individuals.", "processing_time": 1.5161969661712646, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, or derogatory content. It expresses an opinion about a behavior without targeting specific groups or individuals.\"\n}", "text": "They do what they always do take something of someone else 's and make it their own .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content. It appears to be a statement expressing skepticism or criticism about potential future representations.", "processing_time": 1.6454036235809326, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content. It appears to be a statement expressing skepticism or criticism about potential future representations.\"\n}", "text": "No doubt they 'll be coming up with chocolate-colored Leprechauns and a Chinese flavored Uncle <PERSON> 's too .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text contains personality assessment results and does not contain any offensive speech.", "processing_time": 1.2404630184173584, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text contains personality assessment results and does not contain any offensive speech.\"\n}", "text": "INTJ Introvert ( 11 % ) iNtuitive ( 38 % ) iNtuitive Thinking ( 38 % ) Judging ( 22 % ) You have slight preference of Introversion over Extraversion ( 11 % ) You have moderate preference of Intuition over Sensing ( 38 % ) You have moderate preference of Thinking over Feeling ( 38 % ) You have slight preference of Judging over Perceiving ( 22 % )", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, simply stating a score without any offensive content", "processing_time": 1.2498455047607422, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, simply stating a score without any offensive content\"\n}", "text": "Only scored a 120 .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit hatred and derogatory content directed at the Jewish community.", "processing_time": 1.3507049083709717, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit hatred and derogatory content directed at the Jewish community.\"\n}", "text": "In Eternal Combat with that Satanic Spirit - The physical incarnation , creation of the Demiurge : The Jews .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and xenophobic remarks towards Chinese individuals", "processing_time": 1.2242519855499268, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and xenophobic remarks towards Chinese individuals\"\n}", "text": "This is ARKANSAS they have no right to smell up my place with their chinese restaurant BO ~ ▲ RESTORE ORDER ▲ ~", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it simply expresses an opinion about equality and acknowledgment of white nations.", "processing_time": 1.3091638088226318, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it simply expresses an opinion about equality and acknowledgment of white nations.\"\n}", "text": "I do n't think anyone is insinuating that we are equal to non whites , or that we would ignore white nations .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, or derogatory content. It expresses an opinion about people lacking self-awareness and feeling guilty about their knowledge.", "processing_time": 1.5040388107299805, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, or derogatory content. It expresses an opinion about people lacking self-awareness and feeling guilty about their knowledge.\"\n}", "text": "These people have no idea about who they are or where they came from and any knowledge that they do have has guilt attached to it .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is not offensive as it merely suggests trying a nationalist site, without containing insults, threats, or derogatory content.", "processing_time": 1.6160058975219727, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is not offensive as it merely suggests trying a nationalist site, without containing insults, threats, or derogatory content.\"\n}", "text": "It 's a nationalist site as well , maybe give them a try !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text provided is neutral and does not contain any offensive speech.", "processing_time": 1.3999922275543213, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text provided is neutral and does not contain any offensive speech.\"\n}", "text": "Invictus Press Home Page", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech or derogatory content.", "processing_time": 1.2295591831207275, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech or derogatory content.\"\n}", "text": "Pure .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement mentions race but does not contain explicit insults, threats, or hatred. It appears to be discussing a topic related to race.", "processing_time": 1.4046413898468018, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement mentions race but does not contain explicit insults, threats, or hatred. It appears to be discussing a topic related to race.\"\n}", "text": "It has everything to do with RACE .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses an opinion about the safety and future of a specific group without containing explicit insults, threats, or hatred.", "processing_time": 1.3326642513275146, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses an opinion about the safety and future of a specific group without containing explicit insults, threats, or hatred.\"\n}", "text": "I think it is a bit late to think to look after the safety and the future of white people in South africa .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it suggests taking action against injustice through the internet.", "processing_time": 1.231797456741333, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it suggests taking action against injustice through the internet.\"\n}", "text": "Or make something in the internet against thi injustice some times is hit a global repercussion and can help this .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and racial stereotypes towards African Americans", "processing_time": 1.5676829814910889, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and racial stereotypes towards African Americans\"\n}", "text": "WE GOT MONEY AROUND HERE TOO Compton Crips Pt.1 - YouTube These negroes are in the middle of the street , seems like something of a zoo , playing with their cheap lowriders .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech expressing a personal opinion about someone's inability to see the truth", "processing_time": 1.2466199398040771, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech expressing a personal opinion about someone's inability to see the truth\"\n}", "text": "My favorite part : And still he ca n't see the truth right in front of his face .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist language towards a specific group based on their race", "processing_time": 1.2694838047027588, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist language towards a specific group based on their race\"\n}", "text": "I know exactly what you 're saying , I just hate to see those worthless apes benefit from the great things that whites do .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech does not contain explicit insults, threats, or derogatory content. It expresses a lack of willingness to engage in a certain activity.", "processing_time": 1.394287347793579, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech does not contain explicit insults, threats, or derogatory content. It expresses a lack of willingness to engage in a certain activity.\"\n}", "text": "Seems none of these pinheads wanted to get dirty .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms and promotes hatred towards specific groups based on race and sexual orientation.", "processing_time": 1.2308294773101807, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms and promotes hatred towards specific groups based on race and sexual orientation.\"\n}", "text": "It 's going viral on Youtube - warning the lesbian ' groid gets aggressive on the whites for whatever reason : YouTube - Video Caught On Tape Lesbian Or Young Man Going Wild On BART Train In Oakland !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains offensive language and derogatory term towards a specific racial group.", "processing_time": 1.1830053329467773, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains offensive language and derogatory term towards a specific racial group.\"\n}", "text": "More impulsive Negro stripping ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses an opinion about the content of the videos and does not contain any offensive language or content.", "processing_time": 1.2735531330108643, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses an opinion about the content of the videos and does not contain any offensive language or content.\"\n}", "text": "Most of these videos belong on the Animal Planet tv channel .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language towards a specific racial group and gender", "processing_time": 1.2037630081176758, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language towards a specific racial group and gender\"\n}", "text": "The kind of savagery against females that has become expected from negroids in todays society ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text contains a reference to a news event, without any explicit offensive content.", "processing_time": 1.5721728801727295, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text contains a reference to a news event, without any explicit offensive content.\"\n}", "text": "Seattle bus attack on a blind woman : YouTube - Broadcast Yourself .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory and offensive language towards the Islamic community, comparing them to diseases.", "processing_time": 1.5980281829833984, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory and offensive language towards the Islamic community, comparing them to diseases.\"\n}", "text": "The islamic disease is even more dangerous than the more obvious black or yellow disease .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory content towards Chinese men, implying negative behavior towards women, which is offensive.", "processing_time": 2.223036766052246, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory content towards Chinese men, implying negative behavior towards women, which is offensive.\"\n}", "text": "Maybe they will be resettled deliberately as refugees or maybe they will come for the women like the chinese men are doing .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement expresses negative views towards interracial relationships, which can be considered offensive and derogatory.", "processing_time": 1.391991138458252, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement expresses negative views towards interracial relationships, which can be considered offensive and derogatory.\"\n}", "text": "There are so many race mixing ads on TV that it gets overwhelming .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist remarks towards black individuals, comparing them to monkeys and implying inferiority", "processing_time": 1.6035614013671875, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist remarks towards black individuals, comparing them to monkeys and implying inferiority\"\n}", "text": "When I was last time in South Africa , we went to Sun City and saw little black monkey children swimming , funniest thing was watching drying water off their faces , it looks exactly like baboon or something , way they do it , whites have certain mannerisms with doing things , they have completely ape-like ones .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language towards specific groups and promotes fear and hatred based on race", "processing_time": 1.3616704940795898, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language towards specific groups and promotes fear and hatred based on race\"\n}", "text": "The jew media likes to prop this whites becoming the minority non sense up because they want to try and scare whitey , the media plays on the fears that the average retard thinks hispanic or latino is a race and they play word play with titles like white hispanic and white non hispanic it is all a magic show nothing more .. But Whites becoming a minority in Europe the homeland is a much more serious issue that concerns me greatly .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of the term 'subhumans' is derogatory and dehumanizing, making the speech offensive.", "processing_time": 1.311549186706543, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of the term 'subhumans' is derogatory and dehumanizing, making the speech offensive.\"\n}", "text": "The future is ours , as long as we do not sit by and wait to be overrun by the subhumans .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory and discriminatory language towards ethnic groups, implying they are responsible for turning neighborhoods into slums.", "processing_time": 1.3301150798797607, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory and discriminatory language towards ethnic groups, implying they are responsible for turning neighborhoods into slums.\"\n}", "text": "We all know that when their are too many ethnics , the neighbourhood soon turn 's into a slum .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains offensive content by promoting hate crime videos based on race, which can incite violence and discrimination.", "processing_time": 1.617464303970337, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains offensive content by promoting hate crime videos based on race, which can incite violence and discrimination.\"\n}", "text": "More black-on-white hate crime videos are posted here : http://ethniccrime.blogspot.com/p/ra...-on-video.html", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement expresses a desire for the removal of Black people from America, which is derogatory and offensive.", "processing_time": 1.3297548294067383, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement expresses a desire for the removal of Black people from America, which is derogatory and offensive.\"\n}", "text": "I have a dream - one of these days Black occupation of America will be history !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit call for arrest, trial, and execution of a specific group (Zionists), indicating hatred and violence.", "processing_time": 1.451824426651001, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit call for arrest, trial, and execution of a specific group (Zionists), indicating hatred and violence.\"\n}", "text": "There is nothing I would love to see more than the arrest , trial and execution of these murderous and genocidal Zionists !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory and dehumanizing language towards individuals labeled as 'pathological killers'.", "processing_time": 1.2508127689361572, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory and dehumanizing language towards individuals labeled as 'pathological killers'.\"\n}", "text": "Pathological killers , they are .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement promotes harmful stereotypes and incites hatred towards Jewish people.", "processing_time": 1.2963342666625977, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement promotes harmful stereotypes and incites hatred towards Jewish people.\"\n}", "text": "Jews have been poisoning wells since Biblical times .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains explicit violent content and suggests harm towards an individual, making it offensive.", "processing_time": 1.6824088096618652, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains explicit violent content and suggests harm towards an individual, making it offensive.\"\n}", "text": "All you need is to cut off it 's head and the rest of the problems will crumble away with ease", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text describes a violent act but does not contain offensive speech.", "processing_time": 1.299607276916504, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text describes a violent act but does not contain offensive speech.\"\n}", "text": "She was tortured and mutilated .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains derogatory and offensive language towards a specific racial group.", "processing_time": 1.2894062995910645, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains derogatory and offensive language towards a specific racial group.\"\n}", "text": "God, it is sickening to see these young lives taken by feral negroes .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains explicit hatred and derogatory content towards a specific group based on race.", "processing_time": 1.294881820678711, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains explicit hatred and derogatory content towards a specific group based on race.\"\n}", "text": "God bless them all , to hell with the blacks .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and discriminatory remarks towards white people based on their reproductive choices and political beliefs.", "processing_time": 1.6093270778656006, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and discriminatory remarks towards white people based on their reproductive choices and political beliefs.\"\n}", "text": "Whites have the following Too many abortions , too many mixed marriages , too many no kids marriages , and too many liberals .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains a racially insensitive and derogatory statement about black people.", "processing_time": 1.4303021430969238, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains a racially insensitive and derogatory statement about black people.\"\n}", "text": "- `` Something about black '' and the old favorite `` Once you go black you never go back ''", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains derogatory and offensive language towards individuals based on their race, referring to them as 'non WHITE garbage'.", "processing_time": 1.3408472537994385, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains derogatory and offensive language towards individuals based on their race, referring to them as 'non WHITE garbage'.\"\n}", "text": "Their stories have been told before , so instead of showing the pics of the non WHITE garbage that took from them , I am just going to post their pics so they will not be forgotten .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of the derogatory term 'kikes' and the suggestion of unequal treatment based on religion is offensive and discriminatory.", "processing_time": 1.348869800567627, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of the derogatory term 'kikes' and the suggestion of unequal treatment based on religion is offensive and discriminatory.\"\n}", "text": "While they are in prison the kikes should get the same food the regular prison population gets ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and anti-Semitic remarks, targeting a specific group based on religion", "processing_time": 1.2990684509277344, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and anti-Semitic remarks, targeting a specific group based on religion\"\n}", "text": "Only a f' n jew would have the nerve to hide away in a black country , a fugitive from US law , while making campaign promises at the same time as suing the company he defrauded .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains explicit derogatory language and promotes hatred towards black and brown individuals, making it offensive.", "processing_time": 1.8997857570648193, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains explicit derogatory language and promotes hatred towards black and brown individuals, making it offensive.\"\n}", "text": "Why do n't White Europeans and Americans grow a set of balls , and deport these Black and brown animals from their countries ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and hatred towards a specific group (Jewish people)", "processing_time": 1.2795400619506836, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and hatred towards a specific group (Jewish people)\"\n}", "text": "I 'm angry against myself for just letting this whole world drown into a jewish cesspool .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content, insults, and anti-Semitic remarks", "processing_time": 1.7113821506500244, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content, insults, and anti-Semitic remarks\"\n}", "text": "somebody``hcf''calledputin``ahero''!lololol!This``hero''finallytransformedformersovietjewnion in ``russian''judea!In this misery-torn country-are there-70 officially billionaries , tens f thousands millionaries , and tens of millions of poor and miserable who look for their pittance at the garbage places !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement compares physical features of different ethnic groups without containing explicit insults, threats, or hatred. It expresses an observation based on physical characteristics.", "processing_time": 1.3320684432983398, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement compares physical features of different ethnic groups without containing explicit insults, threats, or hatred. It expresses an observation based on physical characteristics.\"\n}", "text": "Comparing these pictures to pictures with serbs I do n't see much difference between them and albanians , the same Dinaric and Mediterranean subtypes .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses opinion about cultural heritage and pride, does not contain offensive speech", "processing_time": 1.270486831665039, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses opinion about cultural heritage and pride, does not contain offensive speech\"\n}", "text": "nobody said I like him and I dont see the reason to like him to be honest WE HAVE ENOUGH CULTURAL HERITAGE FOR HUNDREDS OF GENERATIONS WE SHOULD BE PROUD OF WHAT WE DID NOT OF WHAT SOME AUSTRIAN DID !!!", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains offensive speech with derogatory terms and promotes hatred towards a specific group (Palestinians).", "processing_time": 1.3009974956512451, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains offensive speech with derogatory terms and promotes hatred towards a specific group (Palestinians).\"\n}", "text": "Those poor kikes they cant even escape antisemetism in the country were it is punishable by death ( I am talking about palestinians )", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is a neutral inquiry about a political topic, without containing any offensive content.", "processing_time": 1.2617440223693848, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is a neutral inquiry about a political topic, without containing any offensive content.\"\n}", "text": "I thought there was talk not too long ago about <PERSON> extending the terms to stay in office ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech contains a comparison using 'high-maintenance' and 'crappy old East German Trabants', but it does not contain explicit insults, threats, or hatred towards specific groups or individuals.", "processing_time": 1.4107534885406494, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech contains a comparison using 'high-maintenance' and 'crappy old East German Trabants', but it does not contain explicit insults, threats, or hatred towards specific groups or individuals.\"\n}", "text": "They 're probably as high-maintenance as one of those crappy old East German Trabants , though .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful language towards individuals based on their religion and nationality.", "processing_time": 1.21305251121521, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful language towards individuals based on their religion and nationality.\"\n}", "text": "I could never picture the site of one of the women of the Holy Rus shacking up with some Islamic mud from that fithy , impoverished , and godforsaken mountainous * * * * hole !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is a factual representation of the demographic composition of European population and does not contain any offensive content.", "processing_time": 1.2234423160552979, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is a factual representation of the demographic composition of European population and does not contain any offensive content.\"\n}", "text": "Slavic peoples make up some 34 % of European population .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains offensive speech due to the explicit mention of a 2-year-old child dying from rape, which is a highly sensitive and disturbing topic.", "processing_time": 1.3315675258636475, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains offensive speech due to the explicit mention of a 2-year-old child dying from rape, which is a highly sensitive and disturbing topic.\"\n}", "text": "2-Year-Old <PERSON><PERSON> Of Rape , <PERSON><PERSON> 's <PERSON><PERSON><PERSON><PERSON> ' Charged - NNN Reporters Newsroom Forum Dekalb 2-Year-Old Dead ; Caregiver Charged Police Say Child Was Abused POSTED : 10 : 41 pm EDT October 2 , 2010 UPDATED : 11 : 51 pm EDT October 3 , 2010 Dekalb 2-Year-Old Dead ; Caregiver Charged - DeKalb County Headlines News Story - WGCL Atlanta", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains information about a murder suspect killing his estranged wife, which is offensive and violent.", "processing_time": 1.2484676837921143, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains information about a murder suspect killing his estranged wife, which is offensive and violent.\"\n}", "text": "Richland County deputies say <PERSON><PERSON><PERSON> killed his estranged wife , 38-year-old <PERSON><PERSON><PERSON> , at an apartment on Smallwood Road in Northeast Richland County Deputies : Murder Suspect Shoots at Officers , is Taken Into Custody wltx.com | Columbia , SC News , Weather and Sports |", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains explicit hatred and discrimination based on skin color, making it offensive and derogatory.", "processing_time": 1.3610475063323975, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains explicit hatred and discrimination based on skin color, making it offensive and derogatory.\"\n}", "text": "Keep Russia free from dark skinned beings !!!! thank you and please forgive me for mistakes .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, or derogatory content. It expresses a general observation about potential behavior.", "processing_time": 1.2864646911621094, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, or derogatory content. It expresses a general observation about potential behavior.\"\n}", "text": "They will kill there own just as fast as they would kill there enemy .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it is simply a number followed by an exclamation mark.", "processing_time": 1.2537355422973633, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it is simply a number followed by an exclamation mark.\"\n}", "text": "14 !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content. It merely mentions the absence of a mugshot picture on a page and refers to watching a video report.", "processing_time": 1.4930415153503418, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content. It merely mentions the absence of a mugshot picture on a page and refers to watching a video report.\"\n}", "text": "Oh yeah , no mugshot picture on the page of course , you have to watch the video report to find out it was yet another case of TNB .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it describes a criminal's actions and a columnist's opinion on the death penalty.", "processing_time": 1.3262474536895752, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it describes a criminal's actions and a columnist's opinion on the death penalty.\"\n}", "text": "The brutality of his crime and his lack of remorse made <PERSON> the `` model candidate for death in the gas house , '' one columnist wrote after his death sentence was upheld by the state 's highest court .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains offensive speech as it describes a physical attack on a teacher based on their race, which is a form of discrimination and hatred.", "processing_time": 1.6451551914215088, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains offensive speech as it describes a physical attack on a teacher based on their race, which is a form of discrimination and hatred.\"\n}", "text": "YouTube - Teacher Attacked for Being White 9 year old attacks white teacher , explicitly `` because she was white . ''", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is not offensive as it does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.2831294536590576, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is not offensive as it does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "I caught this on youtube , I 'm sure someone has posted this at some point on here but it deserves reiteration .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Comparing physical attributes and martial arts skills based on ethnicity is offensive and perpetuates stereotypes.", "processing_time": 1.2684600353240967, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Comparing physical attributes and martial arts skills based on ethnicity is offensive and perpetuates stereotypes.\"\n}", "text": "I do n't know about you but I rather fight a 2m swedish male than a 1.70m asian kung fu expert .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, addressing a young man in a neutral manner.", "processing_time": 1.1805922985076904, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, addressing a young man in a neutral manner.\"\n}", "text": "Now, young man !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech.", "processing_time": 1.1661632061004639, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech.\"\n}", "text": "2508", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains derogatory language and promotes discrimination based on race.", "processing_time": 1.1899216175079346, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains derogatory language and promotes discrimination based on race.\"\n}", "text": "So you would refuse even other Nordic whites fleeing the brown chaos in the south ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, or hatred. It merely discusses a topic related to individuals who refuse to surrender and tell the truth.", "processing_time": 1.3674700260162354, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, or hatred. It merely discusses a topic related to individuals who refuse to surrender and tell the truth.\"\n}", "text": "Interesting article about what to do with these evil persons who keep telling the truth and who don ´ t want to surrender ( in Danish )", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is not offensive as it simply states that sardines and anchovies are safe to consume.", "processing_time": 1.7057008743286133, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is not offensive as it simply states that sardines and anchovies are safe to consume.\"\n}", "text": "Sardines and anchovies , for example , are basically 100 % safe .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and anti-Semitic remarks", "processing_time": 1.5205276012420654, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and anti-Semitic remarks\"\n}", "text": "That has been proven many times over by the Jew media in the U.S !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses frustration with deception, not offensive speech", "processing_time": 1.6685254573822021, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses frustration with deception, not offensive speech\"\n}", "text": "Their lies are so thick and many that people do n't see the truth when it is in front of them !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of derogatory terms like 'servants/maids' can be offensive as it belittles and dehumanizes individuals based on their occupation.", "processing_time": 1.7228162288665771, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of derogatory terms like 'servants/maids' can be offensive as it belittles and dehumanizes individuals based on their occupation.\"\n}", "text": "Servents/maids , workers of some type ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, as it is a reference to a fictional character from 'The Lord of the Rings'.", "processing_time": 1.3875670433044434, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, as it is a reference to a fictional character from 'The Lord of the Rings'.\"\n}", "text": "Aragorn", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory content and promotes discrimination based on religion.", "processing_time": 1.2321906089782715, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory content and promotes discrimination based on religion.\"\n}", "text": "I would like to say and I can proudly say that there are no jews in Iceland , those that tried to get here were all sent back to Germany ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is neutral and does not contain any offensive content. It simply asks for information about an event on a lake.", "processing_time": 1.3824608325958252, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is neutral and does not contain any offensive content. It simply asks for information about an event on a lake.\"\n}", "text": "If anyone can tell what happened on this lake ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and promotes violence against LGBTQ+ individuals", "processing_time": 1.9802961349487305, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and promotes violence against LGBTQ+ individuals\"\n}", "text": "When the homos tried to have a fag pride parade in Serbia a few years ago they were beaten up .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The term 'idiotic' is derogatory and offensive as it insults someone's intelligence.", "processing_time": 1.9962940216064453, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The term 'idiotic' is derogatory and offensive as it insults someone's intelligence.\"\n}", "text": "Idiotic.", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive statement expressing a personal opinion on the topic of adopting kids", "processing_time": 1.25657057762146, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive statement expressing a personal opinion on the topic of adopting kids\"\n}", "text": "But as for them adopting kids , the answer is NO .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains a negative generalization about a specific ethnic group, implying a connection between ethnicity and criminal behavior.", "processing_time": 1.2768020629882812, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains a negative generalization about a specific ethnic group, implying a connection between ethnicity and criminal behavior.\"\n}", "text": "Ethnic crime never ends it seems .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text contains factual information about GDP data of Norway, Sweden, and Iceland, without any offensive content.", "processing_time": 2.087458610534668, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text contains factual information about GDP data of Norway, Sweden, and Iceland, without any offensive content.\"\n}", "text": "Norway : 3.3 ( 2001 ) , 3.9 ( 2002 ) , 3.5 ( 2003 ) , 1.8 ( 2004 ) . ( assuming population of 4.5 million ) Sweden : 2.6 ( 2001 ) , 3.7 ( 2002 ) , 3.5 ( 2003 ) , 2.6 ( 2004 ) . ( assuming population of 8.9 million ) Iceland is at about 0.2 .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory and hateful language towards the Islamic community.", "processing_time": 1.2820000648498535, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory and hateful language towards the Islamic community.\"\n}", "text": "It's people like this that are the face that represents the filth of Islam .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, or derogatory content. It expresses a concern about potential harm without directing hatred towards any specific group or individual.", "processing_time": 1.4030354022979736, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, or derogatory content. It expresses a concern about potential harm without directing hatred towards any specific group or individual.\"\n}", "text": "Providing From Finland does n't kick my ass and throw me overboard ... coming or going .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it simply discusses the change in desires of some individuals after living in Finland for a few years.", "processing_time": 1.3326265811920166, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it simply discusses the change in desires of some individuals after living in Finland for a few years.\"\n}", "text": "Some of them claim that they would like to go back home one day but after a few years in Finland they will no longer have this wish .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive remarks based on race and ethnicity, implying mixed-race individuals are undesirable", "processing_time": 1.2725763320922852, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive remarks based on race and ethnicity, implying mixed-race individuals are undesirable\"\n}", "text": "that prince who married that as<PERSON> is a freak. imagine his half and half kids growing up and trying to marry into other royal families", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it appears to be a random sequence of characters.", "processing_time": 1.2209804058074951, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it appears to be a random sequence of characters.\"\n}", "text": "<PERSON> 278", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.4174816608428955, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "It 's more fun that way , too .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language towards women and the LGBTQ+ community.", "processing_time": 1.173823595046997, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language towards women and the LGBTQ+ community.\"\n}", "text": "More likely they 're afraid of having to marry and deal with a snotty GI-Jane-dyke , only to be dragged into court and have the kids and house taken a few years later .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it is a question about the geographical classification of Finland and a sports event.", "processing_time": 1.341426134109497, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it is a question about the geographical classification of Finland and a sports event.\"\n}", "text": "Has anybody pointed out that Finland is n't part of Scandinavia yet ? ( <PERSON>r gick det i Finnkampen , vet ni det ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content towards Sweden and the UK, implying they are 'hell holes'", "processing_time": 1.5676875114440918, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content towards Sweden and the UK, implying they are 'hell holes'\"\n}", "text": "Its only a matter of time until we become a hell hole like Sweden or the UK .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit threat of violence, indicating offensive speech.", "processing_time": 1.1984264850616455, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit threat of violence, indicating offensive speech.\"\n}", "text": "Piece of * * * * deserves to be shot .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it is a casual observation and speculation about someone's appearance.", "processing_time": 1.2790625095367432, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it is a casual observation and speculation about someone's appearance.\"\n}", "text": "now that ye mention it he does look a bit like this traveller i knew. i wonder if we gave him a wash would he really be a white guy with red hair ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it simply expresses a sense of urgency regarding a potential threat.", "processing_time": 1.2850580215454102, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it simply expresses a sense of urgency regarding a potential threat.\"\n}", "text": "If we do n't act soon they will bread us out .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains explicit hatred and a derogatory reference towards Irish people, making it offensive.", "processing_time": 1.2951977252960205, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains explicit hatred and a derogatory reference towards Irish people, making it offensive.\"\n}", "text": "And meanwhile , <PERSON> is trying to kill off future generations of Irish .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains offensive language and derogatory remarks towards a specific group (people from another country)", "processing_time": 1.6113700866699219, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains offensive language and derogatory remarks towards a specific group (people from another country)\"\n}", "text": "On a short holiday , Go back and do something about your own country and stop putting crap on our doorstep !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content. It is a speculative question about potential conflicts between European countries regarding Guantanamo prisoners.", "processing_time": 2.3317079544067383, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content. It is a speculative question about potential conflicts between European countries regarding Guantanamo prisoners.\"\n}", "text": "Hey, do you think we are going to get into a fight between European countries about who all are going to get those Guantanamo prisoners ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory content and promotes racial stereotypes and fear of violence towards specific groups based on ethnicity.", "processing_time": 1.3170795440673828, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory content and promotes racial stereotypes and fear of violence towards specific groups based on ethnicity.\"\n}", "text": "I heard from friends in Boston this is true.Blacks will attack an Irish America with a shamrock tattoo.How long before this happens in Ireland ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains derogatory and discriminatory remarks towards specific racial groups, inciting hatred and promoting racial segregation.", "processing_time": 1.2711288928985596, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains derogatory and discriminatory remarks towards specific racial groups, inciting hatred and promoting racial segregation.\"\n}", "text": "So according to your logic since blacks have been in N.America for hundreds of yrs they should be allowed to stay ? and yes whites should leave africa .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text encourages cultural pride and peaceful demonstrations, promoting unity and inclusivity.", "processing_time": 1.2762959003448486, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text encourages cultural pride and peaceful demonstrations, promoting unity and inclusivity.\"\n}", "text": "If they have some marches and what not about showing pride in their culture you guys should go out and do the same If you could try and contact people you know in different cities and see if their willing to do the same .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory remarks towards individuals from a specific country and refugees, implying they lack skills and are unable to integrate into society.", "processing_time": 1.5230810642242432, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory remarks towards individuals from a specific country and refugees, implying they lack skills and are unable to integrate into society.\"\n}", "text": "how the hell are they going to integrate into Irish society ? They come from a dirt poor medieval country and have spent the last 3 years living in a refugee camp.what skills are they going to bring here ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses an opinion about the perceived leniency of Irish prisons, without containing explicit insults, threats, or hatred.", "processing_time": 1.3677382469177246, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses an opinion about the perceived leniency of Irish prisons, without containing explicit insults, threats, or hatred.\"\n}", "text": "An Irish prison is like a luxury hotel where they come and thats even if they get sent to prison and thats if they get do n't flee the country and thats if they get caught doing the crime ... so crime does pay in Ireland .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory content towards Jews, referring to them as 'scum'.", "processing_time": 1.266096591949463, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory content towards Jews, referring to them as 'scum'.\"\n}", "text": "Israel arrests him i bet <PERSON><PERSON> and <PERSON> are very happy.All students looking in see what scum the Jews are.Details here www.normanfinkelstein.com", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and insults towards individuals based on their political beliefs, indicating offensive speech.", "processing_time": 1.605320692062378, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and insults towards individuals based on their political beliefs, indicating offensive speech.\"\n}", "text": "The red scum will never wake up , ignorant lefties fight for `` their class '' when their making a mockery of their culture and heritage . commie scum ..", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement expresses a desire for individuals to be deported, which can be considered offensive and hostile.", "processing_time": 0.9500682353973389, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement expresses a desire for individuals to be deported, which can be considered offensive and hostile.\"\n}", "text": "Hopefully they will get deportation orders and be on the next flight back to Cairo .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech expressing a desire for a house.", "processing_time": 1.2508995532989502, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech expressing a desire for a house.\"\n}", "text": "I would love a house .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains offensive language and expresses hatred towards an individual, referring to them as a 'creature' and dismissing their thoughts.", "processing_time": 1.4475789070129395, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains offensive language and expresses hatred towards an individual, referring to them as a 'creature' and dismissing their thoughts.\"\n}", "text": "To hell with what this creature thinks anyway .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and negative stereotypes towards a specific group of people", "processing_time": 1.6631989479064941, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and negative stereotypes towards a specific group of people\"\n}", "text": "The problems start when you start to accommonadate these people in the first place ! , give an inch and they 'll take a mile ......", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory remarks towards a town and expresses hatred towards a religious group, inciting division and intolerance.", "processing_time": 1.452028751373291, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory remarks towards a town and expresses hatred towards a religious group, inciting division and intolerance.\"\n}", "text": "Tralee is a disgrace , the place looks like a town in africa nowThe day Ireland becomes a muslim country is the day i stop breathing !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains derogatory content against Islam by associating it with negative connotations like 'Islamization'. It promotes hatred and discrimination against a specific religious group.", "processing_time": 1.8176560401916504, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains derogatory content against Islam by associating it with negative connotations like 'Islamization'. It promotes hatred and discrimination against a specific religious group.\"\n}", "text": "YouTube - Brussels on 9/11 : For a free Europe , against Islamization", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory content towards foreigners, implying they are undeserving and disrespectful towards Irish families.", "processing_time": 1.03482985496521, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory content towards foreigners, implying they are undeserving and disrespectful towards Irish families.\"\n}", "text": "Its disgusting they these foreigners come over here , stamp their feet and jump above good Irish families and get what they want .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it is a statement about individuals' intentions regarding returning to Poland.", "processing_time": 1.354619026184082, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it is a statement about individuals' intentions regarding returning to Poland.\"\n}", "text": "Even if they do stop pouring in most of those that are here have no intention of going back to poland .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it simply describes someone's fashion choices and change in style.", "processing_time": 1.303781509399414, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it simply describes someone's fashion choices and change in style.\"\n}", "text": "During the early punk scene , he was always seen decked out in a Union flag blazer , then all a sudden , an instant nationality transplant .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of derogatory terms like 'Ape' or 'Gorilla' to refer to a person is offensive and dehumanizing.", "processing_time": 1.41117525100708, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of derogatory terms like 'Ape' or 'Gorilla' to refer to a person is offensive and dehumanizing.\"\n}", "text": "When I look at the Zimbawee 's Mugabe , I think `` Ape '' or `` Gorilla '' .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains a derogatory implication by suggesting someone should return to their place of origin, which can be offensive and discriminatory.", "processing_time": 1.3498969078063965, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains a derogatory implication by suggesting someone should return to their place of origin, which can be offensive and discriminatory.\"\n}", "text": "Back to where ya came from !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses hope for someone's recovery and gratitude for not having certain animals in a country, without containing offensive content.", "processing_time": 1.317155361175537, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses hope for someone's recovery and gratitude for not having certain animals in a country, without containing offensive content.\"\n}", "text": "You got that right in one go.Lets hope he gets well soon and on his way home If you do n't have these apes in your great Country then count your blessings", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses disappointment with being taken to a Chinese restaurant, but does not contain explicit insults, threats, or hatred", "processing_time": 1.3722808361053467, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses disappointment with being taken to a Chinese restaurant, but does not contain explicit insults, threats, or hatred\"\n}", "text": "After knocking back a few guinness we got pretty hungry and he said he knew a great place to go get some food , i was disgusted when he brought me to a chinese restaurant .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and expresses hatred towards specific groups based on race.", "processing_time": 1.5284209251403809, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and expresses hatred towards specific groups based on race.\"\n}", "text": "the white race will soon be on the brink of being a minority in a few years if nothing is done to stop these vile hordes invading our lands , I for one do not want to live in a city like Bradford or Birmingham where we are outnumbered 2 to 1 or worse", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and hatred towards a specific group", "processing_time": 1.2725157737731934, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and hatred towards a specific group\"\n}", "text": "Its a disgrace my sister has been on the waiting list for 6 years and their giving away houses to these scumbags", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit derogatory and offensive language towards specific racial groups and individuals.", "processing_time": 1.288055658340454, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit derogatory and offensive language towards specific racial groups and individuals.\"\n}", "text": "I heard about it is it about that white guy that killed that race traitor slut and her nig nog boyfriend ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory content towards people advocating for foreign aid and implies lack of care towards Irish homeless and elderly people, inciting division and negativity.", "processing_time": 1.3254377841949463, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory content towards people advocating for foreign aid and implies lack of care towards Irish homeless and elderly people, inciting division and negativity.\"\n}", "text": "These people who want foreign aid increased are the same people who walk past irish people living rough on the streets and they do n't care about the old people who the government ignores .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory content towards the IRA, labeling them as criminals, which can be considered offensive.", "processing_time": 1.0471951961517334, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory content towards the IRA, labeling them as criminals, which can be considered offensive.\"\n}", "text": "It just goes to show that the IRA are just criminals and not freedom fighters as they claim .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive greeting and inquiry about well-being, no derogatory or hateful content", "processing_time": 1.7219548225402832, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive greeting and inquiry about well-being, no derogatory or hateful content\"\n}", "text": "<PERSON>tha math dhuibh agus dè tha dol agad fh<PERSON>in , a Phàdruig/Hello Pat , how are you ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and racial discrimination towards non-white individuals", "processing_time": 1.4131567478179932, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and racial discrimination towards non-white individuals\"\n}", "text": "So sick of the way its working , I remember when I was growing up in Winnipeg the little shops were all white runned and owned and would hire people from the community and it was great it helped people and kept streets safe even though me and others would cause some bull * * * * it wasnt anything to how it is now .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing confusion and uncertainty without any offensive content", "processing_time": 4.568206787109375, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing confusion and uncertainty without any offensive content\"\n}", "text": "Now i just dont know what to do or think anymore .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses a call to action for the next generation to take charge politically, without containing any offensive language or derogatory content", "processing_time": 1.4056148529052734, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses a call to action for the next generation to take charge politically, without containing any offensive language or derogatory content\"\n}", "text": "18 times in the last 20 years It 's time for the next generation to fight back to regain our country ..... the baby boomers will lose it for us if we do nothing .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive and simply expresses positive opinions about a geographical location and personal activities.", "processing_time": 1.4245381355285645, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive and simply expresses positive opinions about a geographical location and personal activities.\"\n}", "text": "nice to see another maritimer , the south shore is a beautiful place , i ride down that way a lot on my harley , i live in the annapolis valley .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement discusses immigration policies and requirements for entering Canada, without containing any offensive content.", "processing_time": 1.2636141777038574, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement discusses immigration policies and requirements for entering Canada, without containing any offensive content.\"\n}", "text": "There's no way to stop them from coming to Canada all that is needed is proof of ownership of a house in India , africa etc and they can enter in on a tourist visa .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains offensive speech by referring to targeting a minority group (cops) with derogatory language.", "processing_time": 1.253220796585083, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains offensive speech by referring to targeting a minority group (cops) with derogatory language.\"\n}", "text": "Should the guy who sang cop killer also have a trial , because he tragted a minority , cops .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing an opinion about the likely author's age", "processing_time": 1.2337567806243896, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing an opinion about the likely author's age\"\n}", "text": "It was probably written by a 15 year old .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist language towards white girls and their partners", "processing_time": 1.317685842514038, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist language towards white girls and their partners\"\n}", "text": "I noticed a lot of fat little white girls with monkey boyfriends .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains a suggestion to desecrate a religious site, which is offensive and disrespectful.", "processing_time": 1.196087121963501, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains a suggestion to desecrate a religious site, which is offensive and disrespectful.\"\n}", "text": "You can stop this by getting a dead pig and bury it underground where the mosque will be built .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply discusses the speaker's inability to find much information about a subject and mentions the subject's presence in Britain.", "processing_time": 1.358757495880127, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply discusses the speaker's inability to find much information about a subject and mentions the subject's presence in Britain.\"\n}", "text": "I' v checked them out but could n't find much but apparently they have presence in Britain .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement expresses racial prejudice and suggests that black players are inferior representatives of their nations, which is offensive and discriminatory.", "processing_time": 2.566511869430542, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement expresses racial prejudice and suggests that black players are inferior representatives of their nations, which is offensive and discriminatory.\"\n}", "text": "Seeing the teams of white nations have black players in their ranks is a depressing site to see.They should be representing the best of our nations not the worst", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of the term 'Groid' is a derogatory racial slur, making the speech offensive and disrespectful towards individuals based on their race.", "processing_time": 1.6204068660736084, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of the term 'Groid' is a derogatory racial slur, making the speech offensive and disrespectful towards individuals based on their race.\"\n}", "text": "Did they put that Groid 15 next to <PERSON> just to make <PERSON> look at least half-human ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory terms like 'locust' and 'parasite' to describe immigrants, which is offensive and dehumanizing.", "processing_time": 1.3592197895050049, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory terms like 'locust' and 'parasite' to describe immigrants, which is offensive and dehumanizing.\"\n}", "text": "What I do know is that I live on a small Island and it seems that a lot of people from various countries in the world are willing to desert their homelands and their families just to come here , in my mind that tells me a lot about said people and their home countries , most of all it highlights their weaknesses of how their own countries have let them down and how they feel the need to feed off somebody elses country , how should we English interpret this without sounding rude ??? , locust ??? , parasite ??? , you tell me .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist language towards African people", "processing_time": 1.2169010639190674, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist language towards African people\"\n}", "text": "I just need to turn on a tv and I see it advertised everywhere , mostly African apes with attractive white woman .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it simply expresses a personal struggle with grammar and education under a specific political party.", "processing_time": 1.399885654449463, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it simply expresses a personal struggle with grammar and education under a specific political party.\"\n}", "text": "I do get my Theirs , theres and they 're , mixed up sometimes but i was educated under Labours rule so i cant be completly to blame .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and hate speech towards a specific group (zionists and DUP)", "processing_time": 1.2693426609039307, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and hate speech towards a specific group (zionists and DUP)\"\n}", "text": "The Official Website of the British People 's Party A few unpleasant truths re. the zionist ass lickers of the DUP", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and insults towards a specific individual, <PERSON><PERSON>, referring to him as a 'useless savage'", "processing_time": 1.4012763500213623, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and insults towards a specific individual, <PERSON><PERSON>, referring to him as a 'useless savage'\"\n}", "text": "The problem is that through western elites we have to feed and look after useless savages like malema .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and implies harm towards a specific individual or group", "processing_time": 0.8153858184814453, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and implies harm towards a specific individual or group\"\n}", "text": "If that stopped the worthless thing would starve !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, simply introducing a new username and expressing familiarity with past interactions", "processing_time": 1.5904450416564941, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, simply introducing a new username and expressing familiarity with past interactions\"\n}", "text": "i used to post on here a few years back as kruger but for some reason i cant register with that name so here goes with the new one 88 to all i know and to those i will meet", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The mention of 'blackshirts' associated with <PERSON><PERSON> is historically linked to fascist and racist ideologies, making the speech offensive.", "processing_time": 1.6324207782745361, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The mention of 'blackshirts' associated with <PERSON><PERSON> is historically linked to fascist and racist ideologies, making the speech offensive.\"\n}", "text": "Its time for the return of <PERSON><PERSON> 's blackshirts .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it simply describes a surprising situation at a school.", "processing_time": 1.2012913227081299, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it simply describes a surprising situation at a school.\"\n}", "text": "I have to say I was shocked when he said the kids at his ( Junior ) school spoke 31 different languages and teachers have had to devise a system of hand signals to communicate !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses concern about political issues overshadowing racial ones, without containing explicit insults, threats, or hatred", "processing_time": 1.3262653350830078, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses concern about political issues overshadowing racial ones, without containing explicit insults, threats, or hatred\"\n}", "text": "Our country is being over run and people let it happen just because the political issue here seens to take over the racial one .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and racial stereotypes towards black children", "processing_time": 1.6298370361328125, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and racial stereotypes towards black children\"\n}", "text": "i do this and the looks i get out of disgust are regular but i happily carry on walking and laugh them off , they think showing me a few pictures of negro kids on a poster without a burger is going to make me want to give money to them ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The mention of 'Stormfront' indicates association with a white supremacist website known for hate speech and promoting discrimination.", "processing_time": 1.4281563758850098, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The mention of 'Stormfront' indicates association with a white supremacist website known for hate speech and promoting discrimination.\"\n}", "text": "Hello Everyone on Stormfront Have heard a lot of good things about this forum so I have decided to take the plunge .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is a factual comparison between different types of cats, not containing any offensive content.", "processing_time": 1.3053910732269287, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is a factual comparison between different types of cats, not containing any offensive content.\"\n}", "text": "Lions , Tigers , Cheetahs , etc are all cats but they ai n't the same .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory remarks based on ethnicity and promotes hatred towards a specific group, which is offensive.", "processing_time": 1.378021001815796, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory remarks based on ethnicity and promotes hatred towards a specific group, which is offensive.\"\n}", "text": "The chairman of the FA is one <PERSON> and , yes , he is a member of a certain tribe that delight in bringing diversity and the benefits of multi-culturalism and ' anti-racism ' , along with boatloads of third-worlders to Britain .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses frustration towards the Olympics being constantly discussed, but does not contain explicit insults, threats, or hatred.", "processing_time": 1.3876190185546875, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses frustration towards the Olympics being constantly discussed, but does not contain explicit insults, threats, or hatred.\"\n}", "text": "I'm in Australia and I want it to be done and over with , my mum is constantly going on about the Olympics like it 's a big deal .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply asks about a sports tournament in Japan.", "processing_time": 1.3107998371124268, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply asks about a sports tournament in Japan.\"\n}", "text": "Yes, have you seen the Pancrase tornouments in Japan ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses a preference against assimilation, which is not inherently offensive but rather reflects a desire to maintain cultural identity.", "processing_time": 1.3494930267333984, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses a preference against assimilation, which is not inherently offensive but rather reflects a desire to maintain cultural identity.\"\n}", "text": "We do not want `` assimilation '' .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses frustration about limitations on free speech, without containing explicit insults, threats, or hatred.", "processing_time": 1.106785535812378, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses frustration about limitations on free speech, without containing explicit insults, threats, or hatred.\"\n}", "text": "Dare to speak out in a country with supposed free speech and you 're labelled a bigot !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit derogatory content and incites hatred towards ethnic groups", "processing_time": 1.2433021068572998, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit derogatory content and incites hatred towards ethnic groups\"\n}", "text": "You Anglo-Americans are all welcome to England if you can perform the ' small ' task of riding London of it 's ethnics !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing gratitude and camaraderie", "processing_time": 1.2861909866333008, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing gratitude and camaraderie\"\n}", "text": "Thank you brother .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses doubt about the accuracy of numbers, followed by an opinion. Not offensive.", "processing_time": 1.27225661277771, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses doubt about the accuracy of numbers, followed by an opinion. Not offensive.\"\n}", "text": "I was sure that they were lying about the numbers and it 's must be the same in every country IMO .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains offensive speech with derogatory terms ('rat infested', 'enrichers') directed at specific groups (cities).", "processing_time": 2.131359577178955, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains offensive speech with derogatory terms ('rat infested', 'enrichers') directed at specific groups (cities).\"\n}", "text": "Of course the county is lovely , in facts its gods country in our book BUT the cities are rat infested with enrichers , just how the country is isnt it .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech expresses a political preference without containing offensive content.", "processing_time": 1.3038890361785889, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech expresses a political preference without containing offensive content.\"\n}", "text": "I 'll post as much as I can , but I 'm more of a BNP supporter than an expert .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit references to violence and hatred towards specific groups, encouraging further aggression", "processing_time": 1.3189520835876465, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit references to violence and hatred towards specific groups, encouraging further aggression\"\n}", "text": "Here's an example of red violence against patriotic St George 's Day marchers and a red site encouraging even more of it the next year . Stop the March for England 2013 ( from Rough Music ) | Brighton Anti-fascists", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and expresses hatred towards a specific group", "processing_time": 1.344085693359375, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and expresses hatred towards a specific group\"\n}", "text": "Thanks to the imported filth that , like our land under its current occupation .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it is a neutral statement about a search query.", "processing_time": 1.4229791164398193, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it is a neutral statement about a search query.\"\n}", "text": "I was trying to find out if the Egyptians were white or not and my search brought me to a thread on this site .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing a desire for a quote from someone", "processing_time": 1.2620506286621094, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing a desire for a quote from someone\"\n}", "text": "Its got to be a quote from someone .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory language and stereotypes towards Nigerian individuals, which is offensive and disrespectful.", "processing_time": 1.462822675704956, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory language and stereotypes towards Nigerian individuals, which is offensive and disrespectful.\"\n}", "text": "There have been times when I have walked past some nigerian or skinny and they will cough with hand at their side without care for those around them .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit calls for violence, deportation, and discrimination based on race, which are offensive and hateful.", "processing_time": 1.5654304027557373, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit calls for violence, deportation, and discrimination based on race, which are offensive and hateful.\"\n}", "text": "Its about time we as a nation take matters into our own hands , over throw the government stop the white genocide deport all immigrants and establish a government to help indigenous white Britons rebuild this country .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains a threat of locking down and confining individuals, which is considered offensive and threatening behavior.", "processing_time": 1.3386220932006836, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains a threat of locking down and confining individuals, which is considered offensive and threatening behavior.\"\n}", "text": "Any bother u go on lock down no one gets out the cells so its not that bad prison is 24 hour boredom ..", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply expresses difficulty in finding time to meet like-minded people for coffee.", "processing_time": 2.7169957160949707, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply expresses difficulty in finding time to meet like-minded people for coffee.\"\n}", "text": "I've tried to meet for coffee with a few people but everybody is really spread out , it 's tough to find the time to go out and meet like minded people .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.4895169734954834, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "If you come down n ' around Sacramento California area send me a message .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of derogatory terms like 'negro', 'race-mixer', and 'homo' is offensive and promotes hatred and discrimination towards specific groups based on race and sexual orientation.", "processing_time": 1.4967906475067139, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of derogatory terms like 'negro', 'race-mixer', and 'homo' is offensive and promotes hatred and discrimination towards specific groups based on race and sexual orientation.\"\n}", "text": "Hi i need some information to understand the meaning of the negrogreek homo and the race-mixer tag of the HELLENIC THREAD .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and hatred towards specific groups (ZOG)", "processing_time": 1.2474663257598877, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and hatred towards specific groups (ZOG)\"\n}", "text": "<PERSON> <PERSON> is a German patriot and musician who , in the sick minds of ZOG , committed the heinious crime of producing music for a local school .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit hatred and derogatory content towards Muslims, both living and deceased.", "processing_time": 1.908329963684082, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit hatred and derogatory content towards Muslims, both living and deceased.\"\n}", "text": "Hi everyone .... I 'm from Slovenia and I utterly hate every living muslim in this world ....... and I despise even the dead ones", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing doubt about visibility to unregistered users", "processing_time": 1.2991323471069336, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing doubt about visibility to unregistered users\"\n}", "text": "If you are not registered I think that maybe you will not be able to see this .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, just a description of an ice cave and the cold temperature inside.", "processing_time": 1.3752927780151367, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, just a description of an ice cave and the cold temperature inside.\"\n}", "text": "Lets keep it that way and not destroy it Check out this ice cave in Dobšiná Its frigging cold in there", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit derogatory content and hatred towards specific groups (Ukrainians, Jews, Muslims, and Russians)", "processing_time": 1.3449490070343018, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit derogatory content and hatred towards specific groups (Ukrainians, Jews, Muslims, and Russians)\"\n}", "text": "If I was Ukrainian , I 'd sign a pact with devil himself to get rid of jewdo-islam Putinistan AKA Russian Federation ....", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory term 'libtards' and dismissive attitude towards a group of people", "processing_time": 1.3932538032531738, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory term 'libtards' and dismissive attitude towards a group of people\"\n}", "text": "Too many libtards , I would n't waste my time there .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language towards a specific group based on race.", "processing_time": 1.3473472595214844, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language towards a specific group based on race.\"\n}", "text": "Blacks in South Africa wondering why their country is going to hell when they put Blacks in charge .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech expressing frustration or annoyance without insults, threats, or hatred", "processing_time": 1.2439343929290771, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech expressing frustration or annoyance without insults, threats, or hatred\"\n}", "text": "Please stop this nonsense all of you !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory content and promotes violence towards individuals in Africa.", "processing_time": 2.159109115600586, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory content and promotes violence towards individuals in Africa.\"\n}", "text": "They are beating each other to death everywhere , just look what they do to each other in Africa .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses a desire to meet new people and potentially find a romantic partner of a specific race, which is not inherently offensive.", "processing_time": 1.3698086738586426, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses a desire to meet new people and potentially find a romantic partner of a specific race, which is not inherently offensive.\"\n}", "text": "Hey, I 'm looking to meet new people maybe find myself a nice white boy .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply describes the user's cat named <PERSON><PERSON><PERSON> without containing any insults, threats, or derogatory content.", "processing_time": 1.3870255947113037, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply describes the user's cat named <PERSON><PERSON><PERSON> without containing any insults, threats, or derogatory content.\"\n}", "text": "saysay.jpg say say with a candle 2.jpg This is my nearly 3 year old cat called <PERSON><PERSON><PERSON> .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language towards Muslims and objectifies women", "processing_time": 1.20894193649292, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language towards Muslims and objectifies women\"\n}", "text": "I got sloppy seconds after some Muslims used this girl .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The mention of chloroform in the context of running quietly suggests potential harmful intent or actions, making the speech offensive.", "processing_time": 1.2467303276062012, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The mention of chloroform in the context of running quietly suggests potential harmful intent or actions, making the speech offensive.\"\n}", "text": "The ability to run really quietly and lots of chloroform .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, contains personal information and location details", "processing_time": 1.2344601154327393, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, contains personal information and location details\"\n}", "text": "haha; im a 19 y.o white male from newcastle area - but im moving back to sydney very soon ( west sydney im afraid lol )", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is non-offensive, expressing well wishes and positivity towards a group of people.", "processing_time": 1.2971694469451904, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is non-offensive, expressing well wishes and positivity towards a group of people.\"\n}", "text": "To me and the rest of my commrades on this wonderfull White Day !!! I hope you have loads of fun and be safe !!!!", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain offensive speech, it simply describes a situation where children can now leave their bicycles in the yard without fear.", "processing_time": 1.3096559047698975, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain offensive speech, it simply describes a situation where children can now leave their bicycles in the yard without fear.\"\n}", "text": "The only Mexican in town has since moved out and kids can now leave their bicycles in the yard without fear .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it is a statement about interpreting someone's feelings based on their actions.", "processing_time": 1.254258155822754, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it is a statement about interpreting someone's feelings based on their actions.\"\n}", "text": "If her eyes linger ................................... she 's yours .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is not offensive as it expresses a positive sentiment about meeting someone from a specific place.", "processing_time": 1.3575847148895264, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is not offensive as it expresses a positive sentiment about meeting someone from a specific place.\"\n}", "text": "It is always nice to meet another person with roots in Kansas .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech expressing a desire to have children and the challenge of finding a partner for that purpose.", "processing_time": 1.2712829113006592, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech expressing a desire to have children and the challenge of finding a partner for that purpose.\"\n}", "text": "I want to have 3 children ! 2 girls and 1 boy but the hardest part now is i got to try and find someone to make them with , but so far no luck", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing an opinion about unique individuals", "processing_time": 1.3723130226135254, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing an opinion about unique individuals\"\n}", "text": "Only the crazy ones stay .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, as it contains factual information about the specifications of a boat and its motor.", "processing_time": 1.1219754219055176, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, as it contains factual information about the specifications of a boat and its motor.\"\n}", "text": "A 750 lb boat with a 280 hp motor .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply describes the location of a boat without containing any insults, threats, or derogatory content.", "processing_time": 1.7605924606323242, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply describes the location of a boat without containing any insults, threats, or derogatory content.\"\n}", "text": "My buddy 's boat is anchored beside it .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, or derogatory content. It merely expresses a comparison between two groups based on a specific trait.", "processing_time": 1.4350366592407227, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, or derogatory content. It merely expresses a comparison between two groups based on a specific trait.\"\n}", "text": "Of course , they are more virile than us , so that would n't happen would it ?!!!", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses personal preference without offensive content", "processing_time": 2.2122983932495117, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses personal preference without offensive content\"\n}", "text": "I have a dominant personality and I look for the same thing in girls .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is a friendly and casual conversation about meeting up with someone in New Jersey and potentially visiting the recipient during the summer.", "processing_time": 1.4109241962432861, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is a friendly and casual conversation about meeting up with someone in New Jersey and potentially visiting the recipient during the summer.\"\n}", "text": "I can get a ride to see you some time this summer if that is cool i need to get up with someone in NJ as it is his name is <PERSON><PERSON> i have talk to him but i want to meet him so when i go to see him I can stop to see you if that is ok", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is friendly and non-offensive, simply expressing a desire to connect and chat with someone.", "processing_time": 1.4441399574279785, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is friendly and non-offensive, simply expressing a desire to connect and chat with someone.\"\n}", "text": "hey whats up i 'm new on here and i would like to have someone to talk to so hit me back for whatever", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains a clear expression of promoting hate and intolerance, which is offensive.", "processing_time": 1.0244109630584717, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains a clear expression of promoting hate and intolerance, which is offensive.\"\n}", "text": "Maybe spread some HATE and intolerance ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it is a neutral statement about sharing pictures on MSN.", "processing_time": 1.2174160480499268, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it is a neutral statement about sharing pictures on MSN.\"\n}", "text": "Everyone on my MSN gets all manner of pics ( ) but I 've never put any up here so here goes Some pics from about a year ago and a small anigif from a week ago heh Robin", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is friendly and positive, expressing a desire to communicate with the recipient. It does not contain any offensive content.", "processing_time": 2.422056198120117, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is friendly and positive, expressing a desire to communicate with the recipient. It does not contain any offensive content.\"\n}", "text": "hi read your post and my name is jam<PERSON> from australia , and would like to well write to such a pleasent person as yourself have a nice day !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, describes a personal experience without containing any offensive content", "processing_time": 1.3014209270477295, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, describes a personal experience without containing any offensive content\"\n}", "text": "I used to enter horse competitions until I was thrown off one and broke my leg .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it simply states a factual observation about the increase in people from a specific area joining the board.", "processing_time": 1.6049058437347412, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it simply states a factual observation about the increase in people from a specific area joining the board.\"\n}", "text": "There have been more people coming on this board that live in the central Wisconsin area since I have started coming on here .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement expresses support for white nationalism, which is considered offensive and promotes discrimination based on race.", "processing_time": 1.6365764141082764, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement expresses support for white nationalism, which is considered offensive and promotes discrimination based on race.\"\n}", "text": "They 're music is one of the reasons I decided to become a white nationalist in the first place .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it simply states a fact about the speaker's appearance and plans to attend a show.", "processing_time": 1.355006456375122, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it simply states a fact about the speaker's appearance and plans to attend a show.\"\n}", "text": "I 'll be at that show AND I 'm a brunette .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory remarks towards Mexicans and Hispanics, suggesting they are undercutting prices and implying they are not 'good working people'.", "processing_time": 1.3611133098602295, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory remarks towards Mexicans and Hispanics, suggesting they are undercutting prices and implying they are not 'good working people'.\"\n}", "text": "Someone Please Tell me what can be done or which people to tell that the Mexicans and or other hispanics here are undercutting price quotes on Good working People .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and racial discrimination towards a specific group", "processing_time": 1.243363857269287, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and racial discrimination towards a specific group\"\n}", "text": "DuetschGirlTX, No I live in a tiny ass town where i am only one of 100 white folks ( maybe less ) . near San Antonio. there are so many dirts around and they all stick together. so when it comes to a White Girl in this town i gotta have my own back ALWAYS Is that where you are ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, contains factual information about the speaker's age and location", "processing_time": 1.8172237873077393, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, contains factual information about the speaker's age and location\"\n}", "text": "<PERSON>, I am 26yo from Glendale AZ .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it describes job responsibilities related to selling sporting goods and assisting customers, without containing any insults, threats, or derogatory content.", "processing_time": 1.4558818340301514, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it describes job responsibilities related to selling sporting goods and assisting customers, without containing any insults, threats, or derogatory content.\"\n}", "text": "The work involves : selling sporting goods and assisting customers with questions or problems .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is non-offensive as it expresses personal preferences and aspirations without containing any insults, threats, or derogatory content.", "processing_time": 1.398625135421753, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is non-offensive as it expresses personal preferences and aspirations without containing any insults, threats, or derogatory content.\"\n}", "text": "I do n't go line dancing but I listen to country and want to live on a ranch or farm", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content. It simply expresses a preference for a specific type of partner.", "processing_time": 1.8496360778808594, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content. It simply expresses a preference for a specific type of partner.\"\n}", "text": "KC skin boy looking for KC skin girl .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and anti-Semitic slur, making it offensive.", "processing_time": 1.4738025665283203, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and anti-Semitic slur, making it offensive.\"\n}", "text": "what about it. that 's exactly what I said and I meant every word he thought I might and that was enough and with you two talking behind my back like some jew what do you expect me to say have a nice life .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing doubt or disagreement without insults or derogatory content", "processing_time": 1.8453986644744873, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing doubt or disagreement without insults or derogatory content\"\n}", "text": "I do n't think so .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content. It merely expresses a personal experience and opinion about wrestling animals.", "processing_time": 1.4362537860870361, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content. It merely expresses a personal experience and opinion about wrestling animals.\"\n}", "text": "I lived in texas when i was younger , i know how tough some things can get . `` rasslin animals '' is half the fun", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it is a suggestion to search for information online.", "processing_time": 2.0584774017333984, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it is a suggestion to search for information online.\"\n}", "text": "Just Google it and you 'll see what I mean .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing a decision to continue a debate privately", "processing_time": 2.2686350345611572, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing a decision to continue a debate privately\"\n}", "text": "I have decided to take this debate to pm .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory language towards immigrants, referring to them as 'illegal aliens' and suggesting the use of military force against them.", "processing_time": 1.0911736488342285, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory language towards immigrants, referring to them as 'illegal aliens' and suggesting the use of military force against them.\"\n}", "text": "Living in a boarder state , Texas , the problems of illegal aliens is , naturally , a stark reality , and let me add that the only solution that I see is the implimentation of U.S. Troops as guards on the Mexican boarder .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text promotes and justifies the continuation of hate, which is offensive and harmful.", "processing_time": 1.4363555908203125, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text promotes and justifies the continuation of hate, which is offensive and harmful.\"\n}", "text": "Although many have asked us to put it aside in politics , this thread is for all those who see where <PERSON><PERSON><PERSON> FITS IN and why WE NEED THE HATE WAVE TO CONTINUE .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing a personal experience of not being active on the platform for a long time.", "processing_time": 1.4476068019866943, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing a personal experience of not being active on the platform for a long time.\"\n}", "text": "Hi i have not been on here in ages .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech expresses a personal opinion about Nordic people wanting a pure Nordic race, without containing explicit insults, threats, or hatred.", "processing_time": 2.182126522064209, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech expresses a personal opinion about Nordic people wanting a pure Nordic race, without containing explicit insults, threats, or hatred.\"\n}", "text": "No I do n't support Nordic Superiority , but I have no problem with Nordic people who want a pure Nordic race to exist .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory language towards a specific group based on race, which is offensive and inappropriate.", "processing_time": 1.2455437183380127, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory language towards a specific group based on race, which is offensive and inappropriate.\"\n}", "text": "if they have the same help here in Quebec , i would like too say congratulation pitbull-<PERSON><PERSON><PERSON><PERSON> and i hope you have lot of new white resident in your city", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains derogatory content and implies discrimination based on race.", "processing_time": 1.2189948558807373, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains derogatory content and implies discrimination based on race.\"\n}", "text": "Why would they let blacks live ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.345233678817749, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it does not contain any explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "88 all , this is my first post and i 've read this whole thread so i guess I have a few comment 's , let 's start , shall we ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory and discriminatory remarks towards Black and Asian immigrants, suggesting they are a threat to the gene pool based on their breeding patterns.", "processing_time": 1.795013189315796, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory and discriminatory remarks towards Black and Asian immigrants, suggesting they are a threat to the gene pool based on their breeding patterns.\"\n}", "text": "Black and Asian immigrants who are coming in and those who were borne here and their breading patterns are the threat to our gene pool and not other white Europeans such as polish people .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing a personal opinion about a book choice.", "processing_time": 1.2584946155548096, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing a personal opinion about a book choice.\"\n}", "text": "This is my next read .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory language and promotes discrimination based on race, which is offensive and inappropriate.", "processing_time": 1.3103773593902588, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory language and promotes discrimination based on race, which is offensive and inappropriate.\"\n}", "text": "Sadly, I think the government would step in if we tried to stop the lesser races from moving into our land .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing a personal opinion about a book choice.", "processing_time": 1.7505149841308594, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing a personal opinion about a book choice.\"\n}", "text": "This is my next read .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, no explicit insults, threats, or derogatory content", "processing_time": 1.275101661682129, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, no explicit insults, threats, or derogatory content\"\n}", "text": "Absolutely.", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is a reference to a comment made by <PERSON> in 1998, suggesting that <PERSON> had a connection with the African American community, not intended as offensive.", "processing_time": 1.1773886680603027, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is a reference to a comment made by <PERSON> in 1998, suggesting that <PERSON> had a connection with the African American community, not intended as offensive.\"\n}", "text": "Was n't <PERSON> the first `` black '' president ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, no derogatory content or insults detected.", "processing_time": 1.283431053161621, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, no derogatory content or insults detected.\"\n}", "text": "TY!", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, as it simply mentions an upcoming update on a drive back to Florida", "processing_time": 1.4381990432739258, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, as it simply mentions an upcoming update on a drive back to Florida\"\n}", "text": "BTW .. update coming soon with info on my drive back to Florida .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit derogatory content and incites hatred towards non-white individuals.", "processing_time": 1.290625810623169, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit derogatory content and incites hatred towards non-white individuals.\"\n}", "text": "When the non-whites come we should stand together and send em packing , like they do to us .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains offensive language and derogatory remarks towards a specific individual based on their race and body size.", "processing_time": 1.348041296005249, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains offensive language and derogatory remarks towards a specific individual based on their race and body size.\"\n}", "text": "I got stuck next to a really huge black woman who stunk to high heaven , and spent the entire ride with my face as close to the window as I could get .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains offensive content by promoting discrimination and hatred based on race.", "processing_time": 1.3152122497558594, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains offensive content by promoting discrimination and hatred based on race.\"\n}", "text": "Teach our men to be men again and defend our countries and women folk against the non white .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist remarks towards black individuals, promoting hatred and discrimination.", "processing_time": 1.49593186378479, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist remarks towards black individuals, promoting hatred and discrimination.\"\n}", "text": "Most white girls are taught and brainwashed to love black guys , only way is to properly educate them and show them what savages they truly are .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains offensive language and racial stereotypes, referring to individuals based on their race in a derogatory manner.", "processing_time": 1.350809097290039, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains offensive language and racial stereotypes, referring to individuals based on their race in a derogatory manner.\"\n}", "text": "White town saved by the magic negroes of Afreakkaland .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language towards a specific group of people based on race.", "processing_time": 1.281712293624878, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language towards a specific group of people based on race.\"\n}", "text": "Actually Monkeys and Apes are more noble creatures and rarely act violent it really is unfare to the Monkeys and Apes to be compared with subhuman Negroids who are by contrast very murderous .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language towards Nigerians, inciting violence.", "processing_time": 1.2384507656097412, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language towards Nigerians, inciting violence.\"\n}", "text": "Hopefully they will leave Y<PERSON> alone and just deal with the Nigerians when they start chimping out in the streets .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory language and promotes racial hatred by dehumanizing a specific group based on their race.", "processing_time": 1.67569899559021, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory language and promotes racial hatred by dehumanizing a specific group based on their race.\"\n}", "text": "I just refer to them as primates as through THEIR evolution process they are just a little bit higher on the evolutionary chain which makes them all the more dangerous to whites , than any of their animalistic cousins .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses agreement and a warning about dealing with certain individuals, without containing explicit insults, threats, or derogatory content.", "processing_time": 1.7183496952056885, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses agreement and a warning about dealing with certain individuals, without containing explicit insults, threats, or derogatory content.\"\n}", "text": "Amen brother ..... you did say it like you been there ... they talk crap and if you show fear they 'll be on ya fast .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it simply encourages being alert and confident in a situation. It does not include insults, threats, or derogatory content.", "processing_time": 1.3956897258758545, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it simply encourages being alert and confident in a situation. It does not include insults, threats, or derogatory content.\"\n}", "text": "But get the eyes wide open and lets rock attitude ... most will back off and go away ..... been there with ya !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains offensive speech by singling out individuals from the Black community and accusing them of knowingly transmitting HIV/AIDS, which can incite hatred and discrimination.", "processing_time": 1.3614120483398438, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains offensive speech by singling out individuals from the Black community and accusing them of knowingly transmitting HIV/AIDS, which can incite hatred and discrimination.\"\n}", "text": "I'm not sure what sort of information you 're seeking but I recently found a number of Blacks who have been convicted for knowingly transmitting HIV/AIDS : <PERSON><PERSON><PERSON> http://news.bbc.co.uk/2/hi/uk_news/e...de/3389735.stmJohnsonAzigahttp://en.wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-Yondohttp://en.wikipedia.org/wiki/Stan<PERSON><PERSON>_<PERSON>-YondjoTrevisSmithhttp://en.wikipedia.org/wiki/Trevis_SmithAdrienSylverNduwayohttp://www.thenownews.com/issues06/0...031206nn2.htmlWillieAtkinshttp://www.dallasnews.com/sharedcont....32a4d9fd.htmlNushawnWilliamshttp://en.wikipedia.org/wiki/Nushawn_WilliamsSimonMolhttp://en.wikipedia.org/wiki/<PERSON>_<PERSON>onBandahttp://www.dailymail.co.uk/pages/liv...=5&expand=truePeterMwaihttp://www.crime.co.nz/c-files.asp?ID=36WilliamKaranjahttp://www.amren.com/mtnews/archives...n_rapist_j.phpLinkoyMuhurihttp://news.bbc.co.uk/2/hi/uk_news/e...on/3256352.stmMohammedDicahttp://news.bbc.co.uk/2/hi/uk_news/e...on/3190626.stmFestonKonzanihttp://news.bbc.co.uk/2/hi/uk_news/e...es/4267433.stmGabrielVengesaihttp://www.dailymail.co.uk/pages/liv ... n _ page _ id = 1770", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, or derogatory content. It discusses potential consequences for certain actions in a neutral manner.", "processing_time": 1.8084983825683594, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, or derogatory content. It discusses potential consequences for certain actions in a neutral manner.\"\n}", "text": "You do not have to wait for this law to pass since already you would be expelled from your school for doing the above under some sort of hostile school environment clause , as you would at most places of work .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and promotes racial stereotypes, suggesting bias in the criminal justice system", "processing_time": 1.3687455654144287, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and promotes racial stereotypes, suggesting bias in the criminal justice system\"\n}", "text": "http://www.abc25.com/Global/story.asp ? S = 5352185 Non whites commit far more crimes against whites each year but its never considered a hate crime and alot of them never spend any time in jail !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, or derogatory content. It merely mentions a group without expressing hatred or promoting violence.", "processing_time": 1.387650966644287, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, or derogatory content. It merely mentions a group without expressing hatred or promoting violence.\"\n}", "text": "And some people say that the Klan is n't doing anything today to make a difference .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement expresses hatred and discrimination towards individuals in an interracial marriage, suggesting they are being punished by a higher power.", "processing_time": 1.590627908706665, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement expresses hatred and discrimination towards individuals in an interracial marriage, suggesting they are being punished by a higher power.\"\n}", "text": "Maybe God was punishing them for their interracial marriage .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit hatred and derogatory content towards the Jewish race, making it offensive.", "processing_time": 1.3011970520019531, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit hatred and derogatory content towards the Jewish race, making it offensive.\"\n}", "text": "The Jewish race is without any doubt the greatest threat to the survival of the White race .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it simply mentions following someone's adventures.", "processing_time": 1.815260887145996, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it simply mentions following someone's adventures.\"\n}", "text": "I've followed Mr<PERSON> 's adventures through VNN for the last couple years .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains offensive speech with derogatory content directed at a specific group (Jewish people).", "processing_time": 2.754777193069458, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains offensive speech with derogatory content directed at a specific group (Jewish people).\"\n}", "text": "I just wanted to say hello to The Jew is Evil .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and anti-Semitic remarks, targeting a specific group with offensive language.", "processing_time": 1.3516247272491455, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and anti-Semitic remarks, targeting a specific group with offensive language.\"\n}", "text": "The kikes probably put the Leftist up to it , to make it seem like they do n't have total control of everyone .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit racial slur, incitement of violence, and derogatory remarks towards a specific group", "processing_time": 1.3204820156097412, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit racial slur, incitement of violence, and derogatory remarks towards a specific group\"\n}", "text": "These feral negroes stalk white women , rape and kill and , no one seems willing to stop them .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, or derogatory content. It expresses an opinion about someone's attitude.", "processing_time": 1.4224772453308105, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, or derogatory content. It expresses an opinion about someone's attitude.\"\n}", "text": "This is where their `` I 'm a bad ass , I can do whatever I want '' attitute comes from .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, hatred, or derogatory content. It expresses opinions on self-sufficiency and self-governance.", "processing_time": 1.5695712566375732, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, hatred, or derogatory content. It expresses opinions on self-sufficiency and self-governance.\"\n}", "text": "Indeed, now they just need to feed themselves , educate themselves , police themselves ad nauseum ......", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory stereotypes and promotes racial discrimination.", "processing_time": 1.5482852458953857, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory stereotypes and promotes racial discrimination.\"\n}", "text": "Both Asians and whites are composed and calm and blacks seem to have no self control .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory content towards Chinese immigrants, implying a negative judgment based on their immigration status and nationality.", "processing_time": 1.8686490058898926, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory content towards Chinese immigrants, implying a negative judgment based on their immigration status and nationality.\"\n}", "text": "About the Chinese , those who remain in their own country are good , those who become illegal immigrants in white nations are bad .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains a derogatory and discriminatory remark towards Africans, inciting hatred and promoting exclusion based on ethnicity.", "processing_time": 1.465193271636963, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains a derogatory and discriminatory remark towards Africans, inciting hatred and promoting exclusion based on ethnicity.\"\n}", "text": "Why not just kick out all those Africans ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory language and promotes racial hatred by referring to individuals as 'mongrel offspring' and making discriminatory remarks based on skin color and ethnicity.", "processing_time": 1.40220046043396, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory language and promotes racial hatred by referring to individuals as 'mongrel offspring' and making discriminatory remarks based on skin color and ethnicity.\"\n}", "text": "Those in the countries you mentioned are the mongrel offspring between whites ( Aryans ) and Arabs and its funny how those of the East who claim to be the Aryans talk about white people who came and mixed with them and the only Aryans apparently in the East are those with light skin and blue eyes .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses frustration or resistance, but does not contain explicit insults, threats, or hatred towards any specific group or individual.", "processing_time": 1.43278169631958, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses frustration or resistance, but does not contain explicit insults, threats, or hatred towards any specific group or individual.\"\n}", "text": "Exactly, they want us to lay down and die .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains explicit support for genocide, making it offensive and harmful.", "processing_time": 1.3059322834014893, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains explicit support for genocide, making it offensive and harmful.\"\n}", "text": "By their words and deeds , they declare themselves to be staunchly in favor of the genocide of our people .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive remarks towards Jewish and Black individuals.", "processing_time": 1.2983145713806152, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive remarks towards Jewish and Black individuals.\"\n}", "text": "Then we can all live in a global utopia with the Jew running the show - cant wait Nothing short of a brain transplant is going to raise a Negroes IQ .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, or derogatory content. It merely describes a pile of burned offerings.", "processing_time": 1.3423705101013184, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, or derogatory content. It merely describes a pile of burned offerings.\"\n}", "text": "A pile of burned offerings .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit derogatory and hateful speech towards a specific racial group.", "processing_time": 2.6204800605773926, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit derogatory and hateful speech towards a specific racial group.\"\n}", "text": "oddly i would find it worst if she here black , since Negroes are the most inferior and scum bag humanoid race in our planet .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit derogatory term and offensive language towards a specific group based on sexual orientation", "processing_time": 2.2622854709625244, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit derogatory term and offensive language towards a specific group based on sexual orientation\"\n}", "text": "Good luck with your bugchasing , homo . . .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults, threats, and derogatory content directed at a specific group based on race.", "processing_time": 1.3381938934326172, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults, threats, and derogatory content directed at a specific group based on race.\"\n}", "text": "Laugh it up ape cause without yt to protect your race Asians will probably do the world a favour and wipe your kind from the face of the Earth .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful content towards individuals based on their sexual orientation, promoting discrimination and intolerance.", "processing_time": 1.2637662887573242, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful content towards individuals based on their sexual orientation, promoting discrimination and intolerance.\"\n}", "text": "Gay marriage has no place in any society , regardless of your race and culture.It destroys , morals , values , and the future of your offspring that will keep your race and culture alive.Regardless of your race and culture , homosexuality and gay marriage is the genocide of mankind .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory content and anti-Semitic language, suggesting a negative stereotype about Jewish people.", "processing_time": 1.3324363231658936, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory content and anti-Semitic language, suggesting a negative stereotype about Jewish people.\"\n}", "text": "In all likelyhood , it 's jews painting the road of skank for her to follow .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses skepticism about beliefs and censorship, does not contain offensive speech", "processing_time": 1.2079663276672363, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses skepticism about beliefs and censorship, does not contain offensive speech\"\n}", "text": "But millions can believe a lie for centuries upon centuries , especially when you make investigating the story a crime .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of the term 'negroes' in a derogatory manner and the reference to 'Uncle <PERSON>' is offensive and racially insensitive.", "processing_time": 1.3766214847564697, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of the term 'negroes' in a derogatory manner and the reference to 'Uncle <PERSON>' is offensive and racially insensitive.\"\n}", "text": "That type of negroes call your kind of brothas , Uncle <PERSON> .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory content by implying inferiority based on race.", "processing_time": 1.2446274757385254, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory content by implying inferiority based on race.\"\n}", "text": "We knew that these structures were built by sub-saharan blacks in the same way we knew that the civilization Greek and Romanian built by white Europeans .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit derogatory and offensive content towards Indian women", "processing_time": 1.2343580722808838, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit derogatory and offensive content towards Indian women\"\n}", "text": "The only good thing about Indians is their married women are so loose that prostitution fails in Indian areas .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory remarks towards South Asian people", "processing_time": 1.5739214420318604, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory remarks towards South Asian people\"\n}", "text": "Stop showing off South Asian functional illiteracy .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text provides demographic information about Miami, Florida, without containing any offensive speech, insults, threats, or derogatory content.", "processing_time": 1.3625426292419434, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text provides demographic information about Miami, Florida, without containing any offensive speech, insults, threats, or derogatory content.\"\n}", "text": "Miami,( I do n't know how <PERSON> can stand living down there ) Hispanic - 279,456 ( 70.0 % ) Black alone - 64,993 ( 16.3 % ) White alone - 47,622 ( 11.9 % ) Asian alone - 3,649 ( 0.9 % ) Two or more races - 2,642 ( 0.7 % ) Other race alone - 676 ( 0.2 % ) American Indian alone - 361 ( 0.09 % ) Native Hawaiian and Other Pacific Islander alone - 58 ( 0.01 % ) Read more : Miami , Florida ( FL ) profile : population , maps , real estate , averages , homes , statistics , relocation , travel , jobs , hospitals , schools , crime , moving , houses , news , sex offenders", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of the term 'Negroes' in a derogatory manner is offensive and racist.", "processing_time": 1.3541667461395264, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of the term 'Negroes' in a derogatory manner is offensive and racist.\"\n}", "text": "The majority of them are as stupid as real Negroes .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and racial stereotypes towards white and black individuals", "processing_time": 1.284985065460205, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and racial stereotypes towards white and black individuals\"\n}", "text": "I do not think anyone looks up to white Negroes , just the black ones and then they become wiggers .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech expressing a dislike for something without insults or derogatory content", "processing_time": 1.6149864196777344, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech expressing a dislike for something without insults or derogatory content\"\n}", "text": "We just do n't like what we see .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it simply describes a personal experience.", "processing_time": 1.****************, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it simply describes a personal experience.\"\n}", "text": "I tried to sign up there once & they refused to activate my account .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory and offensive language towards a specific racial group, using a racial slur and implying incompetence based on race.", "processing_time": 1.****************, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory and offensive language towards a specific racial group, using a racial slur and implying incompetence based on race.\"\n}", "text": "Negroids could n't put together a bake sale properly much less a work force .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory content and promotes racial stereotypes, which is offensive and discriminatory.", "processing_time": 1.***************, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory content and promotes racial stereotypes, which is offensive and discriminatory.\"\n}", "text": "The Black race is yet to invent any kind of civilization .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit references to racial preservation, extermination of the White race, and destruction of western civilization, promoting hatred and discrimination.", "processing_time": 1.8169777393341064, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit references to racial preservation, extermination of the White race, and destruction of western civilization, promoting hatred and discrimination.\"\n}", "text": "What does any of that have to do with racial preservation and the resistance to the deliberate and calculated extermination of the White race and the destruction of western civilization which 99 % of the members of this site are concerned with ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains a comparison based on race and implies a bias towards one group, which can be considered offensive.", "processing_time": 1.5017757415771484, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains a comparison based on race and implies a bias towards one group, which can be considered offensive.\"\n}", "text": "They almost always choose to identify with blacks more than whites .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement promotes racial superiority and discrimination, which is offensive and derogatory towards other races.", "processing_time": 1.3149528503417969, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement promotes racial superiority and discrimination, which is offensive and derogatory towards other races.\"\n}", "text": "The White race is best because when the <PERSON><PERSON> made Whites , He made them better than He made the other races .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language towards Africans, referring to them as 'ugly beasts'", "processing_time": 1.3743624687194824, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language towards Africans, referring to them as 'ugly beasts'\"\n}", "text": "Africans are tall , thin and lanky and pretty ugly beasts . ... and I 'm just being kind .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and promotes hatred towards Jewish and White individuals", "processing_time": 1.2435028553009033, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and promotes hatred towards Jewish and White individuals\"\n}", "text": "To me it means that Jewish supremacists will never again allow Whites to have a say .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any explicit insults, threats, hatred, or derogatory content. It expresses an opinion about the consequences of withdrawing support.", "processing_time": 1.6824214458465576, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any explicit insults, threats, hatred, or derogatory content. It expresses an opinion about the consequences of withdrawing support.\"\n}", "text": "The above poster is correct when we stop supporting then they 're in terrible danger .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms ('Jew' and 'libtard') and expresses hatred towards specific groups, making it offensive.", "processing_time": 1.3660216331481934, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms ('Jew' and 'libtard') and expresses hatred towards specific groups, making it offensive.\"\n}", "text": "In the eyes of the Jew and the libtard there is a race problem and the problem is us !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language towards athletes, white women, and their children.", "processing_time": 1.7174055576324463, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language towards athletes, white women, and their children.\"\n}", "text": "I cannot imagine how revolting it must be - the PC dialogue at your university -- idolizing athletes who dont belong in college -- white women getting pregnant with freaks", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and anti-Semitic sentiment towards Jews.", "processing_time": 1.290766954421997, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and anti-Semitic sentiment towards Jews.\"\n}", "text": "Thank you for posting this ! this disgusting lie will hopefully one day be addressed and corrected , at that point I would hope the world will see the Jews for what they really are .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement expresses a harsh and exclusionary stance towards immigrants, advocating for a zero tolerance policy, which can be considered offensive and discriminatory.", "processing_time": 1.6539287567138672, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement expresses a harsh and exclusionary stance towards immigrants, advocating for a zero tolerance policy, which can be considered offensive and discriminatory.\"\n}", "text": "As a nation , we are in desperate need for an immigration time out and a zero tolerance policy for those here illegally .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms and expressions targeting specific groups, indicating offensive speech.", "processing_time": 1.2951068878173828, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms and expressions targeting specific groups, indicating offensive speech.\"\n}", "text": "I am not going to be beat down by a dyke , fag , or coon .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is not offensive as it does not contain explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.348708152770996, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is not offensive as it does not contain explicit insults, threats, hatred, or derogatory content.\"\n}", "text": "I can give you her <PERSON><PERSON> and she will answer all your questions .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement expresses hatred and derogatory content towards liberals, suggesting they harm the nation and its people.", "processing_time": 1.3888506889343262, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement expresses hatred and derogatory content towards liberals, suggesting they harm the nation and its people.\"\n}", "text": "Nearly everything if not all acts that the liberals carry out damage this nation and our people .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains offensive content with sexual connotations and objectification.", "processing_time": 1.1609926223754883, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains offensive content with sexual connotations and objectification.\"\n}", "text": "Ya got a virgin shooter .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is non-offensive as it expresses a positive opinion about someone's father.", "processing_time": 2.4194891452789307, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is non-offensive as it expresses a positive opinion about someone's father.\"\n}", "text": "Ten to one they have a great father , too .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is not offensive as it expresses admiration for individuals fighting for a cause.", "processing_time": 1.2293658256530762, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is not offensive as it expresses admiration for individuals fighting for a cause.\"\n}", "text": "It 's where I discovered so many great people who are fighting for our cause .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, encourages critical thinking and questioning of teachings", "processing_time": 1.3062715530395508, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, encourages critical thinking and questioning of teachings\"\n}", "text": "I 've taught her to question everything that they teach her .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The mention of 'Stormfronters' indicates association with a white supremacist online community, making the speech offensive.", "processing_time": 1.311128854751587, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The mention of 'Stormfronters' indicates association with a white supremacist online community, making the speech offensive.\"\n}", "text": "Here are the CDs I want to sell : <PERSON> : Symphonies Nos. 5 & 7 - <PERSON><PERSON><PERSON> : Symphony No. 9 - <PERSON><PERSON><PERSON><PERSON> Bruckner : Symphony No. 9 - <PERSON><PERSON><PERSON><PERSON> : Music for the Royal Fireworks ; Water Music - <PERSON><PERSON><PERSON> <PERSON> : Orchestral Works - Schermerhorn J.S. Bach : Great Organ Works - Hurford Norwegian Classical Favorites - Engeset The Best of Baroque Music - <PERSON><PERSON> Twilight of the Gods : The Essential Wagner Collection - <PERSON><PERSON> , <PERSON><PERSON> , <PERSON><PERSON> , <PERSON><PERSON> , <PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON> Most of these were recommended by fellow Stormfronters .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is polite and does not contain any offensive content.", "processing_time": 1.4921200275421143, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is polite and does not contain any offensive content.\"\n}", "text": "Thanks for coming", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains a threat of violence towards a specific group (people running SA)", "processing_time": 1.3146576881408691, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains a threat of violence towards a specific group (people running SA)\"\n}", "text": "The people running SA into the ground , want to wipe them all out .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains offensive content by promoting racial stereotypes and suggesting superiority of one race over another.", "processing_time": 1.4803972244262695, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains offensive content by promoting racial stereotypes and suggesting superiority of one race over another.\"\n}", "text": "Sometimes I wonder if it is Because Whites are so much smarter that they find it harder to run together , as opposed to , independently .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains offensive content by promoting the idea that diversity destroys social cohesion, which can be considered derogatory and divisive.", "processing_time": 1.434253454208374, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains offensive content by promoting the idea that diversity destroys social cohesion, which can be considered derogatory and divisive.\"\n}", "text": "Check out this youtube channel i found , i agree with most of what he says Diversity DESTROYS Social Cohesion in the West - YouTube", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply provides information about a speaker and an event without containing any offensive content.", "processing_time": 1.3341238498687744, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply provides information about a speaker and an event without containing any offensive content.\"\n}", "text": "<PERSON> : `` False confessions '' https : //www.youtube.com/watch ? v = JDRRwFfJKkw The Vera Institute of Justice 's <PERSON> Research Department Speaker Series presents <PERSON> , Distinguished Professor of Psychology at John Jay College of Criminal Justice .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing acceptance of someone's behavior or characteristics", "processing_time": 1.3187057971954346, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing acceptance of someone's behavior or characteristics\"\n}", "text": "It 's just the way they are .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is questioning a past event and does not contain explicit insults, threats, or derogatory content.", "processing_time": 1.5463662147521973, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is questioning a past event and does not contain explicit insults, threats, or derogatory content.\"\n}", "text": "You need to ask this when the Obama Administration instructed the IRS to target conservatives ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is positive and does not contain any offensive speech or content.", "processing_time": 1.3958477973937988, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is positive and does not contain any offensive speech or content.\"\n}", "text": "I 'm hoping things will continue to grow with our smaller meets and will make for a good turn out at our events this year !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement describes a personal experience without containing any explicit insults, threats, or derogatory content.", "processing_time": 1.5134873390197754, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement describes a personal experience without containing any explicit insults, threats, or derogatory content.\"\n}", "text": "I considered it being `` ethnically cleansed '' from my childhood home", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, hatred, or derogatory content. It expresses an opinion about the intelligence of the <PERSON> brothers and their fighting style.", "processing_time": 1.5763721466064453, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, hatred, or derogatory content. It expresses an opinion about the intelligence of the <PERSON> brothers and their fighting style.\"\n}", "text": "Neither of the <PERSON> brothers are very bright but they generally come to fight .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply describes the location and features of a suburban area with a lake.", "processing_time": 1.348836898803711, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply describes the location and features of a suburban area with a lake.\"\n}", "text": "I live in a suburban area with an old spring fed lake that always has water in it .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing a personal achievement without any offensive content.", "processing_time": 1.2322337627410889, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing a personal achievement without any offensive content.\"\n}", "text": "Nothing happened except I got an 88 % on the paper .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory terms and promotes hatred towards specific racial and religious groups.", "processing_time": 1.3043186664581299, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory terms and promotes hatred towards specific racial and religious groups.\"\n}", "text": "Well, at least these dumb negroes got the fact that Jews are evil right ; though I suspect whitey and the jews are one in the same to their primitive minds ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful speech towards specific racial groups, inciting racial division and promoting racial suicide.", "processing_time": 1.4055402278900146, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful speech towards specific racial groups, inciting racial division and promoting racial suicide.\"\n}", "text": "I am sure California is full of sick self hating whites , who think that for the good of all the black brown and yellow peoples of the world , whites need to commit racial suicide and disappear from their face of the earth for their sake and wo n't that be just dandy !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist remarks towards nonwhite individuals, deeming their DNA as 'yucky' and 'disgusting'.", "processing_time": 1.4242901802062988, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist remarks towards nonwhite individuals, deeming their DNA as 'yucky' and 'disgusting'.\"\n}", "text": "I do n't blame you , all the yucky nonwhite DNA pawing your food as they pass it over the UPC scanner , disgusting .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language towards Filipinos, Indians, and Asians", "processing_time": 1.3687222003936768, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language towards Filipinos, Indians, and Asians\"\n}", "text": "All of our tim hortons are staffed by those nast filipinos and every single town is full of Indians and asians .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement promotes racial hatred and derogatory views towards mixed-race individuals, indicating offensive speech.", "processing_time": 1.4422404766082764, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement promotes racial hatred and derogatory views towards mixed-race individuals, indicating offensive speech.\"\n}", "text": "And theres a good chance these children will end up race mixing back into the white gene pool , mongrelizing our race .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language towards specific racial groups", "processing_time": 1.2876083850860596, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language towards specific racial groups\"\n}", "text": "Then again , in this modern age of white youth emulating negroes and white adults groveling at the feet of the juden , perhaps it 's an appropriate word after all .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains explicit hate speech, advocating for deportation, sterilization, and discrimination based on race.", "processing_time": 1.3820116519927979, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains explicit hate speech, advocating for deportation, sterilization, and discrimination based on race.\"\n}", "text": "We need to deport all nonwhites immediately , that or stop letting more in and sterilize the ones here , that will fix the problem .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory and discriminatory language towards non-white individuals, implying they would cause a 'racial disaster' if more prevalent in Canada.", "processing_time": 1.8942534923553467, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory and discriminatory language towards non-white individuals, implying they would cause a 'racial disaster' if more prevalent in Canada.\"\n}", "text": "If Canada was small the nonwhites would be everywhere but so many whites live rurally or in small towns so they do n't see the racial disaster unfolding on Canada .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply describes the presence of two knife blocks on the counter.", "processing_time": 1.4013173580169678, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply describes the presence of two knife blocks on the counter.\"\n}", "text": "I've got two knife blocks on the counter .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it simply describes a scenario at a restaurant.", "processing_time": 1.358304500579834, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it simply describes a scenario at a restaurant.\"\n}", "text": "At the resturant , the Pres. of Hamms orders a Hamms , the Pres. of Schlitz orders a Schlitz , the Pres. of Budweiser orders a Bud , and the Pres. of <PERSON><PERSON><PERSON> orders a cup of tea .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is a factual description and does not contain any offensive content.", "processing_time": 1.3683350086212158, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is a factual description and does not contain any offensive content.\"\n}", "text": "The school did not cause my much trouble because it was elementary school .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses skepticism towards the news media, not offensive speech", "processing_time": 1.4136807918548584, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses skepticism towards the news media, not offensive speech\"\n}", "text": "We say here that if we want to be lied to just watch the news !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of a derogatory term towards a child is offensive and inappropriate.", "processing_time": 1.3017511367797852, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of a derogatory term towards a child is offensive and inappropriate.\"\n}", "text": "I went outside and the ni<PERSON>t 's mom told me my daughter had hit her first .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech stating a historical fact without any derogatory content or insults.", "processing_time": 1.5249242782592773, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech stating a historical fact without any derogatory content or insults.\"\n}", "text": "The Neolithic farmers who migrated into Europe originated in Anatolia .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory language towards liberals and promotes racial hatred, making it offensive.", "processing_time": 1.3548665046691895, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory language towards liberals and promotes racial hatred, making it offensive.\"\n}", "text": "Explain how liberals are liars and anti-White and tell her about crime statistics .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, providing a suggestion or opinion without any offensive content", "processing_time": 1.3396658897399902, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, providing a suggestion or opinion without any offensive content\"\n}", "text": "Maybe you 'll find it useful .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it simply describes a discussion about incorporating politics into math education.", "processing_time": 1.3844263553619385, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it simply describes a discussion about incorporating politics into math education.\"\n}", "text": "Chicago Teachers Union president explains how to inject politics into math - YouTube Chicago Teachers Union president <PERSON> explains how to politicize school math for organizing - EAGnews.org powered by Education Action Group Foundation , Inc.", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it is a suggestion or opinion regarding a location.", "processing_time": 1.2832181453704834, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it is a suggestion or opinion regarding a location.\"\n}", "text": "Maybe you should look there or just move here to miss. Or more specifically Tupel<PERSON> .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory language and promotes discriminatory behavior based on race.", "processing_time": 1.254931926727295, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory language and promotes discriminatory behavior based on race.\"\n}", "text": "All we have is blacks and a few mestizos and all are pretty well behaved , we keep them that way around here", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text contains factual statements about the birthplaces of the speaker's parents, without any offensive content.", "processing_time": 1.3475666046142578, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text contains factual statements about the birthplaces of the speaker's parents, without any offensive content.\"\n}", "text": "My father was born in Germany My mother 's parent 's moved from Ireland .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains an explicit insult directed at individuals based on their political beliefs.", "processing_time": 2.578214406967163, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains an explicit insult directed at individuals based on their political beliefs.\"\n}", "text": "Lying Marxists !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit insults, derogatory remarks, and dehumanizing language towards an individual based on their nationality", "processing_time": 1.3274269104003906, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit insults, derogatory remarks, and dehumanizing language towards an individual based on their nationality\"\n}", "text": "Lay her off and cart her back to India where she can poop in the Ganges river next to her dead grand parents .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply describes the service of offering certificates for course completion.", "processing_time": 1.3802428245544434, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply describes the service of offering certificates for course completion.\"\n}", "text": "It offers certificates of completion for many courses .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, but rather provides constructive criticism on grammar and language use.", "processing_time": 0.8161594867706299, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, but rather provides constructive criticism on grammar and language use.\"\n}", "text": "Perhaps people will take you more seriously if you do n't make as many grammar errors and using repulsive language .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory language ('mooching') and expresses hatred towards a specific group (those focused on welfare).", "processing_time": 1.3472654819488525, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory language ('mooching') and expresses hatred towards a specific group (those focused on welfare).\"\n}", "text": "The only thing that they care about is welfare and mooching of all the rest of us .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses a personal opinion about a music genre, not offensive speech.", "processing_time": 1.2345738410949707, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses a personal opinion about a music genre, not offensive speech.\"\n}", "text": "I really do hate rap music .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses personal discomfort with music choice, not offensive speech", "processing_time": 1.24284029006958, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses personal discomfort with music choice, not offensive speech\"\n}", "text": "More than that I cringe every time I go to hang out with my friends and they blast this filth in their cars and homes .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it is a neutral question about a scientific topic.", "processing_time": 1.184119701385498, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it is a neutral question about a scientific topic.\"\n}", "text": "And hey , we have not talked about women 's mitochondriat at all ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains a derogatory term and promotes racial hatred and violence.", "processing_time": 1.2776684761047363, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains a derogatory term and promotes racial hatred and violence.\"\n}", "text": "When they sent n iggers to space the space ships started blowing up .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply expresses an opinion about the ease of using modern technology.", "processing_time": 1.6491730213165283, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply expresses an opinion about the ease of using modern technology.\"\n}", "text": "Everything is machined to well today mlst stuff is plug and play .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech. It encourages connecting with like-minded individuals for gardening and plant-related activities.", "processing_time": 1.564711570739746, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech. It encourages connecting with like-minded individuals for gardening and plant-related activities.\"\n}", "text": "Find other gardeners , wild plant enthusiasts , and fellow believers in your area who you can band with .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text contains no offensive speech, just a description of food preparation and cooking activities.", "processing_time": 1.3139240741729736, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text contains no offensive speech, just a description of food preparation and cooking activities.\"\n}", "text": "We are cooking a duck on the rotisserie , catch the fat drippings and cook little potatoes in that , its delicious , we 're also cooking a small turkey , mashed potatoes , carrots , pumpkin pie and butternut squash cut in half and baked with butter and brown sugar .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is neutral and does not contain any offensive speech.", "processing_time": 1.247335433959961, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is neutral and does not contain any offensive speech.\"\n}", "text": "Listening now .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it refers to an Indian symbol for good fortune and mentions a specific group without derogatory content.", "processing_time": 1.3501675128936768, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it refers to an Indian symbol for good fortune and mentions a specific group without derogatory content.\"\n}", "text": "Seriously Guys , It 's An Indian Symbol For Good Fortune - People Of Walmart : People Of Walmart", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and anti-Semitic remarks, targeting specific groups", "processing_time": 1.1757092475891113, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and anti-Semitic remarks, targeting specific groups\"\n}", "text": "No need to allow FACTS to get in the way of a good White guilt message ! ... and the jew laughs loudly !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech expresses a strong opinion about the quality and price of AR-15 rifles, but does not contain explicit insults, threats, or derogatory content.", "processing_time": 1.5022618770599365, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech expresses a strong opinion about the quality and price of AR-15 rifles, but does not contain explicit insults, threats, or derogatory content.\"\n}", "text": "Or just stay the hell away from cheap , crap ar15s when you can get a damn reliable plinker for $ 700 brand new ......", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content towards homosexuals, implying they are more likely to bully others", "processing_time": 1.2612740993499756, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content towards homosexuals, implying they are more likely to bully others\"\n}", "text": "I'm glad you pointed this out that anti-Bullying campaign has been used to promote homosexuals who are more likely to bully anyone who disagrees with them .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive remarks towards Jews and Blacks", "processing_time": 1.2139256000518799, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive remarks towards Jews and Blacks\"\n}", "text": "What makes it suck are the Jews that run it and the Blacks that play in it .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is not offensive as it simply expresses personal preferences and experiences.", "processing_time": 1.193859577178955, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is not offensive as it simply expresses personal preferences and experiences.\"\n}", "text": "I enjoyed playing the game when I was a kid , but I never enjoyed watching it on TV .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, offering help or support", "processing_time": 1.194653034210205, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, offering help or support\"\n}", "text": "Please let me know if there is anything I can do .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it is a statement expressing an opinion on the ideal size of a pellet.", "processing_time": 1.3258435726165771, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it is a statement expressing an opinion on the ideal size of a pellet.\"\n}", "text": "An even smaller pellet is probably ideal , #4 <PERSON> , perhaps .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it is a personal reflection on a decision between going to bed or having a beer.", "processing_time": 1.403601884841919, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it is a personal reflection on a decision between going to bed or having a beer.\"\n}", "text": "I was just debating with myself whether I should go to bed or have one last beer ...", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains a derogatory and stereotypical statement about white individuals, suggesting they are always racist in interracial conflicts.", "processing_time": 1.4586389064788818, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains a derogatory and stereotypical statement about white individuals, suggesting they are always racist in interracial conflicts.\"\n}", "text": "Notice the video title , whenever there is a fight between a white and non white , the white is always the racist", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech expressing a balanced view on the topic.", "processing_time": 1.2864735126495361, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech expressing a balanced view on the topic.\"\n}", "text": "I see pro and cons of both .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is not offensive as it encourages helping others in a light-hearted manner.", "processing_time": 1.533581018447876, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is not offensive as it encourages helping others in a light-hearted manner.\"\n}", "text": "Next time you see someone with their pants falling down , just help them !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains a derogatory generalization and promotes hatred based on race, making it offensive.", "processing_time": 1.4107334613800049, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains a derogatory generalization and promotes hatred based on race, making it offensive.\"\n}", "text": "White speech is Hate ....", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement expresses a discriminatory attitude towards non-white individuals, indicating offensive speech.", "processing_time": 1.7714028358459473, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement expresses a discriminatory attitude towards non-white individuals, indicating offensive speech.\"\n}", "text": "I have one word for you ....... MOVE . We chose this area because we wanted our children to go to all white schools .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses an opinion about the political leanings of parents and does not contain explicit insults, threats, or hatred.", "processing_time": 1.4128828048706055, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses an opinion about the political leanings of parents and does not contain explicit insults, threats, or hatred.\"\n}", "text": "Like I said before I 'd bet 90 % + have Liberal parents .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it is a neutral question about the existence of a border fence.", "processing_time": 1.081122636795044, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it is a neutral question about the existence of a border fence.\"\n}", "text": "Border fence , what border fence ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is a factual observation and does not contain any offensive content.", "processing_time": 1.250767469406128, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is a factual observation and does not contain any offensive content.\"\n}", "text": "I've been following the events in Ukraine and have noticed some troops using the SKS .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, or derogatory content. It could be interpreted as expressing satisfaction or justice being served.", "processing_time": 1.962536096572876, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, or derogatory content. It could be interpreted as expressing satisfaction or justice being served.\"\n}", "text": "He got what he deserved .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory and offensive language towards black individuals, using racial slurs and stereotypes.", "processing_time": 1.2596087455749512, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory and offensive language towards black individuals, using racial slurs and stereotypes.\"\n}", "text": "Back when I was a kid , even the black folks called them that .... `` Ooohhh I' z cai n't wait until <PERSON><PERSON><PERSON><PERSON> , I' z gonna get me sum of dem niggah-toes and oranguz fo ' <PERSON><PERSON>us<PERSON> , an ' sum peppahmintz ; dey shoa iz gud ! ''", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory content and promotes racial segregation.", "processing_time": 1.1890006065368652, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory content and promotes racial segregation.\"\n}", "text": "some times Whites are forced to live around a lot of blacks .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, simply stating the location where the speaker lives.", "processing_time": 1.2025244235992432, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, simply stating the location where the speaker lives.\"\n}", "text": "I live in AR .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language towards a specific racial group", "processing_time": 1.2649166584014893, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language towards a specific racial group\"\n}", "text": "I going to get homeschooled tired of looney lefty teachers and the negroes", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement describes witnessing people carrying a .45 caliber gun and feeling angry, without containing explicit insults, threats, or derogatory content.", "processing_time": 1.6024911403656006, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement describes witnessing people carrying a .45 caliber gun and feeling angry, without containing explicit insults, threats, or derogatory content.\"\n}", "text": "I 've seen shots with a .45 , where people walked around angry .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit derogatory language and promotes racial hatred and violence.", "processing_time": 1.3036181926727295, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit derogatory language and promotes racial hatred and violence.\"\n}", "text": "I've often pointed out that negroes are more stupid than I can even imagine if they actually think they 're gonna win any race war .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains a derogatory term 'jigs' which is offensive and derogatory towards a specific group of people", "processing_time": 1.3248023986816406, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains a derogatory term 'jigs' which is offensive and derogatory towards a specific group of people\"\n}", "text": "This guy seems to get it but otherwise : Sorry jigs , you 're on your own", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains explicit description of violent and harmful actions towards others", "processing_time": 2.247882843017578, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains explicit description of violent and harmful actions towards others\"\n}", "text": "I'm more of a grappler than a striker , I would lunge out with a windpipe grab and bring them down to the floor .... my record for KO' ing someone with a choke is currently 3 secs", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and dehumanizing language towards individuals based on their race, comparing them to apes.", "processing_time": 1.398172378540039, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and dehumanizing language towards individuals based on their race, comparing them to apes.\"\n}", "text": "Instead it is the athletic negro <PERSON><PERSON> 11 kids , <PERSON> 7 kids , <PERSON><PERSON><PERSON> 5 kids , <PERSON> 10 kids who procreate like apes and spread these athlete genes along .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains discriminatory language and perpetuates stereotypes based on gender and race.", "processing_time": 1.346496820449829, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains discriminatory language and perpetuates stereotypes based on gender and race.\"\n}", "text": "Those boys will use the plastic whisks as swords , and the girls ' lego creations will consist of dollhouses for the few white dolls , with the dark ones in the adjoining farm pen .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses an opinion and does not contain offensive content.", "processing_time": 1.3600754737854004, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses an opinion and does not contain offensive content.\"\n}", "text": "The idea that homeschooled kids do n't get socialized is ludicrous .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "No offensive speech detected, please provide text for analysis.", "processing_time": 1.138913869857788, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"No offensive speech detected, please provide text for analysis.\"\n}", "text": "2 .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The speech contains explicit expression of hatred and desire for vengeance towards a specific group, referring to them as 'sub humans'.", "processing_time": 1.342686414718628, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The speech contains explicit expression of hatred and desire for vengeance towards a specific group, referring to them as 'sub humans'.\"\n}", "text": "When I watch things like this I pray God will have vengeance on these sub humans even if they have asked for forgiveness .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text contains references to controversial topics but does not contain explicit offensive speech or derogatory content.", "processing_time": 1.3298485279083252, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text contains references to controversial topics but does not contain explicit offensive speech or derogatory content.\"\n}", "text": "<PERSON><PERSON> and Dr. <PERSON> - Zion<PERSON> , the Holocaust , Aids and Vaccines YouTube - <PERSON><PERSON> and Dr. <PERSON> - Zionism , the Holocaust , Aids and Vaccines Listen/watch the full interview @ <PERSON><PERSON> and Dr. <PERSON> - Zion<PERSON> , the Holocaust , Aids and Vaccines : Free Download & Streaming : Internet Archive www.DrDay.com www.GoodNewsAboutGod.com", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, simply describing children in costumes", "processing_time": 1.8280491828918457, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, simply describing children in costumes\"\n}", "text": "Little kids all decked out in their costumes .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it expresses a positive opinion about a documentary.", "processing_time": 1.3413264751434326, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it expresses a positive opinion about a documentary.\"\n}", "text": "It 's a pretty powerful documentary that blew me away .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech expressing an observation about the sound of something", "processing_time": 2.103541851043701, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech expressing an observation about the sound of something\"\n}", "text": "But it does have a feminine sound .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, just a positive comment on living a TV-free and child-free lifestyle.", "processing_time": 1.7357878684997559, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, just a positive comment on living a TV-free and child-free lifestyle.\"\n}", "text": "Hi Im tv free do n't have kids yet reading mending , sewing , menting board games radio study extra curricula actives Good on you for planning a FREE jewv existence", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The use of a racial slur directed at a specific group is offensive and derogatory.", "processing_time": 1.7731471061706543, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The use of a racial slur directed at a specific group is offensive and derogatory.\"\n}", "text": "These ' niggas ' .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement is a factual description of the number of people living near the speaker's house, without containing any offensive content.", "processing_time": 1.324836254119873, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement is a factual description of the number of people living near the speaker's house, without containing any offensive content.\"\n}", "text": "There are only eight people living within a mile of my house , and none closer than a quarter-mile .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist remarks towards Indians and Black people, promoting hatred and stereotypes.", "processing_time": 1.2018048763275146, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist remarks towards Indians and Black people, promoting hatred and stereotypes.\"\n}", "text": "It's weird most Indians dot and to an extent feather that I deal with hate blacks more than they do whites and find blacks to be lazy and worthless .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains a suggestion to cause harm and discomfort to individuals (chimps) by using stink bombs, which is offensive and inappropriate.", "processing_time": 1.3675501346588135, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains a suggestion to cause harm and discomfort to individuals (chimps) by using stink bombs, which is offensive and inappropriate.\"\n}", "text": "Dude stink bomb this event , when it gets dark , chuck a bunch of them on the floor , different spots , when the chimps start jiving and grooving , they will step on them .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, simply expressing forgetfulness", "processing_time": 1.431962013244629, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, simply expressing forgetfulness\"\n}", "text": "Oh, I forgot to say where I lived .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and stereotypes based on race and gender, promoting hatred and discrimination.", "processing_time": 1.3139581680297852, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and stereotypes based on race and gender, promoting hatred and discrimination.\"\n}", "text": "They laugh at whites who obey the law , bother to get married , women who are chaste .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory content towards individuals based on their sexual orientation.", "processing_time": 1.2684884071350098, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory content towards individuals based on their sexual orientation.\"\n}", "text": "That 's to assume the gay ones are n't abusive .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and hateful speech towards homosexuals, linking them to pedophilia and using offensive language", "processing_time": 1.2863478660583496, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and hateful speech towards homosexuals, linking them to pedophilia and using offensive language\"\n}", "text": "the kind of perversion that leads to homosexuality also leads to pedophilia. why take the risk , and even if the homosexuals turned out not to be pedophiles why would anyone want a child to live with two perverts .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses frustration with a teacher, but does not contain explicit insults, threats, or derogatory content.", "processing_time": 1.28157639503479, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses frustration with a teacher, but does not contain explicit insults, threats, or derogatory content.\"\n}", "text": "Hell to have a teacher like this .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory content towards homosexuals, implying their behavior is unjustifiable and bad.", "processing_time": 1.2629015445709229, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory content towards homosexuals, implying their behavior is unjustifiable and bad.\"\n}", "text": "This is the argument homosexuals use to justify their behavior , that because there are no `` red letters '' about it , then homosexuality is not a bad thing .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, no explicit content present.", "processing_time": 1.6827991008758545, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, no explicit content present.\"\n}", "text": "No .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, promotes freedom of expression", "processing_time": 1.2018797397613525, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, promotes freedom of expression\"\n}", "text": "The students should be able to express themselves .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and stereotypes towards Spaniards and individuals of Hispanic descent", "processing_time": 1.2903952598571777, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and stereotypes towards Spaniards and individuals of Hispanic descent\"\n}", "text": "Americans and Irish understand each other better than Spaniards and the beaners .. the US and Ireland are the 1st world so they are educated unlike the beaners .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The term 'Zionist' is often used in a derogatory manner to target a specific group, implying offensive connotations.", "processing_time": 1.5422453880310059, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The term 'Zionist' is often used in a derogatory manner to target a specific group, implying offensive connotations.\"\n}", "text": "They have properly learnt their Zionist lesson ...", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, or hatred. It expresses a personal observation about holiday mentions at college.", "processing_time": 1.3791780471801758, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, or hatred. It expresses a personal observation about holiday mentions at college.\"\n}", "text": "I remember when I was at my college during the Holidays ... every single holiday would be mentioned loud and clear except for Christmas ... especially kwanzaa .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text expresses skepticism towards media accuracy, but does not contain offensive speech or derogatory content.", "processing_time": 1.3336846828460693, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text expresses skepticism towards media accuracy, but does not contain offensive speech or derogatory content.\"\n}", "text": "great posts. see how the media has been lying to us for decades about what actually happened and what it was actually all about .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is a factual inquiry and does not contain any offensive speech.", "processing_time": 1.34201979637146, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is a factual inquiry and does not contain any offensive speech.\"\n}", "text": "Has anyone claiming this method been able to make even one block like that with wedges water and copper tools ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains a derogatory and racist remark towards white people, inciting hatred and promoting harmful stereotypes.", "processing_time": 2.1492435932159424, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains a derogatory and racist remark towards white people, inciting hatred and promoting harmful stereotypes.\"\n}", "text": "They will eventually kill off the whites and go back to the jungle ways .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text contains a humorous and fictional scenario, not directed at specific groups or individuals.", "processing_time": 1.5511693954467773, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text contains a humorous and fictional scenario, not directed at specific groups or individuals.\"\n}", "text": "Wasn't there something like a big bang in which a burrito exploded and all the beans flew everywhere and started reproducing exponentially ?", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive statement about the ability of young children to learn languages quickly", "processing_time": 1.6853761672973633, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive statement about the ability of young children to learn languages quickly\"\n}", "text": "Apparently young kids pick languages up very quickly .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains derogatory and racist remarks towards black individuals, perpetuating harmful stereotypes and promoting hate speech.", "processing_time": 1.766381025314331, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains derogatory and racist remarks towards black individuals, perpetuating harmful stereotypes and promoting hate speech.\"\n}", "text": "you just say blacks hate the cold. the only reason they are good athletes is because when you have to run lions and tigers you get fast or eaten .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist remarks towards individuals based on their race, which is offensive and inappropriate.", "processing_time": 2.093018054962158, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist remarks towards individuals based on their race, which is offensive and inappropriate.\"\n}", "text": "why did god give negroes good rhythm ?? because he messed up their lips , hair , nose and skin so yes they are ugly !!!!!!!!!!!!!!", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses a belief about a religious story, does not contain explicit insults, threats, or hatred", "processing_time": 1.2988014221191406, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses a belief about a religious story, does not contain explicit insults, threats, or hatred\"\n}", "text": "I believe <PERSON> 's faith was tested by <PERSON> when he asked him to sacrifice <PERSON> , the quran falsely claims it was <PERSON><PERSON><PERSON> .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory content directed at individuals based on their physical stature.", "processing_time": 1.2577846050262451, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory content directed at individuals based on their physical stature.\"\n}", "text": "I take it you are a little person then .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory language and promotes hatred towards a specific group based on race", "processing_time": 1.265624761581421, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory language and promotes hatred towards a specific group based on race\"\n}", "text": "Like the parasites they are they simply moved to a new host , to continue their destruction of Western White civilization and culture", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it is a neutral question seeking clarification.", "processing_time": 1.2518179416656494, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it is a neutral question seeking clarification.\"\n}", "text": "I'm too lazy to reread , did he say ' Teacher `` or '' Teacher 's asst . \" ?", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and targeted insults towards Mexican gang members", "processing_time": 1.253774642944336, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and targeted insults towards Mexican gang members\"\n}", "text": "I 'm sure I would have done great in public school if the Mexican gang members stopped kicking my * * * long enough for me to get my homework done ...", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content towards Jewish people, implying they use money to deceive others", "processing_time": 1.9800901412963867, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content towards Jewish people, implying they use money to deceive others\"\n}", "text": "<PERSON> 's own divine intervention , aided by Jewish money swindled from the goyim .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The term 'JAP' is derogatory and offensive as it is a racial slur targeting individuals of Japanese descent.", "processing_time": 1.3527131080627441, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The term 'JAP' is derogatory and offensive as it is a racial slur targeting individuals of Japanese descent.\"\n}", "text": "Had a run in with a JAP tonight .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains a direct and explicit threat of harm towards an individual, making it offensive.", "processing_time": 1.2876203060150146, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains a direct and explicit threat of harm towards an individual, making it offensive.\"\n}", "text": "I seriously hope she dies in a fire one day .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The statement contains derogatory stereotypes about black sport players in North America, which is offensive and discriminatory.", "processing_time": 1.8142342567443848, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The statement contains derogatory stereotypes about black sport players in North America, which is offensive and discriminatory.\"\n}", "text": "Just watch them at North America and they like all black sport players and their `` slang '' .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain offensive speech, it appears to be a factual observation or opinion about oil reserves underground.", "processing_time": 1.3166420459747314, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain offensive speech, it appears to be a factual observation or opinion about oil reserves underground.\"\n}", "text": "And all those holes below 40,000 feet are filled with oil instead of water .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses dissatisfaction with economic policies, does not contain explicit insults, threats, or hatred", "processing_time": 1.****************, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses dissatisfaction with economic policies, does not contain explicit insults, threats, or hatred\"\n}", "text": "Agreed! rep will have to wait though .. Cheap money , massive bailouts of crony banksters , manipulated employment figures , etc , etc , ..", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and racial stereotypes towards black individuals", "processing_time": 1.****************, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and racial stereotypes towards black individuals\"\n}", "text": "I bet they wouldnt have done that crap to a black person that had black power or those black fists that the panthers use written on his back pack .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply mentions the name and location of a college without containing any insults, threats, or derogatory content.", "processing_time": 1.****************, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply mentions the name and location of a college without containing any insults, threats, or derogatory content.\"\n}", "text": "www.sjca.edu St. John 's College , Santa Fe , N.M.", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Advocates for genocide of other races based on their location and lack of involvement in our affairs, which is offensive and hateful.", "processing_time": 2.5144076347351074, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Advocates for genocide of other races based on their location and lack of involvement in our affairs, which is offensive and hateful.\"\n}", "text": "agreed, there is no reason to make genocide of other races as long as they are in another country and have nothing to do with our affairs !", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The speech is non-offensive as it expresses admiration for a group's positive qualities without containing insults, threats, or derogatory content.", "processing_time": 1.4548125267028809, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The speech is non-offensive as it expresses admiration for a group's positive qualities without containing insults, threats, or derogatory content.\"\n}", "text": "You are joining a unit that is famous for its great spirit and effectiveness at awakening our kinsmen .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing a positive sentiment towards the classroom", "processing_time": 1.2726521492004395, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing a positive sentiment towards the classroom\"\n}", "text": "Enjoy your classroom .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text encourages standing up for oneself and not seeking conflict, but being prepared to defend oneself if necessary. It does not contain explicit insults, threats, or hatred.", "processing_time": 1.4554481506347656, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text encourages standing up for oneself and not seeking conflict, but being prepared to defend oneself if necessary. It does not contain explicit insults, threats, or hatred.\"\n}", "text": "you gotta stand up for yourself you cant just sit back and take their * * * * but on the other hand you cant go out looking for it either but if it comes down to a fight dont back down", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains offensive speech with derogatory remarks towards specific groups based on race, sexual orientation, and loyalty", "processing_time": 1.537109375, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains offensive speech with derogatory remarks towards specific groups based on race, sexual orientation, and loyalty\"\n}", "text": "God forbid anyone should promote straight racially loyal hetrosexuality <PERSON><PERSON><PERSON>", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain explicit insults, threats, or derogatory content. It simply advises against initiating conflict.", "processing_time": 1.3089640140533447, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain explicit insults, threats, or derogatory content. It simply advises against initiating conflict.\"\n}", "text": "First off they probably wont do anything , second off dont start anything .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, it appears to be a username or nickname.", "processing_time": 1.6271793842315674, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, it appears to be a username or nickname.\"\n}", "text": "- = <PERSON>lfi = -", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain offensive speech, it simply mentions watching shows about WWII on the History channel.", "processing_time": 1.3055295944213867, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain offensive speech, it simply mentions watching shows about WWII on the History channel.\"\n}", "text": "Sometimes I do watch shows about WWII on the History channel but other than that I never watch it .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and offensive language towards specific racial groups", "processing_time": 1.3959643840789795, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and offensive language towards specific racial groups\"\n}", "text": "LOL GJ brother 10 points for the Uber humans and - 10 for the sub human ape creatures you are truly one bad mofo whitey crakker man", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses a suspicion but does not contain explicit insults, threats, or hatred. It is not offensive speech.", "processing_time": 2.24033522605896, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses a suspicion but does not contain explicit insults, threats, or hatred. It is not offensive speech.\"\n}", "text": "They probably stole a bunch of stuff .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text contains references to violence and physical harm, making it offensive.", "processing_time": 2.8290164470672607, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text contains references to violence and physical harm, making it offensive.\"\n}", "text": "it is better with a shaved head.A friend of mine was beaten really bad a year ago because they could have pulled him by his hair .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text does not contain any offensive speech, just casual language and a question about the availability of an item.", "processing_time": 1.2858037948608398, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text does not contain any offensive speech, just casual language and a question about the availability of an item.\"\n}", "text": "damn! at least that sounds more plausable than a stupid sea. maybe look on ebay ??? are u sure there is none left ???", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and promotes racial division by referring to 'white guilt'", "processing_time": 2.426166296005249, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and promotes racial division by referring to 'white guilt'\"\n}", "text": "Its simple , they want to create white guilt , .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "The text promotes White Nationalism, which is considered offensive and discriminatory towards other racial groups.", "processing_time": 2.4415769577026367, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"The text promotes White Nationalism, which is considered offensive and discriminatory towards other racial groups.\"\n}", "text": "Welcome to Stormfront , the largest White Nationalist community on the web .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory remarks towards the public education system", "processing_time": 1.2379229068756104, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory remarks towards the public education system\"\n}", "text": "I know how you feel BreakTheSilence luckily this is your last year in school and it is almost over otherwise I would say you should look into independent study where you do not have to put up with all the filth that is in the public education system .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text is non-offensive as it simply mentions updating a website and adding a chat room, without containing any insults, threats, or derogatory content.", "processing_time": 1.4985718727111816, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text is non-offensive as it simply mentions updating a website and adding a chat room, without containing any insults, threats, or derogatory content.\"\n}", "text": "I am going to try and update my site soon , I did however add a chat room .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain any offensive content, it simply expresses the speaker's opinion and the agreement of their friends.", "processing_time": 1.5692033767700195, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain any offensive content, it simply expresses the speaker's opinion and the agreement of their friends.\"\n}", "text": "I've told my friends and all of them agree with me ( some only partially ) .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory and racist language towards a specific group of people based on their race", "processing_time": 1.1928038597106934, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory and racist language towards a specific group of people based on their race\"\n}", "text": "It makes my flesh crawl when I see white girls having to be felt up as they walk down the hallways by big lipped spear chuckers. : attack", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The text contains a reference to a website that may have historical or educational content, without any offensive language or content.", "processing_time": 1.3331620693206787, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The text contains a reference to a website that may have historical or educational content, without any offensive language or content.\"\n}", "text": "You could try looking at the National socialist and Fascist Resource Centre at http://www.ufc.co.uk They cover a whole range of educational material .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement does not contain explicit insults, threats, or derogatory content. It merely expresses an opinion about the American media.", "processing_time": 1.324608564376831, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement does not contain explicit insults, threats, or derogatory content. It merely expresses an opinion about the American media.\"\n}", "text": "American Media appears to have a blackout on these developments .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "The statement expresses an opinion about a group but does not contain explicit insults, threats, or hatred. It is not offensive speech.", "processing_time": 1.4134125709533691, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"The statement expresses an opinion about a group but does not contain explicit insults, threats, or hatred. It is not offensive speech.\"\n}", "text": "And now this clip suggests they were suffering by having to live among heartless and barbarian Whites .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "explanation": "Contains derogatory content and anti-Semitic sentiment", "processing_time": 1.9210267066955566, "raw_model_output": "{\n  \"verdict\": 1,\n  \"explanation\": \"Contains derogatory content and anti-Semitic sentiment\"\n}", "text": "Apparently the Jews ca n't take it when the truth is spoken about them .", "true_label": 1, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech expressing a personal opinion on dietary choices.", "processing_time": 2.750073194503784, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech expressing a personal opinion on dietary choices.\"\n}", "text": "Whether or not one chooses to eat meat is a very personal decision .", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Expresses an opinion about financial decisions, does not contain explicit insults, threats, or hatred", "processing_time": 1.2939684391021729, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Expresses an opinion about financial decisions, does not contain explicit insults, threats, or hatred\"\n}", "text": "the money he is giving away to refugees and the third world is money Canada does not even have !", "true_label": 0, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "explanation": "Non-offensive speech, expressing understanding or realization", "processing_time": 1.6532132625579834, "raw_model_output": "{\n  \"verdict\": 0,\n  \"explanation\": \"Non-offensive speech, expressing understanding or realization\"\n}", "text": "Now I get it !", "true_label": 0, "dataset": "HateSpeechStormfront"}]}