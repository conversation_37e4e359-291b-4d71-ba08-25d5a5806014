import argparse
import os
from offensive_speech_detection.models import SingleAgentDetector
from offensive_speech_detection.evaluator import ModelEvaluator
from offensive_speech_detection.data_loader import get_dataset_loader

def main():
    """Run single agent evaluation"""
    parser = argparse.ArgumentParser(description="Run single agent offensive speech detection evaluation")
    parser.add_argument("--dataset", type=str, default="DynamicallyHate",
                        choices=["CHSD", "COLDataset", "ToxiCN", "ImplicitHate",
                                "HateSpeechOffensive", "HateSpeechStormfront", "DynamicallyHate"],
                        help="Name of the dataset to evaluate")
    parser.add_argument("--num_samples", type=int, default=10,
                        help="Number of samples to evaluate")
    parser.add_argument("--start_idx", type=int, default=0,
                        help="Starting index of samples")
    parser.add_argument("--model", type=str, default="gpt-3.5-turbo-0125",
                        help="Name of the model to use (only required for api/ollama providers)")
    parser.add_argument("--provider", type=str, choices=["api", "ollama", "local"], default="api",
                        help="Model provider")
    parser.add_argument("--ollama-base-url", type=str, default="http://localhost:11434", 
                        help="Ollama API base URL")
    parser.add_argument("--local-model-path", type=str,
                        help="Path to local model (used when provider is 'local')")

    args = parser.parse_args()
    
    if args.provider == "local" and not args.local_model_path:
        parser.error("--local-model-path is required when using a local provider.")

    # 当使用本地模型时，自动从路径解析模型名称
    if args.provider == "local":
        args.model = os.path.basename(args.local_model_path.rstrip('/\\'))

    print(f"Starting single agent evaluation on {args.dataset} dataset")
    print(f"Number of samples: {args.num_samples}, starting index: {args.start_idx}")
    print(f"Model provider: {args.provider}, model: {args.model}")

    model_path = args.model
    if args.provider == "local":
        model_path = args.local_model_path
        print(f"Using local model at: {model_path}")

    # 创建检测器
    detector = SingleAgentDetector(
        model=model_path,
        provider=args.provider,
        ollama_base_url=args.ollama_base_url,
        local_model_path=model_path  # Pass the local model path
    )
    
    # 创建评估器
    evaluator = ModelEvaluator(
        detector=detector,
        dataset_name=args.dataset,
        num_samples=args.num_samples,
        start_idx=args.start_idx
    )
    
    # 运行评估
    evaluator.evaluate()
    
    # 绘制指标图
    evaluator.plot_metrics()

if __name__ == "__main__":
    main() 