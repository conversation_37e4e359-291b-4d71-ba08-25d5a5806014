#!/usr/bin/env python3
"""
测试新的RoBERTa模型
验证模型能够正常加载和推理
"""

import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import numpy as np
from typing import Dict, List
import time
import os

class RoBERTaHateDetector:
    """RoBERTa仇恨言论检测器"""
    
    def __init__(self, model_path: str = "C:/Users/<USER>/Downloads/twitter-roberta-base-hate-latest"):
        """
        初始化RoBERTa检测器
        
        Args:
            model_path: 模型路径
        """
        self.model_path = model_path
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {self.device}")
        
        # 加载模型和分词器
        self._load_model()
        
    def _load_model(self):
        """加载模型和分词器"""
        try:
            print(f"正在加载RoBERTa模型: {self.model_path}")
            
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            print("✅ 分词器加载成功")
            
            # 加载模型
            self.model = AutoModelForSequenceClassification.from_pretrained(self.model_path)
            self.model.to(self.device)
            self.model.eval()
            print("✅ 模型加载成功")
            
            # 获取标签映射
            self.id2label = self.model.config.id2label
            self.label2id = self.model.config.label2id
            print(f"标签映射: {self.id2label}")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def detect(self, text: str) -> Dict:
        """
        检测文本是否包含仇恨言论
        
        Args:
            text: 输入文本
            
        Returns:
            检测结果字典
        """
        start_time = time.time()
        
        try:
            # 分词
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 推理
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits
                
            # 计算概率
            probs = F.softmax(logits, dim=-1)
            predicted_class_id = logits.argmax().item()
            confidence = probs.max().item()
            
            # 获取预测标签
            predicted_label = self.id2label[predicted_class_id]
            
            # 判断是否为仇恨言论
            # 根据模型的标签映射调整判断逻辑
            if predicted_label == "HATE":
                verdict = 1  # 仇恨言论
            else:
                verdict = 0  # 正常文本
                
            processing_time = time.time() - start_time
            
            return {
                "verdict": verdict,
                "confidence": confidence,
                "predicted_label": predicted_label,
                "all_probabilities": {
                    self.id2label[i]: prob.item() 
                    for i, prob in enumerate(probs[0])
                },
                "processing_time": processing_time,
                "method": "roberta_classification"
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                "verdict": 0,
                "confidence": 0.5,
                "error": str(e),
                "processing_time": processing_time,
                "method": "roberta_classification"
            }
    
    def batch_detect(self, texts: List[str]) -> List[Dict]:
        """批量检测"""
        results = []
        for text in texts:
            result = self.detect(text)
            results.append(result)
        return results
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            "model_path": self.model_path,
            "device": str(self.device),
            "num_labels": len(self.id2label),
            "labels": list(self.id2label.values()),
            "model_type": self.model.config.model_type,
            "vocab_size": self.model.config.vocab_size
        }

def test_roberta_model():
    """测试RoBERTa模型"""
    print("🧪 RoBERTa模型测试")
    print("=" * 60)
    
    # 检查模型路径
    model_path = "C:/Users/<USER>/Downloads/twitter-roberta-base-hate-latest"
    if not os.path.exists(model_path):
        print(f"❌ 模型路径不存在: {model_path}")
        return False
    
    try:
        # 初始化检测器
        detector = RoBERTaHateDetector(model_path)
        
        # 显示模型信息
        model_info = detector.get_model_info()
        print(f"\n📋 模型信息:")
        for key, value in model_info.items():
            print(f"   {key}: {value}")
        
        # 测试文本
        test_texts = [
            "I love this beautiful day!",  # 正常文本
            "You are so stupid and worthless!",  # 攻击性文本
            "I hate all people from that country",  # 仇恨言论
            "Thank you for your help",  # 正常文本
            "Go kill yourself, you idiot!",  # 极端攻击性
            "The weather is nice today",  # 中性文本
            "All women are inferior",  # 歧视性言论
            "I disagree with your opinion"  # 正常不同意见
        ]
        
        print(f"\n🔍 测试结果:")
        print("-" * 60)
        
        total_time = 0
        correct_predictions = 0
        
        for i, text in enumerate(test_texts, 1):
            result = detector.detect(text)
            total_time += result["processing_time"]
            
            verdict_text = "仇恨言论" if result["verdict"] == 1 else "正常文本"
            
            print(f"\n{i}. 文本: {text}")
            print(f"   预测: {verdict_text}")
            print(f"   置信度: {result['confidence']:.3f}")
            print(f"   标签: {result.get('predicted_label', 'N/A')}")
            print(f"   处理时间: {result['processing_time']:.3f}s")
            
            if "error" in result:
                print(f"   ❌ 错误: {result['error']}")
            else:
                # 简单的正确性检查（基于关键词）
                has_offensive_keywords = any(word in text.lower() for word in 
                                           ["stupid", "hate", "kill", "idiot", "worthless", "inferior"])
                expected_verdict = 1 if has_offensive_keywords else 0
                
                if result["verdict"] == expected_verdict:
                    correct_predictions += 1
                    print("   ✅ 预测符合预期")
                else:
                    print("   ⚠️ 预测与预期不符")
        
        # 统计信息
        avg_time = total_time / len(test_texts)
        accuracy = correct_predictions / len(test_texts)
        
        print(f"\n📊 测试统计:")
        print(f"   总测试样本: {len(test_texts)}")
        print(f"   符合预期: {correct_predictions}/{len(test_texts)}")
        print(f"   预期符合率: {accuracy:.1%}")
        print(f"   平均处理时间: {avg_time:.3f}s")
        print(f"   总处理时间: {total_time:.3f}s")
        
        # 批量测试
        print(f"\n🚀 批量测试:")
        batch_start = time.time()
        batch_results = detector.batch_detect(test_texts[:3])
        batch_time = time.time() - batch_start
        print(f"   批量处理3个样本耗时: {batch_time:.3f}s")
        
        print(f"\n✅ RoBERTa模型测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_roberta_model()
    if success:
        print("\n🎉 模型测试成功，可以集成到多智能体框架中")
    else:
        print("\n💥 模型测试失败，请检查模型路径和配置")
