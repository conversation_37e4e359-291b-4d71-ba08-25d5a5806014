{"run_id": "08568541", "dataset": "ImplicitHate", "model": "gpt-3.5-turbo-0125", "detector_type": "intelligent_coordination", "system_type": "intelligent_coordination", "num_samples": 100, "metrics": {"accuracy": 0.74, "precision": 0.6153846153846154, "recall": 0.8421052631578947, "f1": 0.7111111111111111, "confusion_matrix": [[42, 20], [6, 32]], "avg_processing_time": 3.671828806400299}, "results": [{"verdict": 1, "confidence": 0.5614804700016975, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9918437600135803, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.992 confidence", "processing_time": 0.6753871440887451}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory content and insinuates violence towards Jews, which is offensive and hateful.", "processing_time": 1.4486732482910156}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains a suggestion of violence towards Jews.", "processing_time": 2.8436105251312256, "retrieved_examples": "Example 1: \"white nationalism is horrible but jews have a right to a state of their own in palestine ? ? ?\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"jews call for white countries to allow all palestinians to move there | more :\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \": so you know about this guy ? the guy who is telling muslims to kill the jews ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.84671688079834, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 1, "dynamic_weight_predictions": 1, "consensus_predictions": 0, "avg_coordination_time": 2.84671688079834, "system_type": "intelligent_coordination"}, "text": ": peres : jews can live safely in palestinian state : whats this fear ? theyll kill jews ? today ?", "true_label": 1, "processing_time": 2.84671688079834, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5387274753302336, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.81); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8098198026418686, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.810 confidence", "processing_time": 0.06353497505187988}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The statement does not contain explicit insults, threats, or derogatory content, but rather expresses an opinion on the relationship between a person's behavior and their argument.", "processing_time": 0.8982415199279785}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it dismisses the importance of how women are treated based on optics.", "processing_time": 2.8146018981933594, "retrieved_examples": "Example 1: \"that's not what he was saying . women voting objectively caused white genocide / white demographic decline .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"well that dude doesn't respect white genders .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"i don't think what she said affected the optics particularly . she comes across as a humorless old boome\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.8202390670776367, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 2, "dynamic_weight_predictions": 2, "consensus_predictions": 0, "avg_coordination_time": 2.8334779739379883, "system_type": "intelligent_coordination"}, "text": "adhom . his treatment of women doesn't invalidate his position or argument on optics", "true_label": 0, "processing_time": 2.8202390670776367, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.541236761957407, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.83); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.829894095659256, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.830 confidence", "processing_time": 0.06009507179260254}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text expresses a historical opinion about desegregation in the 60s, without containing any offensive content.", "processing_time": 1.0232853889465332}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is considered offensive as it downplays the importance of desegregation and implies a negative view towards it.", "processing_time": 2.945427894592285, "retrieved_examples": "Example 1: \"black human rights campaigners managed it in the 50s and 60s.no good reason white human rights campaigners can't .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"it's not about any specific people  but about preserving everyone's freedom of conscience  incl yours .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"no one should be forced to do anything that violates their religious beliefs .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.950429677963257, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 3, "dynamic_weight_predictions": 3, "consensus_predictions": 0, "avg_coordination_time": 2.8724618752797446, "system_type": "intelligent_coordination"}, "text": ". in the 60s it was argued that desegregation violated freedom of conscience . it didn't work then either .", "true_label": 1, "processing_time": 2.950429677963257, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5509037598967552, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.91); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9072300791740417, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.907 confidence", "processing_time": 0.06055045127868652}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and promotes negative stereotypes towards specific political groups", "processing_time": 0.8015615940093994}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains derogatory terms and accusations towards certain groups based on political beliefs and race.", "processing_time": 2.1277310848236084, "retrieved_examples": "Example 1: \"since u support trump your probably racist too like the rest of us deplorables !\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"i've been called a racist 5 times today elder because i'm a white trump supporter.the democrats are the party of hate . it's all they have .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"now trump has done it . now the white house is calling all republicans racist . even the good republicans like me !\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.132869005203247, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 4, "dynamic_weight_predictions": 4, "consensus_predictions": 0, "avg_coordination_time": 2.68756365776062, "system_type": "intelligent_coordination"}, "text": "same here bro ! i'm called a white racist everyday because i support trump even though i'm black ! conservatives are now called nazi's !", "true_label": 0, "processing_time": 2.132869005203247, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5388822369277477, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.81); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8110578954219818, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.811 confidence", "processing_time": 0.05995750427246094}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive speech by promoting discrimination based on race, sexual orientation, and ability", "processing_time": 1.0469756126403809}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive. It describes a demographic group without any derogatory language or intent.", "processing_time": 2.7172882556915283, "retrieved_examples": "Example 1: \"white children\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"more white babies *\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"white children = = = > via\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.7209701538085938, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 5, "dynamic_weight_predictions": 5, "consensus_predictions": 0, "avg_coordination_time": 2.694244956970215, "system_type": "intelligent_coordination"}, "text": "* straight white able-bodied children", "true_label": 0, "processing_time": 2.7209701538085938, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5505657307803631, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9045258462429047, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.905 confidence", "processing_time": 0.06578421592712402}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory content and implies hatred towards a specific group (national socialists).", "processing_time": 1.1455132961273193}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it accuses someone of intentionally making national socialists look bad.", "processing_time": 2.967026710510254, "retrieved_examples": "Example 1: \"yes . national socialist party .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"are you a white nationalist ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"let's review our voices here : you're a #nazi and the people i've cited are employed by the most prestigious institutions in our country . .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.971930980682373, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 6, "dynamic_weight_predictions": 6, "consensus_predictions": 0, "avg_coordination_time": 2.740525960922241, "system_type": "intelligent_coordination"}, "text": "are you employed to make us real national socialists look bad ?", "true_label": 0, "processing_time": 2.971930980682373, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.54245893843472, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.84); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8396715074777603, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.840 confidence", "processing_time": 0.061033010482788086}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it simply mentions the topics of discussion for a live broadcast.", "processing_time": 0.8437724113464355}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive.", "processing_time": 2.0693039894104004, "retrieved_examples": "Example 1: \"i'll be live tonight on renegade broadcasting w / my friend pet<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> - joins us for a 30 min segment in hour 2 #trump2016\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"renegade roundtable : thought criminals ( 9-12- 15 ) - renegade broadcasting\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \": white pride radio 8p m est tonight . join in . we will support your shows when you have them .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.0750176906585693, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 7, "dynamic_weight_predictions": 7, "consensus_predictions": 0, "avg_coordination_time": 2.6454533508845737, "system_type": "intelligent_coordination"}, "text": "i'll be live tonight on renegade broadcasting with - show starts at 8 pm est . topics : npi   alt right", "true_label": 0, "processing_time": 2.0750176906585693, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5348483820756276, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.78); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.53)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7787870566050211, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.779 confidence", "processing_time": 0.06631588935852051}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory language ('stupid') directed at a specific group, which is offensive.", "processing_time": 0.9997260570526123}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it is discussing individuals who are described as being on the 'stupid side of the spectrum' but not as vocal white power enthusiasts.", "processing_time": 2.3419063091278076, "retrieved_examples": "Example 1: \"they aren't white supremacists are you kidding ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"they're not white nationalists . they're democrats .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"white nationalists aren't nazis stupid commie .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.3463873863220215, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 8, "dynamic_weight_predictions": 8, "consensus_predictions": 0, "avg_coordination_time": 2.6080701053142548, "system_type": "intelligent_coordination"}, "text": "no  not them . they're on the stupid side of the spectrum  but they're not vocal white power enthusiasts ( etc ) .", "true_label": 1, "processing_time": 2.3463873863220215, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5575562939047813, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.96); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9604503512382507, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.960 confidence", "processing_time": 0.07943463325500488}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The statement does not contain any offensive speech, it simply compares crime rates in different types of neighborhoods.", "processing_time": 1.0780115127563477}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply mentions the presence of crime in a comparative manner without targeting any specific group.", "processing_time": 2.152210235595703, "retrieved_examples": "Example 1: \"they do and you can check them the white on white crime is less than half the total % of black on black\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"black and hispanics are targeted more than their white counterparts for crime\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"they don't figure too much . population density has the biggest impact . in queens  blacks outearn whites but have a much higher crime rate .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.156529426574707, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 9, "dynamic_weight_predictions": 9, "consensus_predictions": 0, "avg_coordination_time": 2.557898918787638, "system_type": "intelligent_coordination"}, "text": "we have crime  but way less than in the homogenized neighborhoods .", "true_label": 0, "processing_time": 2.156529426574707, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5509815216064453, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9078521728515625, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.908 confidence", "processing_time": 0.06006169319152832}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory content by associating the New Century Foundation with white supremacy.", "processing_time": 0.8328900337219238}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech as it refers to a foundation and a think tank, without promoting or endorsing any hateful ideology.", "processing_time": 2.783170223236084, "retrieved_examples": "Example 1: \"white supremacists\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"from its ashes a white centric nationalist party is born\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"white because white supremacy\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.787538528442383, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 10, "dynamic_weight_predictions": 10, "consensus_predictions": 0, "avg_coordination_time": 2.580862879753113, "system_type": "intelligent_coordination"}, "text": "the new centry foundatin is a white supremacists thinking tank arm of the", "true_label": 0, "processing_time": 2.787538528442383, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5534790568053722, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9278324544429779, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.928 confidence", "processing_time": 0.0701591968536377}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains a reference to Antifa and labels them as terrorists, which is derogatory and offensive.", "processing_time": 0.8387508392333984}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it is seeking information about the strategy regarding Antifa and does not contain any offensive language or intent.", "processing_time": 2.632081985473633, "retrieved_examples": "Example 1: \"what do they have to say about antifa ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"btw  antifa is terrorizing people . i do not at all agree with their method\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"who is this antifa ? ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.637662887573242, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 11, "dynamic_weight_predictions": 11, "consensus_predictions": 0, "avg_coordination_time": 2.58602651682767, "system_type": "intelligent_coordination"}, "text": "what's the strategy on antifa   ? you know that the terrori", "true_label": 0, "processing_time": 2.637662887573242, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5554597824811935, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.94); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9436782598495483, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.944 confidence", "processing_time": 0.06690430641174316}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The mention of Ku Klux Klan, a hate group known for racism and violence, is offensive and promotes white supremacy ideology.", "processing_time": 0.9547507762908936}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply mentions the name of a t-shirt and a website.", "processing_time": 2.8095791339874268, "retrieved_examples": "Example 1: \"ku klux klan : the invisible empire t-shirt from the white resister :\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"white pride world wide t-shirt from the white resister : via\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"white pride world wide t-shirt from the white resister :\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.8138489723205566, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 12, "dynamic_weight_predictions": 12, "consensus_predictions": 0, "avg_coordination_time": 2.6050117214520774, "system_type": "intelligent_coordination"}, "text": "invisible empire - ku klux klan t-shirt from the white resister : via", "true_label": 0, "processing_time": 2.8138489723205566, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.547850960244735, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.88); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8828076819578806, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.883 confidence", "processing_time": 0.057118892669677734}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains explicit racism and exclusion based on race, making it offensive.", "processing_time": 0.8242058753967285}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it promotes racial discrimination and exclusion based on skin color.", "processing_time": 2.181534767150879, "retrieved_examples": "Example 1: \"geor<PERSON> <PERSON><PERSON> signed legislation making it so only free white men could immigrate to america\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"america had a white only immigration policy .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"white christian immigration only the usa was founded as a white christian nation #fact\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.18412709236145, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 13, "dynamic_weight_predictions": 13, "consensus_predictions": 0, "avg_coordination_time": 2.572635980752798, "system_type": "intelligent_coordination"}, "text": "the 1790 naturalization act explicitly stated that america was only for white", "true_label": 1, "processing_time": 2.18412709236145, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5513501949608326, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9108015596866608, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.911 confidence", "processing_time": 0.060155630111694336}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it simply mentions sharing information about religious freedom and a survey on hostility towards religion in America.", "processing_time": 1.1010522842407227}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive.", "processing_time": 2.0652565956115723, "retrieved_examples": "Example 1: \"check out this must read article from : we need to protect our religious liberty now  before it's too late .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"the human cost of the attack on religious liberty via #christianpersecution #1a\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"we must stand together to stop the attack on our 1st amendment religious freedoms pls rt\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.0700321197509766, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 14, "dynamic_weight_predictions": 14, "consensus_predictions": 0, "avg_coordination_time": 2.536735704966954, "system_type": "intelligent_coordination"}, "text": "i tagged before sharing this tweet.please also take notice of the information that i shared regarding #religiousfreedomundeniable : the survey of hostility to religion in america #religiousliberty #defendreligiousliberty via", "true_label": 0, "processing_time": 2.0700321197509766, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5491884835064411, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.89); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8935078680515289, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.894 confidence", "processing_time": 0.06364202499389648}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and incites hatred towards Jews and non-white individuals.", "processing_time": 0.8399209976196289}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it promotes the expulsion of a specific group of people based on their race and uses derogatory language.", "processing_time": 3.2693588733673096, "retrieved_examples": "Example 1: \"jews need to evacuate white nations and live in israel . that's something i'm not compromising on .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"all jews should be evicted from white countries\"\nVerdict: 1 (offensive speech)\n\nExample 3: \": jew supremacists call for mass invasion of america  demand all non-jews be deported from israel\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 3.27547550201416, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 15, "dynamic_weight_predictions": 15, "consensus_predictions": 0, "avg_coordination_time": 2.585985024770101, "system_type": "intelligent_coordination"}, "text": "jews don't want to live close to the non-white invaders they welcome #expeltheinvaders #withjewswelose", "true_label": 1, "processing_time": 3.27547550201416, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5607829168438911, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9862633347511292, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.986 confidence", "processing_time": 0.08302497863769531}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains derogatory content and promotes hatred towards specific groups based on race.", "processing_time": 0.9980571269989014}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains racial slurs and promotes violence against a specific group based on their race.", "processing_time": 2.9124319553375244, "retrieved_examples": "Example 1: \"mi<PERSON><PERSON> johnson the dallas police killer specifically targeted white cops so why isn't <PERSON><PERSON><PERSON> giving us a lecture on how racist he was ? hmmm !\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"another white officer killed by a black thug . <PERSON><PERSON><PERSON> and his race baiters must be smiling from ear to ear .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"he is always talking about killing white people but he couldn't kill couldn't kill a fly !\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.919316053390503, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 16, "dynamic_weight_predictions": 16, "consensus_predictions": 0, "avg_coordination_time": 2.606818214058876, "system_type": "intelligent_coordination"}, "text": "<PERSON><PERSON><PERSON> claims he doesn't know the motive of a serial killer who said he wanted to kill whites especially white cops . wow !", "true_label": 0, "processing_time": 2.919316053390503, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5564118164281051, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.95); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9512945314248403, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.951 confidence", "processing_time": 0.06534409523010254}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains derogatory language and compares a group of people to another in a negative manner.", "processing_time": 0.8798413276672363}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language by degrading a group of people as 'idiots' and comparing them negatively to another group.", "processing_time": 2.2329235076904297, "retrieved_examples": "Example 1: \"they're worse-useful idiot twits-fools that caused a massive flow of islam refug into eur th\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"idiotic savages.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"they are worse than slaves & black americans flocking to islam.\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.2366952896118164, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 17, "dynamic_weight_predictions": 17, "consensus_predictions": 0, "avg_coordination_time": 2.585046277326696, "system_type": "intelligent_coordination"}, "text": "idiots like this are worse than the orthodox muslims.", "true_label": 1, "processing_time": 2.2366952896118164, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5576933436095715, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.96); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9615467488765717, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.962 confidence", "processing_time": 0.0625772476196289}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults, accusations, and incitement of violence based on race.", "processing_time": 0.7817630767822266}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it suggests racism and supports genocide based on race.", "processing_time": 2.14450740814209, "retrieved_examples": "Example 1: \"do you hate white people ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"why do you hate white people ?\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"you sound violent and anti-white .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.1470773220062256, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 18, "dynamic_weight_predictions": 18, "consensus_predictions": 0, "avg_coordination_time": 2.560714668697781, "system_type": "intelligent_coordination"}, "text": "you sound like a racist . do you hate white people  support genocide of native whites ?", "true_label": 1, "processing_time": 2.1470773220062256, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5601624883711338, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.98); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9812999069690704, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.981 confidence", "processing_time": 0.06603026390075684}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains references to violence and confrontation with the police, which is considered offensive and potentially harmful.", "processing_time": 0.8403728008270264}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is not offensive.", "processing_time": 2.816293478012085, "retrieved_examples": "Example 1: \"antifa get violent again in portland via\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"did anybody hear if shooter was antifa\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"an attendee of charlottesville mentioned seeing taped bug foggers being thrown by antifa .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.8211371898651123, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 19, "dynamic_weight_predictions": 19, "consensus_predictions": 0, "avg_coordination_time": 2.5744211171802722, "system_type": "intelligent_coordination"}, "text": "heard there were slingshots at police . antifa crossed i - 5 bridge into vancouver after pa", "true_label": 0, "processing_time": 2.8211371898651123, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5547457374632359, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.94); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9379658997058868, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.938 confidence", "processing_time": 0.06305170059204102}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and insinuates racism towards white people", "processing_time": 0.7472894191741943}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive.", "processing_time": 2.1582462787628174, "retrieved_examples": "Example 1: \"are you white supremacists ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"you white supremacist ! !\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"are you a white nationalist ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.163144826889038, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 20, "dynamic_weight_predictions": 20, "consensus_predictions": 0, "avg_coordination_time": 2.5538573026657105, "system_type": "intelligent_coordination"}, "text": "are you inviting me on a helicopter ? you white supremacists are so generous !", "true_label": 0, "processing_time": 2.163144826889038, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.534345731139183, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.77); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.53)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.7747658491134644, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.775 confidence", "processing_time": 0.05862307548522949}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text appears to be a historical reference to a visit by a white man to Haiti in 1899, without containing any explicit insults, threats, or derogatory content.", "processing_time": 0.9542815685272217}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it describes a historical event without any derogatory language or intent.", "processing_time": 2.784919261932373, "retrieved_examples": "Example 1: \"white man visits the black republic : rt :\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"black riots  white flight | american renaissance\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"black officers torn between duty and race | american renaissance\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.789665699005127, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 21, "dynamic_weight_predictions": 21, "consensus_predictions": 0, "avg_coordination_time": 2.5650862739199685, "system_type": "intelligent_coordination"}, "text": "white man visits the black republic ( haiti  1899 ) | american renaissance", "true_label": 0, "processing_time": 2.789665699005127, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.538318507373333, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.81); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8065480589866638, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.807 confidence", "processing_time": 0.06166958808898926}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text expresses an opinion about the origins of Christmas traditions and does not contain offensive speech.", "processing_time": 1.1191742420196533}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it discusses the integration of pagan traditions with Christianity in a respectful manner.", "processing_time": 2.5206568241119385, "retrieved_examples": "Example 1: \"pagan traditions were european too . merry yule  happy solstice  felix saturnalia . only when the roman emperor <PERSON><PERSON> replaced a roman festival with a christian holiday  but kept the vacation days the same  was christmas pinned to december 25th . #westerncivilization\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"christmas isn't european ! ! ! hail odin xd\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \" what hateful ideology ? recognizing your open lies ?  \" christmas is not a european tradition \" \" - yes it is  as countless historians have documented it's rise in rome and dissemination throughout the european continent and the world  at the hands of europeans . \"\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.5274899005889893, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 22, "dynamic_weight_predictions": 22, "consensus_predictions": 0, "avg_coordination_time": 2.5633773478594692, "system_type": "intelligent_coordination"}, "text": "wrong . christians are supposed to engage with their pagan ( folk ways ) if they do not contradict christ . so christmas with the trees and lights  etc is a very european tradition .", "true_label": 1, "processing_time": 2.5274899005889893, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5541084855794907, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.93); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9328678846359253, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.933 confidence", "processing_time": 0.06716132164001465}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content towards <PERSON> and dismisses concerns about racism and violence in Chicago", "processing_time": 0.8430352210998535}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by making derogatory remarks about a public figure based on their race and criminalizing a specific racial group for criminal activities.", "processing_time": 2.2017698287963867, "retrieved_examples": "Example 1: \"another white officer killed by a black thug . <PERSON><PERSON><PERSON> and his race baiters must be smiling from ear to ear .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"murdering thugs were black he is white so no news or hate crime here . such godless hypocrisy !\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"68 shot and 8 killed in chicago over the thanksgiving weekend but the race baiters keep blaming white supremacy . can you say copout ?\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.2072980403900146, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 23, "dynamic_weight_predictions": 23, "consensus_predictions": 0, "avg_coordination_time": 2.547895638839058, "system_type": "intelligent_coordination"}, "text": "more black on black murders in chicago this week but the tax evader <PERSON> <PERSON><PERSON> is worried about supposedly racist jokes against his boss .", "true_label": 0, "processing_time": 2.2072980403900146, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5522584058344364, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.92); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9180672466754913, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.918 confidence", "processing_time": 0.06065249443054199}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and derogatory language directed at an individual", "processing_time": 0.7542250156402588}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is not offensive as it uses sarcasm to criticize someone's behavior.", "processing_time": 2.0684359073638916, "retrieved_examples": "Example 1: \"what a freaking loser .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"you reallly do go to low places don't you ? how ignorant of you .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"no racist but you are a loser !\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.073681116104126, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 24, "dynamic_weight_predictions": 24, "consensus_predictions": 0, "avg_coordination_time": 2.5281367003917694, "system_type": "intelligent_coordination"}, "text": "could you stoop any lower ? don't think so  loser .", "true_label": 0, "processing_time": 2.073681116104126, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.539521012455225, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.82); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8161680996417999, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.816 confidence", "processing_time": 0.06125640869140625}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Expresses an opinion about Confederate heritage being part of white American history, without containing explicit insults, threats, or derogatory content", "processing_time": 0.874875545501709}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply refers to Confederate heritage as a part of white American history without promoting any negative or harmful ideas.", "processing_time": 2.2086215019226074, "retrieved_examples": "Example 1: \"anything that has to do with white heritage .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"you're not for white heritage . you're for white supremacy heritage . you don't celebrate the whites that defeated the confederates .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"this is white heritage . honor it\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.21461820602417, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 25, "dynamic_weight_predictions": 25, "consensus_predictions": 0, "avg_coordination_time": 2.5155959606170653, "system_type": "intelligent_coordination"}, "text": "confederate heritage is white american history .", "true_label": 1, "processing_time": 2.21461820602417, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5487635309497516, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.89); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8901082475980123, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.890 confidence", "processing_time": 0.06307458877563477}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it simply mentions a university and a hashtag related to a project.", "processing_time": 2.400031805038452}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply mentions a university location and a project name.", "processing_time": 2.766362190246582, "retrieved_examples": "Example 1: \"university of washington . #projectsiege\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"university of chicago  ca . #projectsiege\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"university of california berkeley ( berkeley  ca ) #projectsiege\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.7725861072540283, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 26, "dynamic_weight_predictions": 26, "consensus_predictions": 0, "avg_coordination_time": 2.5254801970261793, "system_type": "intelligent_coordination"}, "text": "university of washington ( seattle  wa ) #projectsiege", "true_label": 0, "processing_time": 2.7725861072540283, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5446952730417252, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.86); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8575621843338013, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.858 confidence", "processing_time": 0.05816364288330078}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text expresses support for religious freedom and the First Amendment, without containing any offensive content.", "processing_time": 0.8466918468475342}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it promotes being a champion for Christ and mentions religious freedom and the 1st amendment.", "processing_time": 2.2442948818206787, "retrieved_examples": "Example 1: \"#religiousfreedom can't have too many champions . stand with : #rfra\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \": god is blessing your good prayers and generous support for the work of defending religious freedom across our natio ...\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"a wisconsin judge builds the case for #religiousfreedom .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.2491695880889893, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 27, "dynamic_weight_predictions": 27, "consensus_predictions": 0, "avg_coordination_time": 2.5152464707692466, "system_type": "intelligent_coordination"}, "text": "; divinity alum a #championforchrist in action . #religiousfreedom #1stamendment", "true_label": 0, "processing_time": 2.2491695880889893, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5555390156805515, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.94); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9443121254444122, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.944 confidence", "processing_time": 0.059783935546875}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory terms and insults towards specific groups, indicating offensive speech.", "processing_time": 0.7407956123352051}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive due to the use of derogatory terms like 'shitlibs' and 'cucks'.", "processing_time": 2.747459650039673, "retrieved_examples": "Example 1: \"we should not go back to cville under any circumstances . i know from people inside the univ . that they\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"i do hope people will sue c-ville and the state of virginia for not protecting citizens .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"virginia cuckservative protested for white supremacy at townhall via\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.7528610229492188, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 28, "dynamic_weight_predictions": 28, "consensus_predictions": 0, "avg_coordination_time": 2.523732704775674, "system_type": "intelligent_coordination"}, "text": "cville and the state of virginia are run by shitlibs and cucks as we know  all this wil", "true_label": 1, "processing_time": 2.7528610229492188, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5604723729193211, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.98); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9837789833545685, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.984 confidence", "processing_time": 0.07010817527770996}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive speech expressing criticism of <PERSON>'s posture, related to political actions", "processing_time": 0.8113977909088135}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply criticizes <PERSON>'s posture without any derogatory language or hate speech.", "processing_time": 2.0777595043182373, "retrieved_examples": "Example 1: \"secure white house against russia\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"le pen & putin denounce donald ' the cuck ' trump's sneak #syriastrikes . # via\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"liberal democrats hate white people and despise america ! where is the kgb when you need them ? #putin\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.0814924240112305, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 29, "dynamic_weight_predictions": 29, "consensus_predictions": 0, "avg_coordination_time": 2.5084830399217277, "system_type": "intelligent_coordination"}, "text": "white house criticizes v<PERSON><PERSON><PERSON> putins posture #russia #putin", "true_label": 0, "processing_time": 2.0814924240112305, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.550372302532196, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9029784202575684, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.903 confidence", "processing_time": 0.06153440475463867}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain any offensive speech, it is a neutral statement expressing an opinion about someone's intelligence.", "processing_time": 1.1800875663757324}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply describes someone as bright and implies that they may not listen despite being intelligent.", "processing_time": 2.4546868801116943, "retrieved_examples": "Example 1: \"bit late now,and he's supposed to be clever.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"he's a very bitter man\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"he won't what a shame for you . i'm white i'm bright and i hate you\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.4602785110473633, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 30, "dynamic_weight_predictions": 30, "consensus_predictions": 0, "avg_coordination_time": 2.5068762222925822, "system_type": "intelligent_coordination"}, "text": "even if i tell him he won't listen because he's quite bright, actually.", "true_label": 1, "processing_time": 2.4602785110473633, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5569832623004913, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.96); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9558660984039307, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.956 confidence", "processing_time": 0.06989526748657227}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and promotes hate speech towards an individual", "processing_time": 0.78694748878479}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it accuses 'spencer' of spewing bigotry and hate.", "processing_time": 2.1896450519561768, "retrieved_examples": "Example 1: \"spencer is a racist zionist\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"racist hatemonger robert spencer the ignorant and misleaded\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"spencer - you are enslaved by your own hate .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.193960666656494, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 31, "dynamic_weight_predictions": 31, "consensus_predictions": 0, "avg_coordination_time": 2.496782172110773, "system_type": "intelligent_coordination"}, "text": "spencer has so much free time to spew bigotry considering how churches keep canceling his hate talks", "true_label": 0, "processing_time": 2.193960666656494, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5616645887494087, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9933167099952698, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.993 confidence", "processing_time": 0.06210470199584961}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit reference to racism, ethnic cleansing, and a known white supremacist, <PERSON>.", "processing_time": 0.807631254196167}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it discusses racism and ethnic cleansing, which are sensitive and harmful topics.", "processing_time": 2.190962076187134, "retrieved_examples": "Example 1: \"david duke : the racial murder and rape of white women in america | read more : via\"\nVerdict: 1 (offensive speech)\n\nExample 2: \": david duke : the racial murder and rape of white women in america\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"  \" : \" \" : david duke : the racial murder and rape of white women in america via \" \" \"\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.1977269649505615, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 32, "dynamic_weight_predictions": 32, "consensus_predictions": 0, "avg_coordination_time": 2.4874366968870163, "system_type": "intelligent_coordination"}, "text": "david duke : the real racism of the ethnic cleansing of european americans at universities via", "true_label": 0, "processing_time": 2.1977269649505615, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.55435810983181, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.93486487865448, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.935 confidence", "processing_time": 0.06334567070007324}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains a derogatory term and promotes hatred and violence towards a specific racial group", "processing_time": 0.7765519618988037}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains language promoting racial violence and discrimination.", "processing_time": 2.0231916904449463, "retrieved_examples": "Example 1: \"more black white supremacists .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"no country for minority whites : racist attacks by violent non-whites rising in america | more :\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"white people need to watch their backs . there is a war upon us .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.0304696559906006, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 33, "dynamic_weight_predictions": 33, "consensus_predictions": 0, "avg_coordination_time": 2.473589210799246, "system_type": "intelligent_coordination"}, "text": "war afoot now : rampantblack terrorism targeting whites >", "true_label": 1, "processing_time": 2.0304696559906006, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.559377633035183, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.98); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9750210642814636, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.975 confidence", "processing_time": 0.060233354568481445}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain explicit insults, threats, hatred, or derogatory content. It merely mentions a group in a neutral manner.", "processing_time": 0.8318185806274414}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it is discussing the naming and mention of groups like Antifa and the 'alt-left'.", "processing_time": 2.549459218978882, "retrieved_examples": "Example 1: \"what do they have to say about antifa ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"can you name any such antifa groups ? because all i've heard in the media are about violent alt right nazis .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"denounce antifa and violent left extremism by the end of this  might even seek to name them as a terror\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.5557971000671387, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 34, "dynamic_weight_predictions": 34, "consensus_predictions": 0, "avg_coordination_time": 2.4760070898953606, "system_type": "intelligent_coordination"}, "text": " does antifa   \" or how you might say  the alt-left \" \" get even a dishonorable mention ? follow \"", "true_label": 0, "processing_time": 2.5557971000671387, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5434368550777435, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.85); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8474948406219482, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.847 confidence", "processing_time": 0.059216976165771484}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit language promoting racial superiority and derogatory remarks towards specific individuals", "processing_time": 0.8298296928405762}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it promotes the preservation of a specific race (white race) and implies exclusion based on race.", "processing_time": 2.604809522628784, "retrieved_examples": "Example 1: \"we will make america white again ...\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"we all need to stick together  for the white race\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"pl . ensure victory of for ur own survival we are back to support the leader\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.611579656600952, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 35, "dynamic_weight_predictions": 35, "consensus_predictions": 0, "avg_coordination_time": 2.4798805918012348, "system_type": "intelligent_coordination"}, "text": "we are back to support you and you alone will win preserve our beautiful white race including hillary types", "true_label": 1, "processing_time": 2.611579656600952, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.550335122893254, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9026809831460317, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.903 confidence", "processing_time": 0.05827188491821289}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains a call for restricting free speech based on race, which is offensive and discriminatory.", "processing_time": 0.8409397602081299}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is not offensive as it discusses a poll result about non-whites in California wanting a ban on free speech for white nationalists.", "processing_time": 2.236694812774658, "retrieved_examples": "Example 1: \"poll : california non-whites wants ban on free speech for white nationalists #swrm #us #white via\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"poll : california non-whites wants ban on free speech for white nationalists #swrm #nonwhites #us via\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"the ' charming ' white supremacist leader stoking hate across california via\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.240532636642456, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 36, "dynamic_weight_predictions": 36, "consensus_predictions": 0, "avg_coordination_time": 2.4732320374912686, "system_type": "intelligent_coordination"}, "text": "poll : california non-whites wants ban on free speech for white nationalists via", "true_label": 0, "processing_time": 2.240532636642456, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.553511908898751, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9280952711900076, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.928 confidence", "processing_time": 0.056975364685058594}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains a call to stop cultural appropriation and implies negativity towards a specific group based on race", "processing_time": 0.8431837558746338}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it promotes anti-white sentiments and accuses cultural appropriation.", "processing_time": 2.2348954677581787, "retrieved_examples": "Example 1: \"stop anti-white racism you crackers\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"white power | read more : via\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"yet more anti white propaganda\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.237534999847412, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 37, "dynamic_weight_predictions": 37, "consensus_predictions": 0, "avg_coordination_time": 2.4668618472846777, "system_type": "intelligent_coordination"}, "text": "stop anti-white cultural appropriation | more : via", "true_label": 1, "processing_time": 2.237534999847412, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5496856905519962, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8974855244159698, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.897 confidence", "processing_time": 0.060009002685546875}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive speech with derogatory remarks towards Islam", "processing_time": 0.7511298656463623}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech advocating for the banning of Islam.", "processing_time": 2.073101758956909, "retrieved_examples": "Example 1: \"islam must be banned from all civilized societies\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"ban that pos plague called islam ! ! ! they are illegal here ! ! !\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"the world would be a better place without islam.\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.076183557510376, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 38, "dynamic_weight_predictions": 38, "consensus_predictions": 0, "avg_coordination_time": 2.4565808396590385, "system_type": "intelligent_coordination"}, "text": "it has one useful purpose. fancy toilet paper. ban islam islam bad", "true_label": 1, "processing_time": 2.076183557510376, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5514060979088147, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.911248783270518, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.911 confidence", "processing_time": 0.06517553329467773}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains an explicit insult towards the sitting Prime Minister of India, which is offensive and derogatory.", "processing_time": 0.8122329711914062}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it involves insulting a sitting Prime Minister and implies harm through radiation.", "processing_time": 2.10138201713562, "retrieved_examples": "Example 1: \"life of indian pm not his private choice to be put at risk on invitation of jokers like na<PERSON><PERSON> s<PERSON><PERSON> ;\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"how long we can tolerate such abuses ? pl . radiate\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"congress hurled every abuse in the dictionary at me but i kept quiet because that's my culture : narendra modi\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.106802463531494, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 39, "dynamic_weight_predictions": 39, "consensus_predictions": 0, "avg_coordination_time": 2.447612163348076, "system_type": "intelligent_coordination"}, "text": "insulting sitting pm this manner indian national cancer can't avoid radiation", "true_label": 0, "processing_time": 2.106802463531494, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5249670669436455, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.70); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.52)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.6997365355491638, "reasoning": "Hybrid classification using best_multi_label with 0.700 confidence", "processing_time": 0.06386661529541016}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain explicit insults, threats, hatred, or derogatory content. It expresses an opinion about the actions of a specific organization.", "processing_time": 1.****************}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it downplays or dismisses the persecution of revisionists, which can be seen as insensitive or disrespectful.", "processing_time": 2.***************, "retrieved_examples": "Example 1: \"german government admits it cannot account for 600000 of its 1.1 million muslim refugees : its not just germ ...\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"jewish reporter in germany : ' it was racist to stop refugee luggage porters ' via\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \": germany : hundreds of treason complaints against merkel over foreign invasion\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.**************, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 40, "dynamic_weight_predictions": 40, "consensus_predictions": 0, "avg_coordination_time": 2.****************, "system_type": "intelligent_coordination"}, "text": "reporters without borders ignore persecution of revisionists : reporters without borders ranks germany on place ...", "true_label": 0, "processing_time": 2.**************, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.559182308614254, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.97); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.973458468914032, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.973 confidence", "processing_time": 0.05856060981750488}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content towards a specific group (#altright) and promotes racism (#whitepropaganda)", "processing_time": 0.7980761528015137}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it associates the alt-right with racism and promotes white propaganda.", "processing_time": 2.6539814472198486, "retrieved_examples": "Example 1: \"new republic : the rise of white identity politics via #altright\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"alt-right is anti-zionism ( jews ) and white genocide\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"#altrightmeans helping your fellow white man awaken to the realities of the world .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.6606099605560303, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 41, "dynamic_weight_predictions": 41, "consensus_predictions": 0, "avg_coordination_time": 2.458504304653261, "system_type": "intelligent_coordination"}, "text": "the #altright is a mask for racism and isolationism . #whitepropaganda", "true_label": 0, "processing_time": 2.6606099605560303, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5537379421293736, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9299035370349884, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.930 confidence", "processing_time": 0.06882619857788086}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain any offensive speech, just a statement about a location.", "processing_time": 0.8262839317321777}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply mentions a location without any derogatory or harmful language.", "processing_time": 2.5865848064422607, "retrieved_examples": "Example 1: \"i suspect this map is more realistic about cultural divide in ok  tx but i'd sure like to include them if we were independent .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"texas : cruz + 1\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"that is certainly true .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.5925819873809814, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 42, "dynamic_weight_predictions": 42, "consensus_predictions": 0, "avg_coordination_time": 2.4616966304324923, "system_type": "intelligent_coordination"}, "text": "i'm fairly sure it was this is texas .", "true_label": 0, "processing_time": 2.5925819873809814, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5413265228271484, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.83); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8306121826171875, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.831 confidence", "processing_time": 0.06738805770874023}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive; conveys a traditional saying about moderation in eating", "processing_time": 0.8157303333282471}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is not offensive as it simply conveys a message about moderation in eating habits to avoid being laughed at.", "processing_time": 2.629794120788574, "retrieved_examples": "Example 1: \"careful don't feed the tribesman\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"europeans ate raw meat to survive nordic cold ; islamists are just food for europeans .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"humans must give up primitive food like meat of dog or beef or camel or donkey etc\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.6353187561035156, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 43, "dynamic_weight_predictions": 43, "consensus_predictions": 0, "avg_coordination_time": 2.4657343542853067, "system_type": "intelligent_coordination"}, "text": "old norse wisdom : don't eat too much or people will laugh at you .", "true_label": 0, "processing_time": 2.6353187561035156, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5525251006086667, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.92); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9202008048693339, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.920 confidence", "processing_time": 0.0604703426361084}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and racist remarks towards individuals based on their ethnicity and religion.", "processing_time": 0.8172752857208252}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains derogatory language targeting a specific group based on ethnicity.", "processing_time": 2.286731481552124, "retrieved_examples": "Example 1: \"some europeans are non white\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"euro jews are white\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"jews are not white .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.2916555404663086, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 44, "dynamic_weight_predictions": 44, "consensus_predictions": 0, "avg_coordination_time": 2.4617780176076023, "system_type": "intelligent_coordination"}, "text": "jeeeewwww aree not of european desent there non white", "true_label": 1, "processing_time": 2.2916555404663086, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5573904365301132, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.96); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9591234922409058, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.959 confidence", "processing_time": 0.05762195587158203}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The mention of 'holocaust' and 'treblinka' in the context of debate and news can be offensive due to the sensitive nature of these topics.", "processing_time": 0.9418394565582275}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech.", "processing_time": 2.4433138370513916, "retrieved_examples": "Example 1: \"' holocaust ' debate / comments / news burden of proof : the fables of r<PERSON><PERSON>'s teapot and blobel's vanished ...\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"' holocaust ' debate / comments / news more ' holocaust ' absurdities : auschwitz eyewitness / survivor ' dov pa ...\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"' holocaust ' debate / comments / news the mysterious polish witness to massive transfers of jews ? : does anyon ...\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.4486844539642334, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 45, "dynamic_weight_predictions": 45, "consensus_predictions": 0, "avg_coordination_time": 2.4614870495266383, "system_type": "intelligent_coordination"}, "text": "' holocaust ' debate / comments / news sturdy colls ' tiles from treblinka tested ? ? : more on the fabulously sh ...", "true_label": 0, "processing_time": 2.4486844539642334, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5605848580598831, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.98); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9846788644790649, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.985 confidence", "processing_time": 0.06176471710205078}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and accusations based on race, targeting white individuals", "processing_time": 0.998145341873169}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it expresses a desire for white people to leave and labels the person as 'antiwhite'.", "processing_time": 2.6315431594848633, "retrieved_examples": "Example 1: \"you sound violent and anti-white .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"are you anti-white ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"you are a white anti white  the worst kind .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.6376564502716064, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 46, "dynamic_weight_predictions": 46, "consensus_predictions": 0, "avg_coordination_time": 2.465316819108051, "system_type": "intelligent_coordination"}, "text": "sounds like you want white people to leave . sounds like you are an antiwhite .", "true_label": 0, "processing_time": 2.6376564502716064, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5445589013397694, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.86); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8564712107181549, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.856 confidence", "processing_time": 0.06248760223388672}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain explicit insults, threats, hatred, or derogatory content. It mentions a political figure in a neutral context.", "processing_time": 0.8565175533294678}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by promoting the idea of bringing down anti-semitism laws, which can be seen as discriminatory and harmful.", "processing_time": 2.7454681396484375, "retrieved_examples": "Example 1: \"trump mourns jewish holocaust victims and vows to fight anti-semitism in speech to jewish group\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"dream night and day of the day mueller puts the hammer down on trump  jr  jarad  et al .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"trump supports jews . jews support whitegenocide . get the picture ?\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.7510085105895996, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 47, "dynamic_weight_predictions": 47, "consensus_predictions": 0, "avg_coordination_time": 2.471395365735318, "system_type": "intelligent_coordination"}, "text": "trump  the hammer  will be the one that brings down the anti-semitism laws . remember your trump vigor then .", "true_label": 1, "processing_time": 2.7510085105895996, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5205403193831444, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.66); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.52)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.664322555065155, "reasoning": "Hybrid classification using best_zero_shot with 0.664 confidence", "processing_time": 0.06703305244445801}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains derogatory content towards liberals, implying they are not smart, which is offensive.", "processing_time": 0.8132364749908447}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it promotes hate towards a specific group based on their political beliefs.", "processing_time": 2.2302372455596924, "retrieved_examples": "Example 1: \"i am a southern liberal and their is so many racist  stupid people running in the rural towns that think if your not white your the enemy .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"white liberals are all over the south  you just can't see it in a graph like this\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"only racist if you're white  this just makes you smart .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.234557628631592, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 48, "dynamic_weight_predictions": 48, "consensus_predictions": 0, "avg_coordination_time": 2.4664612462123237, "system_type": "intelligent_coordination"}, "text": "the south isnt racist they just hate liberals which means they're smart !", "true_label": 0, "processing_time": 2.234557628631592, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.530824601650238, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.75); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7465968132019043, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.747 confidence", "processing_time": 0.0644083023071289}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains discriminatory language based on religion and nationality, promoting prejudice and intolerance.", "processing_time": 0.8205108642578125}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by discriminating against individuals based on their religion (Muslims). It implies a preference for individuals from non-Muslim countries, which is discriminatory and offensive.", "processing_time": 2.1698668003082275, "retrieved_examples": "Example 1: \"we don't want any of your muslims tho\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"i agree . we need to understand islamic ideology and the threat it poses .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"we do not have to except unvetted immigrants o<PERSON><PERSON>'s scheme to alter america's make up ..\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.1748619079589844, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 49, "dynamic_weight_predictions": 49, "consensus_predictions": 0, "avg_coordination_time": 2.460510239309194, "system_type": "intelligent_coordination"}, "text": "no we need honest, hard working, clever intelligent, human beings from non muslim countries and we al", "true_label": 1, "processing_time": 2.1748619079589844, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5562445819377899, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.95); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9499566555023193, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.950 confidence", "processing_time": 0.0653080940246582}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and offensive remarks based on race and sexuality.", "processing_time": 0.7637920379638672}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains derogatory remarks based on race and implies inappropriate behavior.", "processing_time": 2.109801769256592, "retrieved_examples": "Example 1: \"shaun is black . why are you obsessed with his race ? you like white boys ? skinny lil white boys ?\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"he is white  as a dna test will show\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"he isn't white .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.1168007850646973, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 50, "dynamic_weight_predictions": 50, "consensus_predictions": 0, "avg_coordination_time": 2.453636050224304, "system_type": "intelligent_coordination"}, "text": "ignore him . he's into little white boys & his heart is broken that shaun isn't white . serious weirdo . wants dna .", "true_label": 0, "processing_time": 2.1168007850646973, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5594453141093254, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.98); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9755625128746033, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.976 confidence", "processing_time": 0.07176899909973145}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The mention of 'white on white crime' can be considered offensive as it perpetuates racial stereotypes and may incite division or hatred.", "processing_time": 0.9174966812133789}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it is discussing a topic without any derogatory language or intent.", "processing_time": 123.08163070678711, "retrieved_examples": "Example 1: \"what about white on white crime ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"info black on white crime in the us .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"yawn ! but white on white gun crime killing thousands doesn't ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 123.08707857131958, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 51, "dynamic_weight_predictions": 51, "consensus_predictions": 0, "avg_coordination_time": 4.818997668284996, "system_type": "intelligent_coordination"}, "text": "how about white on white crime vua gun in usa ?", "true_label": 0, "processing_time": 123.08707857131958, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5538249015808105, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9305992126464844, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.931 confidence", "processing_time": 0.06159210205078125}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The statement provides demographic information without containing any explicit insults, threats, or derogatory content. It does not qualify as offensive speech.", "processing_time": 0.8960459232330322}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply discusses the demographic composition of a population.", "processing_time": 2.782052993774414, "retrieved_examples": "Example 1: \"what % of white population ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"about 64 % white .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"it's clearly far majority white\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.788362741470337, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 52, "dynamic_weight_predictions": 52, "consensus_predictions": 0, "avg_coordination_time": 4.779946996615483, "system_type": "intelligent_coordination"}, "text": "its 75 % white which is very high when the > 22 population is only about half white", "true_label": 0, "processing_time": 2.788362741470337, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5494704470038414, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8957635760307312, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.896 confidence", "processing_time": 0.06035494804382324}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains blaming and derogatory content towards the mainstream media, linking them to violence in Ferguson", "processing_time": 0.8210887908935547}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it criticizes the mainstream media's influence on the events in Ferguson without targeting any specific group of people.", "processing_time": 2.2787396907806396, "retrieved_examples": "Example 1: \"after last night, the rioters of #ferguson should be considered terrorists and be dealt with accordingly\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"white protester attacked in #ferguson negrogeddon\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"just shows you what can happen when the media controls the narrative .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.284940242767334, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 53, "dynamic_weight_predictions": 53, "consensus_predictions": 0, "avg_coordination_time": 4.732871397486273, "system_type": "intelligent_coordination"}, "text": "every injury  act of property destruction  and shot fired tonight can be laid at the feet of the mainstream media . #ferguson", "true_label": 0, "processing_time": 2.284940242767334, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5410439111292362, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.83); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8283512890338898, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.828 confidence", "processing_time": 0.07034778594970703}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement promotes segregation based on race and implies a preference for a society without white individuals, which can be considered offensive and discriminatory.", "processing_time": 0.9273293018341064}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it discusses the idea of separate societies without promoting hate or discrimination.", "processing_time": 2.38551664352417, "retrieved_examples": "Example 1: \"also  we don't live under white supremacy ..\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"obviously  liberia isn't like white america because it was founded by somebody else's babies\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"i guess whites and blacks dont belong in civilized countries according to your rhetoric .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.391192674636841, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 54, "dynamic_weight_predictions": 54, "consensus_predictions": 0, "avg_coordination_time": 4.689506976692765, "system_type": "intelligent_coordination"}, "text": "so we should have our own separate societies  right ? i don't think there are any white supremacists in liberia .", "true_label": 0, "processing_time": 2.391192674636841, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5114167183637619, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.59); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.51)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.5913337469100952, "reasoning": "Hybrid classification using best_similarity with 0.591 confidence", "processing_time": 0.058655500411987305}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it is merely promoting a sale on a CD with discounted shipping rates.", "processing_time": 0.87538743019104}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it solely promotes a shipping offer for a CD.", "processing_time": 1.9958038330078125, "retrieved_examples": "Example 1: \"my book is available at  international postage available . download on amazon kindle\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"white friday\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"merry christmas from identity evropa !\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 1.9980666637420654, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 55, "dynamic_weight_predictions": 55, "consensus_predictions": 0, "avg_coordination_time": 4.64057169827548, "system_type": "intelligent_coordination"}, "text": "1cent shipping on skinfull cd until dec . 4th all over the world", "true_label": 0, "processing_time": 1.9980666637420654, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5548057891428471, "reasoning": "激进优化协调决策: deberta(原权重:0.42→优化权重:0.12, 预测:0, 置信度:0.94); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.30→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9384463131427765, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.938 confidence", "processing_time": 0.06606173515319824}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and hateful language towards a specific group (Muslims) and promotes violence", "processing_time": 0.8351390361785889}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains derogatory language towards a specific group of people and promotes violence.", "processing_time": 2.305055618286133, "retrieved_examples": "Example 1: \"#spreadthetruth radical muslims came out again on the streets ( cont )\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"boston marathon jihad murderer knew of brother's role in 9/11 / 11 murders of jews\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"you know they're fundamentally nuts when muslims hate other muslims enough to kill them\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.42499999999999993, "llm_single": 0.27499999999999997, "llm_retrieval": 0.3}, "coordination_time": 2.308669090270996, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 56, "dynamic_weight_predictions": 56, "consensus_predictions": 0, "avg_coordination_time": 4.5989305802753995, "system_type": "intelligent_coordination"}, "text": "radical radical radical radical radical radical muslims cheer terror bombings in boston", "true_label": 1, "processing_time": 2.308669090270996, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5505579387148221, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.904463509718577, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.904 confidence", "processing_time": 0.06602787971496582}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content towards a specific racial group, promoting racial stereotypes and division.", "processing_time": 0.8203041553497314}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it promotes racial stereotypes and discrimination by suggesting that black people are responsible for their own lack of achievement.", "processing_time": 2.1857872009277344, "retrieved_examples": "Example 1: \"there are both white and black supremacists  but when it comes to being self-sufficient  whites are much more capable  and you hold us back .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"white privilege is a myth  i've been discriminated against 4 jobs  housing  education assistance  n welfare because i was white .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"it the blacks that keep us down as a people i see it\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.191884994506836, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 57, "dynamic_weight_predictions": 57, "consensus_predictions": 0, "avg_coordination_time": 4.5567017103496354, "system_type": "intelligent_coordination"}, "text": "i spent my entire life being told racist whites hold back black people  who are just like us but we keep them from acivieving like us . lies .", "true_label": 0, "processing_time": 2.191884994506836, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5432458147406578, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.85); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8459665179252625, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.846 confidence", "processing_time": 0.05803275108337402}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains language that suggests a negative attitude towards other races, referring to them as an 'invasion'. This can be considered offensive and discriminatory.", "processing_time": 1.0992276668548584}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it refers to a 'racial invasion' which implies a negative and discriminatory view towards other races.", "processing_time": 2.554184675216675, "retrieved_examples": "Example 1: \"i can tell you myself among many white nationalists don't hate anyone . we're aware to the fact that humans are different among race .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"i can appreciate that . i'm against non white immigration because far too many hate whites .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"i'm not going to hate myself for my race nor will i hate you back . i love my heritage  that's it .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.558561325073242, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 58, "dynamic_weight_predictions": 58, "consensus_predictions": 0, "avg_coordination_time": 4.522251014051767, "system_type": "intelligent_coordination"}, "text": "being racist and being aware of a race problem are different things . i don't hate a race . i just love mine . we have a racial invasion here .", "true_label": 1, "processing_time": 2.558561325073242, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5567134494582813, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.95); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9537075956662496, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.954 confidence", "processing_time": 0.06297421455383301}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit call for registering a group as a hate group, similar to the KKK, indicating offensive speech.", "processing_time": 0.8504757881164551}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive. It is a suggestion to register a group as a hate group, similar to the KKK, but does not contain any explicit offensive language or attacks.", "processing_time": 2.7999556064605713, "retrieved_examples": "Example 1: \"is a racist hate group . they have blood on their hands .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"another liberal i presume lol . ok you wanna defend this racist hate group ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"just another racist hate group . it's sad that people are still so intolerant and oppressive . the future is not yours .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.805237293243408, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 59, "dynamic_weight_predictions": 59, "consensus_predictions": 0, "avg_coordination_time": 4.493149086580438, "system_type": "intelligent_coordination"}, "text": "pathetic . can someone go ahead and register the as a hate group  just like the kkk ?", "true_label": 0, "processing_time": 2.805237293243408, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5560556314885616, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.95); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.948445051908493, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.948 confidence", "processing_time": 0.05689883232116699}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The term 'cuckservatives' is derogatory and offensive, targeting a specific group of conservatives. It is considered offensive speech.", "processing_time": 1.1053261756896973}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it discusses <PERSON>'s stance on political correctness and federal judges without using derogatory language.", "processing_time": 2.1970553398132324, "retrieved_examples": "Example 1: \"cuckservatives : trump refuses to bow to political correctness on federal judges via\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"cuckservatives : trump and white identitypolitics\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"trump courts conservatives with solid judges :\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.202510356903076, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 60, "dynamic_weight_predictions": 60, "consensus_predictions": 0, "avg_coordination_time": 4.454971774419149, "system_type": "intelligent_coordination"}, "text": "cuckservatives : trump refuses to bow to political correctness on federaljudges", "true_label": 1, "processing_time": 2.202510356903076, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5254685431718826, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.70); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.703748345375061, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.704 confidence", "processing_time": 0.06139874458312988}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains derogatory content towards Indian journalists and <PERSON> based on nationality and political affiliation.", "processing_time": 1.1475577354431152}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it targets Indian journalists for not asking tough questions to <PERSON>, implying bias or lack of professionalism.", "processing_time": 3.340041160583496, "retrieved_examples": "Example 1: \"for the time  indian editors should do investigative stories about each other .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"<PERSON><PERSON><PERSON> wanted bjp nomination for pm post but outwitted by modi ; was also pro-sonia earlier .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"cong stooges in msm who underplay the darkest event in indian history #emergency have the gall to accuse cong's opponents of fascism .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 3.3438944816589355, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 61, "dynamic_weight_predictions": 61, "consensus_predictions": 0, "avg_coordination_time": 4.4367573925706205, "system_type": "intelligent_coordination"}, "text": "these same indian jouranlists never asked tough questions to sonia gandhi .", "true_label": 0, "processing_time": 3.3438944816589355, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5559234954416752, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.95); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9473879635334015, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.947 confidence", "processing_time": 0.05673980712890625}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains a violent and threatening language towards a group of individuals, suggesting they should be executed for treason.", "processing_time": 0.8984725475311279}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains a call for violence and suggests hanging individuals for treason, which is offensive.", "processing_time": 2.180675745010376, "retrieved_examples": "Example 1: \"Those filthy traitors must hang!!!\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I don't need someone to explain to me how the Dems are outright traitors. What I need explained to me is how do we get these fuckers hanged.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"#TarAndFeather THESE #DeepState motherf**king Government drones and squares, THEN THROW THEM IN GITMO FOR ATTEMPTING TO SUBVERT OUR DEMOCRACY.\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.1848974227905273, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 62, "dynamic_weight_predictions": 62, "consensus_predictions": 0, "avg_coordination_time": 4.400437070477393, "system_type": "intelligent_coordination"}, "text": "THEN WHY HAVEN'T THEY BEEN HUNG FOR TREASON!", "true_label": 1, "processing_time": 2.1848974227905273, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5347448252141476, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.78); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7779586017131805, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.778 confidence", "processing_time": 0.06070685386657715}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it implies discrimination based on race.", "processing_time": 0.7883002758026123}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is considered offensive as it implies a discriminatory requirement based on race.", "processing_time": 2.0526282787323, "retrieved_examples": "Example 1: \"can i join ? im part white :)\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"can i join if i'm black but on the inside white ?\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"i'm brown and white can i still join or is there a rule ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.0571441650390625, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 63, "dynamic_weight_predictions": 63, "consensus_predictions": 0, "avg_coordination_time": 4.363241944994245, "system_type": "intelligent_coordination"}, "text": "hi do you have to be white to join", "true_label": 0, "processing_time": 2.0571441650390625, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5457826691369216, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.87); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8662613530953726, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.866 confidence", "processing_time": 0.0662088394165039}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text reports a decision by Charlottesville to deny a permit for an anniversary rally, without containing any offensive content.", "processing_time": 0.8753864765167236}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply reports the denial of a rally permit without promoting or inciting any harm or violence.", "processing_time": 2.1570589542388916, "retrieved_examples": "Example 1: \"p<PERSON><PERSON> told charlottesville how to hold a peaceful demonstration . the people's republic of charlottesville  however  ignored it\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"they didn't cuz mcauliffe told them to stand down charlottesville was planned man make us look bad\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"charlottesville : huge torchlight march & rally via\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.160205125808716, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 64, "dynamic_weight_predictions": 64, "consensus_predictions": 0, "avg_coordination_time": 4.328819494694471, "system_type": "intelligent_coordination"}, "text": "charlottesville denies unite the right anniversary rally permit", "true_label": 0, "processing_time": 2.160205125808716, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5570539012551308, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.96); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9564312100410461, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.956 confidence", "processing_time": 0.058478593826293945}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains accusations of racism and white supremacy, which are offensive and derogatory towards individuals and groups.", "processing_time": 0.8498067855834961}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive.", "processing_time": 2.53861927986145, "retrieved_examples": "Example 1: \"definitely a white supremacist .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"obviously a white supremacist\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"so  this is white supremacist .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.5407261848449707, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 65, "dynamic_weight_predictions": 65, "consensus_predictions": 0, "avg_coordination_time": 4.3013103668506325, "system_type": "intelligent_coordination"}, "text": "and he is definitely a racist  and probably white supremacist . at the very least he is supporting white supremacists .", "true_label": 0, "processing_time": 2.5412368774414062, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.552572812885046, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.92); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.920582503080368, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.921 confidence", "processing_time": 0.06635379791259766}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains derogatory content towards Islamic traders, implying laziness and lack of innovation.", "processing_time": 0.8134567737579346}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by referring to a group of people as 'too lazy'. It also implies a negative stereotype about a specific community.", "processing_time": 2.2466933727264404, "retrieved_examples": "Example 1: \"first save yourselves from barbarians you created 1400 years ago from islamic technology; you pigs have no idea\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"i believe they are too lazy. isn't that why we went and brought back slaves in the first place?\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"muslims always plagiarise  copy and twist other people's work just like m<PERSON><PERSON><PERSON> who copied then twisted the ot .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.2499313354492188, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 66, "dynamic_weight_predictions": 66, "consensus_predictions": 0, "avg_coordination_time": 4.270228866374854, "system_type": "intelligent_coordination"}, "text": "want to know something funny? islamic traders showed them the wheel, they were just too lazy to adopt i", "true_label": 1, "processing_time": 2.2499313354492188, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5448997616767883, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.86); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8591980934143066, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.859 confidence", "processing_time": 0.06732392311096191}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Expresses an opinion about the challenges faced by the white working class in America, without containing offensive speech", "processing_time": 0.84572434425354}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by suggesting that the white working class is the most oppressed group in America, which can be seen as dismissive of the experiences of other marginalized communities.", "processing_time": 2.151850461959839, "retrieved_examples": "Example 1: \"the most oppressed demographic in the #us is the white #workingclass\"\nVerdict: 1 (offensive speech)\n\nExample 2: \": the most oppressed demographic in the #us is the white #workingclass  tbh\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"the white #workingclass is the most oppressed demographic in the #unitedstates - #wakeup #fightback\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.158064126968384, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 67, "dynamic_weight_predictions": 67, "consensus_predictions": 0, "avg_coordination_time": 4.238704019518041, "system_type": "intelligent_coordination"}, "text": "you're right  and of all the groups in america that are being trampled upon  the white working class is getting it the worst", "true_label": 0, "processing_time": 2.158064126968384, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5490717813372612, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.89); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8925742506980896, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.893 confidence", "processing_time": 0.06354188919067383}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory content by implying that being white and Jewish is not enough to be considered American, with a sarcastic tone towards inclusivity.", "processing_time": 0.9492056369781494}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive. It emphasizes the inclusivity of being American regardless of race or religion.", "processing_time": 2.4454376697540283, "retrieved_examples": "Example 1: \"no .. brother your not racist .. your american .. #maga\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \". can a jew join ? i am white  green eyes and like your white trash blond south american girls .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"may i join u i am white myself hail hitler white power\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.452846050262451, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 68, "dynamic_weight_predictions": 68, "consensus_predictions": 0, "avg_coordination_time": 4.212441402323106, "system_type": "intelligent_coordination"}, "text": "i'm white and jewish  can i join your party ? oh that's right neither of those are qualifiers to be american . #maga brother", "true_label": 1, "processing_time": 2.452846050262451, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5552543886005878, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.94); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9420351088047028, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.942 confidence", "processing_time": 0.062062978744506836}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains an explicit accusation of racism towards an individual, which is offensive.", "processing_time": 0.783088207244873}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive. The exclamation marks do not change the context of the statement.", "processing_time": 4.877310037612915, "retrieved_examples": "Example 1: \"racist ! ! !\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"but he's a racist .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"is a true racist !\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 4.879970073699951, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 69, "dynamic_weight_predictions": 69, "consensus_predictions": 0, "avg_coordination_time": 4.222115730893785, "system_type": "intelligent_coordination"}, "text": "yes he is racist ! !", "true_label": 0, "processing_time": 4.879970073699951, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5593657270073891, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.97); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9749258160591125, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.975 confidence", "processing_time": 0.06497669219970703}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory term 'kikes' and promotes hateful stereotypes based on ethnicity", "processing_time": 0.7796614170074463}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains derogatory terms and promotes negative stereotypes.", "processing_time": 2.466876983642578, "retrieved_examples": "Example 1: \"jews are not white .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"none ! semites aren't white .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"jews are white\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.473255157470703, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 70, "dynamic_weight_predictions": 70, "consensus_predictions": 0, "avg_coordination_time": 4.197132008416313, "system_type": "intelligent_coordination"}, "text": "kikes are not white they are semites - arab blood", "true_label": 1, "processing_time": 2.473255157470703, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5545673904319603, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.94); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9365391234556834, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.937 confidence", "processing_time": 0.06377339363098145}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and negative stereotypes towards LGBT individuals", "processing_time": 0.7700095176696777}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it expresses distrust towards LGBT individuals and implies they should not be trusted with someone's daughter.", "processing_time": 2.090343713760376, "retrieved_examples": "Example 1: \"i may be powerless to stop trannies from sharing a locker room with my daughter, but gosh darn it i can get a lot of sons killed overseas!\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"gay or transgender, should not be in the scouts lgbt behavior not appropriate for scouts, not born that way\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"chicago showers locker room policy with criticism - i would never let my daughters be subject to this . :\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.0948901176452637, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 71, "dynamic_weight_predictions": 71, "consensus_predictions": 0, "avg_coordination_time": 4.1675229677012275, "system_type": "intelligent_coordination"}, "text": "yikes ! don't trust your daughter to a staffer either . #lgbt rt", "true_label": 1, "processing_time": 2.0948901176452637, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5535856783390045, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9286854267120361, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.929 confidence", "processing_time": 0.06581449508666992}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech contains a reference to white nationalism, which is associated with discrimination and hatred towards certain racial groups.", "processing_time": 0.8502707481384277}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is not offensive as it does not contain any explicit offensive language or intent.", "processing_time": 2.192559242248535, "retrieved_examples": "Example 1: \"that's right ! to white nationalist ...\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"says the white nationalist .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"are you a white nationalist ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.194891929626465, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 72, "dynamic_weight_predictions": 72, "consensus_predictions": 0, "avg_coordination_time": 4.140125314394634, "system_type": "intelligent_coordination"}, "text": "yes ... white nationalist", "true_label": 0, "processing_time": 2.194891929626465, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5607724338769913, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9861794710159302, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.986 confidence", "processing_time": 0.06113791465759277}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text expresses a concern about safety and the need for licensed weapons for protection, without containing any offensive language or derogatory content.", "processing_time": 0.9621341228485107}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech contains a reference to a gang surrounding someone's home, which can be perceived as a violent and threatening scenario.", "processing_time": 2.616671085357666, "retrieved_examples": "Example 1: \"even kids ? i know its normal to justify the police regardless . these people were unarmed and you still mentioned weapons\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"that's why the police have no go areas . that's why london is the aci\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"better sit in an office and police the fish in a barrel than monitor machete sales and truck rentals  eh ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.6203160285949707, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 73, "dynamic_weight_predictions": 73, "consensus_predictions": 0, "avg_coordination_time": 4.119306009109708, "system_type": "intelligent_coordination"}, "text": ": this is thee reason people want licensed weapons ! can you imagine a gang surrounding your home with no polic", "true_label": 0, "processing_time": 2.6203160285949707, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5545283332467079, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.94); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9362266659736633, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.936 confidence", "processing_time": 0.06059455871582031}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Expresses a political opinion, does not contain offensive speech", "processing_time": 0.803722620010376}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply reports a statement made by <PERSON> regarding not accepting an endorsement from <PERSON>.", "processing_time": 2.3411996364593506, "retrieved_examples": "Example 1: \"oh  cruz is cruzin ' for a trump bruisin ' .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"ted cruz to endorse donald trump via #trumptrain\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"donald trump just made it impossible for ted cruz to clinch the gop nomination\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.346147060394287, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 74, "dynamic_weight_predictions": 74, "consensus_predictions": 0, "avg_coordination_time": 4.095344401694635, "system_type": "intelligent_coordination"}, "text": "trump : i wouldn't accept cruz endorsement", "true_label": 0, "processing_time": 2.346147060394287, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5507269663115342, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9058157304922739, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.906 confidence", "processing_time": 0.06036496162414551}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and hateful speech towards non-white migrants and implies sexual objectification based on race", "processing_time": 0.829899787902832}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it dehumanizes and discriminates against non-white migrants based on their race.", "processing_time": 2.006213903427124, "retrieved_examples": "Example 1: \"that is sick and racist\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"we dont want. illegals to be sick and spread diseases!\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"white men who get with nonwhite women, are sexual failures.\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.0093629360198975, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 75, "dynamic_weight_predictions": 75, "consensus_predictions": 0, "avg_coordination_time": 4.067531315485638, "system_type": "intelligent_coordination"}, "text": "what kind of sick white person would be sexually attracted to the non-white migrant inv", "true_label": 1, "processing_time": 2.0098812580108643, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5527620613574982, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.92); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9220964908599854, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.922 confidence", "processing_time": 0.06186676025390625}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and accusation of being a white supremacist, which is offensive.", "processing_time": 0.9460225105285645}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it involves a discussion about suspecting someone of being a white supremacist and does not contain any explicit offensive language or hate speech.", "processing_time": 2.6023669242858887, "retrieved_examples": "Example 1: \"a white supremacist claiming\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"now if you have a problem with me suspecting someone of being a white supremacist because they are literally retweeting from known white supremacist then i may have to suspect that you are a white supremacist madam\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"and i explain why i called you a suspect a white supremacist you're literally retweeting and following people who promote theories that say whites are genetically superior than blacks on average via iq so you're definitely a suspect white supremacist\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.6085190773010254, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 76, "dynamic_weight_predictions": 76, "consensus_predictions": 0, "avg_coordination_time": 4.04833378603584, "system_type": "intelligent_coordination"}, "text": "so i deserved to get cursed for suspecting someone was a white supremacist who retweeted white supremacists ... says the christian", "true_label": 0, "processing_time": 2.6085190773010254, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.524307619780302, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.69); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.52)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.6944609582424164, "reasoning": "Hybrid classification using best_similarity with 0.694 confidence", "processing_time": 0.06090903282165527}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains a reference to the Holocaust and minimizes the tragedy by suggesting a sudden decrease in Jewish population figures.", "processing_time": 0.8148558139801025}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by referencing the Holocaust and the false claim of a sudden decrease in the Jewish population.", "processing_time": 2.601818799972534, "retrieved_examples": "Example 1: \"it's funny how jews are white except when a chance to exploit muh 6 gorillion arises and then they become a separate group .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"i wonder if this is related to the hordes of imported third worlders . that couldn't be  the jewish elites told us they'd contribute to a stronger  wealthier  more tolerant society and definitely not cause massive social and economic problems .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"more jew ...\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.6072981357574463, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 77, "dynamic_weight_predictions": 77, "consensus_predictions": 0, "avg_coordination_time": 4.029619037330926, "system_type": "intelligent_coordination"}, "text": "strange how no sudden 6million dip in jewish population figures .", "true_label": 1, "processing_time": 2.6072981357574463, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5547934882342815, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.94); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9383479058742523, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.938 confidence", "processing_time": 0.06468605995178223}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it simply discusses the association of advanced or intricate things with the color white.", "processing_time": 0.8285953998565674}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it does not contain any derogatory language or harmful intent.", "processing_time": 2.770019769668579, "retrieved_examples": "Example 1: \" white people killing white people  ha  \" perfect \" \" ! \"\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"  \" white people \" \" . . . the only people that count ? \"\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \" oh  and the group that kills children are  \" white \" \" too . \"\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.7759692668914795, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 78, "dynamic_weight_predictions": 78, "consensus_predictions": 0, "avg_coordination_time": 4.013546604376574, "system_type": "intelligent_coordination"}, "text": " rt people will object  \" but the famous  large kill count  intricate ones are white ! \" \" - well yes  anything advanced / intricate / noteworthy is . \"", "true_label": 0, "processing_time": 2.7759692668914795, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5608134493231773, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9865075945854187, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.987 confidence", "processing_time": 0.061473846435546875}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and derogatory language towards individuals based on race and gender.", "processing_time": 2.214500904083252}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and derogatory terms targeting individuals based on their race and gender.", "processing_time": 2.555884599685669, "retrieved_examples": "Example 1: \"u can smack my mom kid shes black\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"shut up  white trash\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"this nieger thinks u white trash sucks !\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.5600945949554443, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 79, "dynamic_weight_predictions": 79, "consensus_predictions": 0, "avg_coordination_time": 3.9951484776750408, "system_type": "intelligent_coordination"}, "text": "-when you #blacktrash sons-of-#crackwhore mommas want a dialog let me know? you are pathetic #urbanboybitch pukes!", "true_label": 1, "processing_time": 2.5600945949554443, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5564368478953838, "reasoning": "激进优化协调决策: deberta(原权重:0.42→优化权重:0.12, 预测:1, 置信度:0.95); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.30→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9514947831630707, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.951 confidence", "processing_time": 0.05705451965332031}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text contains statistical data and does not include any offensive speech.", "processing_time": 0.74942946434021}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is not offensive.", "processing_time": 2.0207297801971436, "retrieved_examples": "Example 1: \"new ma poll : trump 43 % rubio 12 % cruz 10 % kasi<PERSON> 9 % bush 8 % carson 6 %\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"breaking : trump doubles florida lead since last weektrump 44 % ( + 6 ) rubio 27 % ( - 3 ) cruz 17 % ( - - ) kasich 9 % ( - 1 )\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"trump most broadly popular republican in md with 57/37 fav . kasich at 48/35  cruz unpopular even with gop at 33/50 :\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.42499999999999993, "llm_single": 0.27499999999999997, "llm_retrieval": 0.3}, "coordination_time": 2.0245046615600586, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 80, "dynamic_weight_predictions": 80, "consensus_predictions": 0, "avg_coordination_time": 3.9705154299736036, "system_type": "intelligent_coordination"}, "text": "when you dump kasich / carson out of va ( monmouth ): trump 42 % ( + 1 ) rubio 32 % ( + 5 ) cruz 19 % ( + 5 ) minus rubio and 45 % cruz 27 %", "true_label": 0, "processing_time": 2.0245046615600586, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5511805353065331, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9094442824522654, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.909 confidence", "processing_time": 0.058762311935424805}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and derogatory remarks towards an individual based on their weight and talent, as well as racial discrimination.", "processing_time": 0.9083149433135986}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains insults based on weight, talent, and race.", "processing_time": 2.158904552459717, "retrieved_examples": "Example 1: \"shut up  white trash\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"you're white .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"im white and im not your people . you are nothing but delusional trash . you do not belong here or anywhere .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.163710594177246, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 81, "dynamic_weight_predictions": 81, "consensus_predictions": 0, "avg_coordination_time": 3.948209197432908, "system_type": "intelligent_coordination"}, "text": "shut your mouth . you're clinically obese and talentless . no white man worth his salt would look at you anyway .", "true_label": 1, "processing_time": 2.163710594177246, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.539107222110033, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.81); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8128577768802643, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.813 confidence", "processing_time": 0.06982684135437012}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language and dehumanizes a specific group of individuals", "processing_time": 1.0622141361236572}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it dehumanizes and incites mistreatment towards individuals labeled as 'illegals'. It promotes hostility and discrimination based on immigration status.", "processing_time": 2.5872409343719482, "retrieved_examples": "Example 1: \"illegals need to be removed from our country now! without delay!!\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"we either have laws or we don't . your here legally or not . it's black and white . illegals are illegal .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"= = = has shown he puts illegal aliens  refugees before us citzens .. he must be defeated .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.59279727935791, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 82, "dynamic_weight_predictions": 82, "consensus_predictions": 0, "avg_coordination_time": 3.931679783797847, "system_type": "intelligent_coordination"}, "text": "birthright citizenship must end or be defined to align with the intended purpose . illegals are enemies of our republic . treat them as such .", "true_label": 1, "processing_time": 2.59279727935791, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5605308786034584, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.98); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9842470288276672, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.984 confidence", "processing_time": 0.06374049186706543}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit threat of death towards an individual, making it offensive.", "processing_time": 1.4958038330078125}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it reports on the expulsion of an ex-Muslim critic of Islam from Norway, facing deportation to Pakistan and potential death. It does not contain any explicit offensive language or intent.", "processing_time": 3.176997184753418, "retrieved_examples": "Example 1: \"pakistan : foe of blasphemy law accused of hate speech for opposing blasphemy law\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"norway : threat of jihad :\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"pakistan : police arrest muslim cleric for inciting boy to cut off his own hand\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 3.1801717281341553, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 83, "dynamic_weight_predictions": 83, "consensus_predictions": 0, "avg_coordination_time": 3.922625469874188, "system_type": "intelligent_coordination"}, "text": ": ex-muslim critic of islam expelled from norway  faces deportion to pakistan and death", "true_label": 0, "processing_time": 3.1801717281341553, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5493314824998379, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.89); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.894651859998703, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.895 confidence", "processing_time": 0.07913327217102051}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it expresses frustration and doubt about the construction of a wall and supporting <PERSON>.", "processing_time": 0.9169714450836182}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive. It expresses doubt about the construction of the wall and frustration with supporting <PERSON>, but does not contain any offensive language or intent.", "processing_time": 2.5553054809570312, "retrieved_examples": "Example 1: \"i told alot of people to not support trump but they don't care only if he builds the wall\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"note : says funding ( not building ) the wall is good enough ( once a cuck ... ) want fight over obama care will surrender on wall .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"why can't wallace also support trump ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.562427043914795, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 84, "dynamic_weight_predictions": 84, "consensus_predictions": 0, "avg_coordination_time": 3.9064326314699094, "system_type": "intelligent_coordination"}, "text": "i am not even sure wall will get built . i am tired of cheerleading for trump we need to sepa", "true_label": 0, "processing_time": 2.562427043914795, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5429203175008297, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.84); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8433625400066376, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.843 confidence", "processing_time": 0.06351804733276367}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The statement does not contain offensive speech, it simply mentions the speaker's race without any derogatory content.", "processing_time": 0.8836817741394043}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply mentions the speaker's race without any derogatory or harmful language.", "processing_time": 1.9508459568023682, "retrieved_examples": "Example 1: \"i'm white\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"i don't identify as white\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"im white\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 1.956557035446167, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 85, "dynamic_weight_predictions": 85, "consensus_predictions": 0, "avg_coordination_time": 3.8834929185755125, "system_type": "intelligent_coordination"}, "text": "i don't want to be controversial  but i'm white  1/2", "true_label": 0, "processing_time": 1.956557035446167, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5289931446313858, "reasoning": "激进优化协调决策: deberta(原权重:0.42→优化权重:0.12, 预测:0, 置信度:0.73); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.30→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7319451570510864, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.732 confidence", "processing_time": 0.07132768630981445}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and insults towards <PERSON> and supporters, promoting negative stereotypes and spreading misinformation.", "processing_time": 0.8221101760864258}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains derogatory remarks and negative stereotypes about <PERSON> and implies that certain individuals are reliant on 'free stuff' from the government.", "processing_time": 2.439485788345337, "retrieved_examples": "Example 1: \"do you want to trump sycophant in the white house ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"i employ many people in the state of virginia - jobs  jobs  jobs ! crooked hill<PERSON> will sell us out  just like her husband did with nafta .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"trump not nominee-let hillary win and radicalize our people even more . trump is a lightweight compared to what could be coming .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.42499999999999993, "llm_single": 0.27499999999999997, "llm_retrieval": 0.3}, "coordination_time": 2.445028305053711, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 86, "dynamic_weight_predictions": 86, "consensus_predictions": 0, "avg_coordination_time": 3.8667665858601428, "system_type": "intelligent_coordination"}, "text": "yeah  giddy with their oh goody  ms hillary will make sure we keep getting our free stuff like <PERSON><PERSON><PERSON> did  and will get us more free stuff  government needs to pay us  owes us  so we dont have to work . what ? ? ? trump wants us to work ? ? ? ms hillary wouldnt ever do that to us !", "true_label": 0, "processing_time": 2.445028305053711, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5392393749207258, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.81); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8139149993658066, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.814 confidence", "processing_time": 0.06266021728515625}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain any explicit insults, threats, hatred, or derogatory content. It appears to be a statement referencing something written down.", "processing_time": 1.1791965961456299}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it does not contain any derogatory language or target a specific group.", "processing_time": 2.995089292526245, "retrieved_examples": "Example 1: \": there it is in black and white . are muslims blind that they cannot see this ?\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"i am quoting it right from the koran  it is in black and white ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"you obviously see everything in black and white .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 3.0000643730163574, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 87, "dynamic_weight_predictions": 87, "consensus_predictions": 0, "avg_coordination_time": 3.8568044914596396, "system_type": "intelligent_coordination"}, "text": "of course it's there in black and white - you can't deny it . #marref", "true_label": 0, "processing_time": 3.0000643730163574, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5440895780920982, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.85); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8527166247367859, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.853 confidence", "processing_time": 0.07888269424438477}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it is a call to join a group advocating for immigration reform and enforcement.", "processing_time": 0.8734421730041504}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it promotes a group advocating for immigration reform and enforcement.", "processing_time": 2.082058906555176, "retrieved_examples": "Example 1: \"join the #immigration conversation on twitter #afire\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"arizona is ground zero for illegal immigration battle #afire #tcot #teaparty #immigration\"\nVerdict: 1 (offensive speech)\n\nExample 3: \": az appeal of immigration ruling set for november #afire #tcot #teaparty illegal #immigr ...\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.086585760116577, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 88, "dynamic_weight_predictions": 88, "consensus_predictions": 0, "avg_coordination_time": 3.8366883695125593, "system_type": "intelligent_coordination"}, "text": " #afire stands for  \" americans for immigration reform and enforcement \" \" join us ! #tcot #topprog #tlot #sgp #hhrs \"", "true_label": 0, "processing_time": 2.086585760116577, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5459225624799728, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.87); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8673804998397827, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.867 confidence", "processing_time": 0.06375789642333984}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains a threat to take away media platforms like Facebook and Twitter to hinder a movement, which is considered offensive and threatening.", "processing_time": 1.208106279373169}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it discusses the potential impact of media platforms being taken away without promoting harm or discrimination.", "processing_time": 2.1784632205963135, "retrieved_examples": "Example 1: \"censorship will just cause these platforms to collapse of their own and new media to take their place\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"is choosing the path of legacy media and will decline as a result .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"social media has played a major role in the decline of the #trucons as well . no platforming is less and less effective\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.181150436401367, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 89, "dynamic_weight_predictions": 89, "consensus_predictions": 0, "avg_coordination_time": 3.818086819702321, "system_type": "intelligent_coordination"}, "text": "will take all media away . this includes facebook & twitter . there will be no way to continue movement .", "true_label": 0, "processing_time": 2.181150436401367, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5489318817853928, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.89); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8914550542831421, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.891 confidence", "processing_time": 0.060582637786865234}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement promotes discrimination based on race, color, and physical appearance, which is offensive and derogatory.", "processing_time": 0.9693639278411865}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it discriminates based on race and physical appearance.", "processing_time": 2.682840585708618, "retrieved_examples": "Example 1: \"jobs to americans first .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"americans first ! illegals have to go it's time for americans to get out of poverty !\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"be realistic the emigrants take a jobs than neither white or black americans like to do .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.687373638153076, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 90, "dynamic_weight_predictions": 90, "consensus_predictions": 0, "avg_coordination_time": 3.8055233399073294, "system_type": "intelligent_coordination"}, "text": "our people here in america who have no jobs . must come first red yellow black blue purple white skinny tall short fat americans", "true_label": 1, "processing_time": 2.687373638153076, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.528087355196476, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.72); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7246988415718079, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.725 confidence", "processing_time": 0.06431388854980469}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains offensive and derogatory content towards women, objectifying them based on their sexual behavior.", "processing_time": 0.854210376739502}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it objectifies and degrades women by reducing their value to sexual availability.", "processing_time": 2.415774345397949, "retrieved_examples": "Example 1: \"i would have no problem putting a bullet into these women's heads.\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"right, so lets outlaw it so women still cloths hooks up their snatch instead.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"so proud of my girls ! i've always argued feminist victimhood tactics are one of the good kinds of identity politics .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.4210972785949707, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 91, "dynamic_weight_predictions": 91, "consensus_predictions": 0, "avg_coordination_time": 3.790309866706095, "system_type": "intelligent_coordination"}, "text": "I'm all for women's rights and freedom and whatever, as long as they still put out.", "true_label": 1, "processing_time": 2.4210972785949707, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5500712655484676, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9005701243877411, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.901 confidence", "processing_time": 0.05896496772766113}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains derogatory language towards Democrats and Muslims, implying negative intentions and promoting hatred.", "processing_time": 0.8596024513244629}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech contains offensive language towards Muslims by referring to them in a derogatory manner and spreading misinformation about them changing books.", "processing_time": 2.104079246520996, "retrieved_examples": "Example 1: \"except now democrats demonize people that happen to be white .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"democrats in white hoods have hung white republicans from nooses before  you don't scare us .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"once again a democrat-socialist rushing to defend their deranged  mass murdering  anti-semitic  islamic supremacist friends .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.1072349548339844, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 92, "dynamic_weight_predictions": 92, "consensus_predictions": 0, "avg_coordination_time": 3.7720155741857457, "system_type": "intelligent_coordination"}, "text": "the democrats let the moslems change all our books about them nice hug", "true_label": 1, "processing_time": 2.1072349548339844, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5433628037571907, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.85); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8469024300575256, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.847 confidence", "processing_time": 0.0646059513092041}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insults and suggests seeking professional help in a derogatory manner.", "processing_time": 0.8132724761962891}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by demeaning and belittling the individual's mental health struggles.", "processing_time": 2.5246732234954834, "retrieved_examples": "Example 1: \"you're a sick and evil creature . you deserve a life of pointless atomized consumerism .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"you are a sick, poor excuse for a human being. the sludge on the bottom of a garbage can. shameful.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Why can't you act like a normal person? You don't have Asperger's. There's nothing wrong with you. You're just looking for attention/excuses. If you'd just stop behaving so oddly, you wouldn't need to blame it on a disease.\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.529546022415161, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 93, "dynamic_weight_predictions": 93, "consensus_predictions": 0, "avg_coordination_time": 3.7586556865322986, "system_type": "intelligent_coordination"}, "text": "i'm not interested in helping you work your sickness out, you need a shrink and a pile of meds, still probably wouldn't help you. get help", "true_label": 1, "processing_time": 2.529546022415161, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5535104498267174, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.93); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.928083598613739, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.928 confidence", "processing_time": 0.06386232376098633}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit insult and discrimination based on race, suggesting relocation based on skin color", "processing_time": 0.7958498001098633}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains derogatory language towards a racial group and suggests discriminatory actions based on race.", "processing_time": 3.606187582015991, "retrieved_examples": "Example 1: \"are you not happy you whites do not need welfare . change places .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"this country needs more white immigrants .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"let white sas immigrate to us .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 3.6131443977355957, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 94, "dynamic_weight_predictions": 94, "consensus_predictions": 0, "avg_coordination_time": 3.7571076940982913, "system_type": "intelligent_coordination"}, "text": "i'm white stupid . tell you what  we will give you an extra welfare check and you move to a white country .", "true_label": 1, "processing_time": 3.6131443977355957, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5529849690695603, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.92); llm_single(原权重:0.23→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9238797525564829, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.924 confidence", "processing_time": 0.0666055679321289}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains references to a controversial figure and a group associated with hate speech, indicating support for the alt-right movement.", "processing_time": 1.2211930751800537}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it refers to a controversial figure triggering a meltdown, which implies a negative impact on the situation.", "processing_time": 2.5870494842529297, "retrieved_examples": "Example 1: \"milo can be useful to an extent . his gayness makes lefties more receptive to him . i started with milo and ended at the alt-right\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"of at berkeley free speech rally .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"why the schadenfreude ? why not be neutral since milo helped the altright with his positive piece on the movement in breitbart\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.593867778778076, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 95, "dynamic_weight_predictions": 95, "consensus_predictions": 0, "avg_coordination_time": 3.74486306341071, "system_type": "intelligent_coordination"}, "text": "the meltdown milo triggered at berkeley was very positive for the altright as well .", "true_label": 0, "processing_time": 2.593867778778076, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5381378568708897, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.81); llm_single(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8051028549671173, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.805 confidence", "processing_time": 0.06025958061218262}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text describes a motorcycle rally by Golden Dawn supporters in Athens, without containing any explicit insults, threats, or derogatory content.", "processing_time": 0.8847479820251465}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it describes a motorcycle rally held by Golden Dawn supporters in Athens.", "processing_time": 2.317317008972168, "retrieved_examples": "Example 1: \"thousands of golden dawn supporters on the streets of athens over arrest of partys leader via\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"thousands of golden dawn supporters on the streets of athens over arrest of partys leader va\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"  \" : greece : two retired generals announce theyre running for mep on golden dawn ticket via \" \" \"\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.3232791423797607, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 96, "dynamic_weight_predictions": 96, "consensus_predictions": 0, "avg_coordination_time": 3.7300548975666374, "system_type": "intelligent_coordination"}, "text": ": greece : golden dawn supporters holds massive motorcycle rally in athens )", "true_label": 0, "processing_time": 2.3232791423797607, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5234429314732552, "reasoning": "激进优化协调决策: deberta(原权重:0.27→优化权重:0.12, 预测:0, 置信度:0.69); llm_single(原权重:0.27→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.45→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.52)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.6875434517860413, "reasoning": "Hybrid classification using best_multi_label with 0.688 confidence", "processing_time": 0.06803083419799805}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains a derogatory reference to HIV tests in Africa, which is offensive and insensitive.", "processing_time": 0.861154317855835}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by making a derogatory joke about HIV tests in Africa.", "processing_time": 2.070664167404175, "retrieved_examples": "Example 1: \"Finally something positive I found of the Ethiopians HIV\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Why don't Africans need help? They already have aids.\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"If only Africa had more mosquito nets If only Africa had more mosquito nets then every year we could save millions of mosquitoes from dying needlessly from AIDs.\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.27499999999999997, "llm_single": 0.27499999999999997, "llm_retrieval": 0.45}, "coordination_time": 2.0758705139160156, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 97, "dynamic_weight_predictions": 97, "consensus_predictions": 0, "avg_coordination_time": 3.7130014503125075, "system_type": "intelligent_coordination"}, "text": "Name one positive about Africa. The HIV Tests.", "true_label": 1, "processing_time": 2.0758705139160156, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5301390364766121, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:1, 置信度:0.74); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.53)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.7411122918128967, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.741 confidence", "processing_time": 0.06411910057067871}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it reflects on a past time when attending concerts felt safe and enjoyable.", "processing_time": 0.8649609088897705}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is non-offensive as it reflects on a time when people attended concerts without fear of violence.", "processing_time": 2.451458215713501, "retrieved_examples": "Example 1: \"white supremacists dont shoot up white people at country concerts .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"aye  well  i like the nineties . people were happy and confident and the diversity hadn't started to bite .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"people used to pay to see freaks like this .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.4573731422424316, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 98, "dynamic_weight_predictions": 98, "consensus_predictions": 0, "avg_coordination_time": 3.7001889165566904, "system_type": "intelligent_coordination"}, "text": "the era when people went to concerts and never dreamed of being bombed or mowed down .", "true_label": 0, "processing_time": 2.4573731422424316, "dataset": "ImplicitHate"}, {"verdict": 1, "confidence": 0.5500215652088325, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9001725216706594, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.900 confidence", "processing_time": 0.0626375675201416}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The statement does not contain explicit insults, threats, or hatred. It appears to be a conditional statement expressing concern, not offensive speech.", "processing_time": 0.9243462085723877}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it implies harm or negative consequences towards white people.", "processing_time": 2.126725912094116, "retrieved_examples": "Example 1: \"the sign says : we're better will less white people than with more white people .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"whites  a necessary but not a sufficient condition .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"so we should kill anyone whos not white ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.132429361343384, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 99, "dynamic_weight_predictions": 99, "consensus_predictions": 0, "avg_coordination_time": 3.6843529614535258, "system_type": "intelligent_coordination"}, "text": "the comment was more of a conditional : white people must not be fractured or we'll may", "true_label": 1, "processing_time": 2.132429361343384, "dataset": "ImplicitHate"}, {"verdict": 0, "confidence": 0.5517280623316765, "reasoning": "激进优化协调决策: deberta(原权重:0.38→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.23→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9138244986534119, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.914 confidence", "processing_time": 0.06647610664367676}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain explicit insults, threats, hatred, or derogatory content. It expresses an opinion about media coverage.", "processing_time": 0.8740079402923584}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it discusses a statistical fact without promoting hatred or discrimination.", "processing_time": 2.4260096549987793, "retrieved_examples": "Example 1: \"white people winning is racist . haven't you been listening ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"<PERSON><PERSON><PERSON> only won 18 % of the white vote\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"the gop seems to be forgetting another group they should consider : white people .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.375, "llm_single": 0.225, "llm_retrieval": 0.4}, "coordination_time": 2.430908441543579, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 100, "dynamic_weight_predictions": 100, "consensus_predictions": 0, "avg_coordination_time": 3.6718185162544263, "system_type": "intelligent_coordination"}, "text": "white people won * * * only thirty percent * of the delegates in the iowa gop race . bet the media hasnt mentioned that  huh ?", "true_label": 0, "processing_time": 2.430908441543579, "dataset": "ImplicitHate"}], "system_config": {"enable_dynamic_weights": true, "enable_performance_tracking": true, "enable_online_learning": true, "coordination_method": "dynamic_weighted", "learning_rate": 0.01, "confidence_threshold": 0.6}}