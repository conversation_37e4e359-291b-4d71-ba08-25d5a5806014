#!/usr/bin/env python3
"""
增强的DeBERTa智能体实现
专注于零样本分类和特征提取，与LLM智能体深度集成
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import (
    AutoTokenizer, AutoModelForSequenceClassification,
    AutoModel, AutoConfig, pipeline
)
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import json
import logging
from pathlib import Path
from dataclasses import dataclass
from sklearn.metrics.pairwise import cosine_similarity

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DeBERTaConfig:
    """DeBERTa智能体配置"""
    model_name: str = "D:/models/DeBERTa-v3-base-mnli-fever-anli"  # 使用本地分词器
    zero_shot_model: str = "D:/models/DeBERTa-v3-base-mnli-fever-anli"
    max_length: int = 512
    confidence_threshold: float = 0.7
    similarity_threshold: float = 0.8
    cache_dir: str = "./models/deberta_cache"
    offensive_labels: List[str] = None

    def __post_init__(self):
        if self.offensive_labels is None:
            self.offensive_labels = [
                "hate speech", "offensive language", "toxic content",
                "harassment", "discrimination", "abusive language"
            ]

class EnhancedDeBERTaAgent:
    """
    增强的DeBERTa智能体
    专注于零样本分类和特征提取，支持多种推理策略
    """

    def __init__(self, config: DeBERTaConfig = None):
        self.config = config or DeBERTaConfig()
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 初始化模型和分词器
        self.tokenizer = None
        self.zero_shot_model = None
        self.feature_model = None
        self.zero_shot_classifier = None

        # 性能跟踪和缓存
        self.performance_history = {}
        self.feature_cache = {}
        self.prediction_cache = {}

        self._initialize_models()
        
    def _initialize_models(self):
        """初始化模型"""
        try:
            logger.info(f"正在加载本地DeBERTa模型: {self.config.model_name}")

            # 加载本地分词器
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.model_name,
                cache_dir=self.config.cache_dir,
                local_files_only=True  # 强制使用本地文件
            )

            # 零样本分类模型 (MNLI) - 使用本地模型
            self.zero_shot_model = AutoModelForSequenceClassification.from_pretrained(
                self.config.zero_shot_model,
                cache_dir=self.config.cache_dir,
                local_files_only=True  # 强制使用本地文件
            ).to(self.device)

            # 特征提取模型 - 使用本地零样本模型进行特征提取
            self.feature_model = AutoModel.from_pretrained(
                self.config.zero_shot_model,
                cache_dir=self.config.cache_dir,
                local_files_only=True  # 强制使用本地文件
            ).to(self.device)

            # 零样本分类Pipeline
            self.zero_shot_classifier = pipeline(
                "zero-shot-classification",
                model=self.config.zero_shot_model,
                device=0 if torch.cuda.is_available() else -1
            )

            logger.info("DeBERTa模型加载完成")

        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
            
    def zero_shot_classify(self, text: str, labels: List[str] = None) -> Dict:
        """
        零样本分类 - 使用Pipeline确保准确性
        """
        if not self.zero_shot_classifier:
            raise ValueError("零样本分类器未初始化")

        if labels is None:
            labels = ["offensive speech", "normal speech"]

        try:
            # 使用Pipeline进行零样本分类
            result = self.zero_shot_classifier(text, labels)

            # 解析结果
            top_label = result['labels'][0]
            top_score = result['scores'][0]

            # 转换为二分类结果
            verdict = 1 if top_label == "offensive speech" else 0
            confidence = top_score

            # 构建标签分数字典
            label_scores = {label: score for label, score in zip(result['labels'], result['scores'])}

            return {
                "verdict": verdict,
                "confidence": confidence,
                "method": "zero_shot_pipeline",
                "label_scores": label_scores,
                "explanation": f"Zero-shot classification with {confidence:.3f} confidence for '{top_label}'"
            }

        except Exception as e:
            logger.error(f"零样本分类失败: {e}")
            return {
                "verdict": 0,
                "confidence": 0.5,
                "method": "zero_shot_pipeline",
                "error": str(e)
            }
            
    def extract_features(self, text: str) -> torch.Tensor:
        """
        提取文本的语义特征向量
        """
        try:
            # 检查缓存
            if text in self.feature_cache:
                return self.feature_cache[text]

            # 编码输入
            inputs = self.tokenizer(
                text,
                truncation=True,
                padding=True,
                max_length=self.config.max_length,
                return_tensors="pt"
            ).to(self.device)

            # 提取特征
            with torch.no_grad():
                outputs = self.feature_model(**inputs)
                # 使用[CLS]标记的表示作为文本特征
                features = outputs.last_hidden_state[:, 0, :].squeeze(0)  # [768]

            # 缓存特征
            self.feature_cache[text] = features

            return features

        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            # 返回零向量作为后备
            return torch.zeros(768).to(self.device)

    def semantic_similarity_classify(self, text: str, reference_texts: List[Dict] = None) -> Dict:
        """
        基于语义相似度的分类
        使用预定义的攻击性和非攻击性文本作为参考
        """
        try:
            if reference_texts is None:
                # 默认参考文本
                reference_texts = [
                    {"text": "I hate you and wish you were dead", "label": 1},
                    {"text": "You are stupid and worthless", "label": 1},
                    {"text": "This is a beautiful day", "label": 0},
                    {"text": "Thank you for your help", "label": 0}
                ]

            # 提取输入文本特征
            input_features = self.extract_features(text)

            # 计算与参考文本的相似度
            similarities = []
            for ref in reference_texts:
                ref_features = self.extract_features(ref["text"])
                similarity = F.cosine_similarity(
                    input_features.unsqueeze(0),
                    ref_features.unsqueeze(0)
                ).item()
                similarities.append((similarity, ref["label"]))

            # 基于相似度进行分类
            offensive_similarities = [sim for sim, label in similarities if label == 1]
            normal_similarities = [sim for sim, label in similarities if label == 0]

            avg_offensive_sim = np.mean(offensive_similarities) if offensive_similarities else 0
            avg_normal_sim = np.mean(normal_similarities) if normal_similarities else 0

            # 决策
            if avg_offensive_sim > avg_normal_sim and avg_offensive_sim > self.config.similarity_threshold:
                verdict = 1
                confidence = avg_offensive_sim
            else:
                verdict = 0
                confidence = avg_normal_sim

            return {
                "verdict": verdict,
                "confidence": confidence,
                "method": "semantic_similarity",
                "offensive_similarity": avg_offensive_sim,
                "normal_similarity": avg_normal_sim,
                "explanation": f"Semantic similarity classification with {confidence:.3f} confidence"
            }

        except Exception as e:
            logger.error(f"语义相似度分类失败: {e}")
            return {
                "verdict": 0,
                "confidence": 0.5,
                "method": "semantic_similarity",
                "error": str(e)
            }
            
    def multi_label_zero_shot_classify(self, text: str) -> Dict:
        """
        多标签零样本分类，使用更细粒度的攻击性标签
        """
        try:
            labels = self.config.offensive_labels + ["normal speech"]

            # 构建假设
            hypotheses = [f"This text contains {label}" for label in labels]

            # 编码输入
            inputs = self.tokenizer(
                [text] * len(hypotheses),
                hypotheses,
                truncation=True,
                padding=True,
                max_length=self.config.max_length,
                return_tensors="pt"
            ).to(self.device)

            # 推理
            with torch.no_grad():
                outputs = self.zero_shot_model(**inputs)
                logits = outputs.logits

            # 计算概率
            probs = F.softmax(logits, dim=-1)
            entailment_probs = probs[:, 2]  # MNLI中的entailment标签

            # 分析结果
            label_scores = {label: prob.item() for label, prob in zip(labels, entailment_probs)}

            # 计算攻击性概率
            offensive_scores = [score for label, score in label_scores.items() if label != "normal speech"]
            normal_score = label_scores["normal speech"]
            max_offensive_score = max(offensive_scores) if offensive_scores else 0

            # 决策
            if max_offensive_score > normal_score and max_offensive_score > self.config.confidence_threshold:
                verdict = 1
                confidence = max_offensive_score
                best_label = max([(label, score) for label, score in label_scores.items() if label != "normal speech"], key=lambda x: x[1])[0]
            else:
                verdict = 0
                confidence = normal_score
                best_label = "normal speech"

            return {
                "verdict": verdict,
                "confidence": confidence,
                "method": "multi_label_zero_shot",
                "label_scores": label_scores,
                "best_label": best_label,
                "explanation": f"Multi-label zero-shot classification: {best_label} with {confidence:.3f} confidence"
            }

        except Exception as e:
            logger.error(f"多标签零样本分类失败: {e}")
            return {
                "verdict": 0,
                "confidence": 0.5,
                "method": "multi_label_zero_shot",
                "error": str(e)
            }

    def hybrid_classify(self, text: str, reference_texts: List[Dict] = None) -> Dict:
        """
        混合分类：结合零样本分类和语义相似度
        """
        # 获取多种方法的结果
        zero_shot_result = self.zero_shot_classify(text)
        multi_label_result = self.multi_label_zero_shot_classify(text)
        similarity_result = self.semantic_similarity_classify(text, reference_texts)

        # 收集所有方法的投票
        methods = [
            ("zero_shot", zero_shot_result),
            ("multi_label", multi_label_result),
            ("similarity", similarity_result)
        ]

        # 基于置信度加权投票
        total_weight = 0
        weighted_score = 0
        high_confidence_methods = []

        for method_name, result in methods:
            if result["confidence"] > self.config.confidence_threshold:
                high_confidence_methods.append((method_name, result))
                weight = result["confidence"]
                total_weight += weight
                weighted_score += result["verdict"] * weight

        # 决策逻辑
        if high_confidence_methods:
            # 有高置信度方法时，使用加权投票
            final_score = weighted_score / total_weight if total_weight > 0 else 0
            final_verdict = 1 if final_score > 0.5 else 0
            final_confidence = min(total_weight / len(high_confidence_methods), 1.0)
            method = f"weighted_consensus_{len(high_confidence_methods)}_methods"
        else:
            # 没有高置信度方法时，选择置信度最高的
            best_method = max(methods, key=lambda x: x[1]["confidence"])
            final_verdict = best_method[1]["verdict"]
            final_confidence = best_method[1]["confidence"]
            method = f"best_{best_method[0]}"

        return {
            "verdict": final_verdict,
            "confidence": final_confidence,
            "method": method,
            "zero_shot_result": zero_shot_result,
            "multi_label_result": multi_label_result,
            "similarity_result": similarity_result,
            "weighted_score": weighted_score / total_weight if total_weight > 0 else 0,
            "high_confidence_methods": [m[0] for m in high_confidence_methods],
            "explanation": f"Hybrid classification using {method} with {final_confidence:.3f} confidence"
        }
        
    def detect(self, text: str, method: str = "hybrid", reference_texts: List[Dict] = None) -> Dict:
        """
        主要检测接口

        Args:
            text: 输入文本
            method: 检测方法 ("hybrid", "zero_shot", "multi_label", "similarity")
            reference_texts: 语义相似度参考文本
        """
        # 检查缓存
        cache_key = f"{method}_{hash(text)}"
        if cache_key in self.prediction_cache:
            return self.prediction_cache[cache_key]

        # 执行检测
        if method == "hybrid":
            result = self.hybrid_classify(text, reference_texts)
        elif method == "zero_shot":
            result = self.zero_shot_classify(text)
        elif method == "multi_label":
            result = self.multi_label_zero_shot_classify(text)
        elif method == "similarity":
            result = self.semantic_similarity_classify(text, reference_texts)
        else:
            raise ValueError(f"未知的检测方法: {method}")

        # 缓存结果
        self.prediction_cache[cache_key] = result

        return result
            
    def build_reference_database(self, training_data: List[Dict]) -> None:
        """
        构建参考文本数据库，用于语义相似度分类

        Args:
            training_data: 训练数据，格式为 [{"text": "...", "label": 0/1}, ...]
        """
        logger.info(f"构建参考文本数据库，样本数: {len(training_data)}")

        # 分类样本
        offensive_samples = [item for item in training_data if item["label"] == 1]
        normal_samples = [item for item in training_data if item["label"] == 0]

        # 选择代表性样本（基于特征多样性）
        def select_representative_samples(samples: List[Dict], max_samples: int = 10) -> List[Dict]:
            if len(samples) <= max_samples:
                return samples

            # 提取所有样本的特征
            features = []
            for sample in samples:
                feature = self.extract_features(sample["text"])
                features.append(feature.cpu().numpy())

            features = np.array(features)

            # 使用K-means聚类选择代表性样本
            from sklearn.cluster import KMeans

            n_clusters = min(max_samples, len(samples))
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(features)

            # 从每个聚类中选择最接近中心的样本
            representative_samples = []
            for i in range(n_clusters):
                cluster_indices = np.where(cluster_labels == i)[0]
                cluster_center = kmeans.cluster_centers_[i]

                # 找到最接近聚类中心的样本
                distances = [np.linalg.norm(features[idx] - cluster_center) for idx in cluster_indices]
                best_idx = cluster_indices[np.argmin(distances)]
                representative_samples.append(samples[best_idx])

            return representative_samples

        # 选择代表性样本
        self.reference_offensive = select_representative_samples(offensive_samples, 10)
        self.reference_normal = select_representative_samples(normal_samples, 10)

        logger.info(f"选择了 {len(self.reference_offensive)} 个攻击性参考样本")
        logger.info(f"选择了 {len(self.reference_normal)} 个正常参考样本")

    def clear_cache(self):
        """清理缓存"""
        self.feature_cache.clear()
        self.prediction_cache.clear()
        logger.info("缓存已清理")

    def get_cache_stats(self) -> Dict:
        """获取缓存统计"""
        return {
            "feature_cache_size": len(self.feature_cache),
            "prediction_cache_size": len(self.prediction_cache)
        }
        
    def update_performance_history(self, dataset_name: str, metrics: Dict):
        """更新性能历史记录"""
        if dataset_name not in self.performance_history:
            self.performance_history[dataset_name] = []
        self.performance_history[dataset_name].append(metrics)
        
    def get_performance_stats(self, dataset_name: str = None) -> Dict:
        """获取性能统计"""
        if dataset_name:
            return self.performance_history.get(dataset_name, [])
        return self.performance_history
        
    def save_config(self, path: str):
        """保存配置"""
        config_dict = {
            "model_name": self.config.model_name,
            "zero_shot_model": self.config.zero_shot_model,
            "max_length": self.config.max_length,
            "confidence_threshold": self.config.confidence_threshold,
            "similarity_threshold": self.config.similarity_threshold,
            "performance_history": self.performance_history
        }

        with open(path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)

    def load_config(self, path: str):
        """加载配置"""
        with open(path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)

        self.config.confidence_threshold = config_dict.get("confidence_threshold", 0.7)
        self.config.similarity_threshold = config_dict.get("similarity_threshold", 0.8)
        self.performance_history = config_dict.get("performance_history", {})

# 使用示例
if __name__ == "__main__":
    # 创建DeBERTa智能体
    config = DeBERTaConfig(
        model_name="D:/models/DeBERTa-v3-base-mnli-fever-anli",
        confidence_threshold=0.7
    )

    agent = EnhancedDeBERTaAgent(config)

    # 测试文本
    test_texts = [
        "I hate all people from that country",
        "This is a beautiful day",
        "You are so stupid and worthless",
        "Let's work together for peace"
    ]

    print("增强DeBERTa智能体测试结果:")
    print("=" * 60)

    for text in test_texts:
        print(f"\n文本: {text}")

        # 测试不同方法
        methods = ["zero_shot", "multi_label", "similarity", "hybrid"]

        for method in methods:
            try:
                result = agent.detect(text, method=method)
                verdict = "攻击性" if result["verdict"] == 1 else "正常"
                print(f"{method:12}: {verdict} (置信度: {result['confidence']:.3f})")
            except Exception as e:
                print(f"{method:12}: 错误 - {e}")

        print("-" * 60)

    # 缓存统计
    print(f"\n缓存统计: {agent.get_cache_stats()}")
