import argparse
import os
import json
import subprocess
import pandas as pd
import matplotlib
matplotlib.use('Agg') # 必须在导入pyplot之前设置
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

def run_evaluation_script(script_name, args, local_model_path, additional_args=None):
    """通用函数，用于运行评估脚本并返回日志文件路径"""
    command = [
        "python", "-u", script_name,
        "--dataset", args.dataset,
        "--num_samples", str(args.num_samples),
        "--start_idx", str(args.start_idx),
        "--provider", args.provider,
        "--model", args.model
    ]
    if args.provider == 'local':
        command.extend(["--local-model-path", local_model_path])

    # 添加额外参数（用于多智能体系统配置）
    if additional_args:
        command.extend(additional_args)
    
    print(f"\n--- Running: {' '.join(command)} ---\n")
    
    env = os.environ.copy()
    env["PYTHONIOENCODING"] = "UTF-8"

    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        encoding='utf-8',
        env=env,
        bufsize=1,
        universal_newlines=True
    )

    log_file = None
    
    if process.stdout:
        for line in iter(process.stdout.readline, ''):
            try:
                print(line, end='')
            except UnicodeEncodeError:
                # 处理编码问题，使用ASCII编码
                print(line.encode('ascii', 'ignore').decode('ascii'), end='')
            if "Results saved to:" in line:
                # 修复路径提取逻辑，处理包含冒号的路径
                parts = line.split("Results saved to:")
                if len(parts) > 1:
                    log_file = parts[1].strip()

    process.wait()

    if process.returncode != 0:
        print(f"\nError: Script {script_name} exited with return code {process.returncode}")
        return None
            
    return log_file

def plot_comparison(single_agent_metrics, multi_agent_metrics, dataset_name, run_id):
    """绘制单智能体和多智能体的性能对比图"""
    metrics_to_plot = ["accuracy", "precision", "recall", "f1"]
    
    single_scores = [single_agent_metrics.get(m, 0) for m in metrics_to_plot]
    multi_scores = [multi_agent_metrics.get(m, 0) for m in metrics_to_plot]

    df = pd.DataFrame({
        "Metrics": metrics_to_plot,
        "singleagent": single_scores,
        "multiagent": multi_scores
    })

    # 绘制条形图
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, ax = plt.subplots(figsize=(12, 7))

    ax = df.set_index('Metrics').plot(kind='bar', ax=ax, width=0.6)

    ax.set_title(f"Metrics Comparison: singleagent vs multiagent on {dataset_name}", fontsize=16)
    ax.set_ylabel("Scores", fontsize=12)
    ax.set_xlabel("Metrics", fontsize=12)
    plt.xticks(rotation=0)
    ax.legend(title="Agent Type")

    # 在每个柱子上方显示具体数值
    for p in ax.patches:
        ax.annotate(f"{p.get_height():.4f}", 
                    (p.get_x() + p.get_width() / 2., p.get_height()), 
                    ha='center', va='center', 
                    xytext=(0, 9), 
                    textcoords='offset points')
    
    # 在结果目录中保存图像
    results_dir = "results"
    os.makedirs(results_dir, exist_ok=True)
    
    # 使用 run_id 来命名
    image_path = os.path.join(results_dir, f"{dataset_name}_comparison_{run_id}.png")
    plt.savefig(image_path)
    print(f"\nComparison plot saved to: {image_path}")
    plt.close()

def main():
    """Run comparison evaluation between single agent and multi-agent approaches"""
    parser = argparse.ArgumentParser(description="Run comparison between single agent and multi-agent detection")
    parser.add_argument("--dataset", type=str, default="DynamicallyHate",
                        choices=["CHSD", "COLDataset", "ToxiCN", "ImplicitHate",
                                "HateSpeechOffensive", "HateSpeechStormfront", "DynamicallyHate"],
                        help="Name of the dataset to evaluate")
    parser.add_argument("--num_samples", type=int, default=10, help="Number of samples to evaluate")
    parser.add_argument("--start_idx", type=int, default=0, help="Starting index of samples")
    parser.add_argument("--model", type=str, default="gpt-3.5-turbo-0125",
                        help="Name of the model to use (only required for api/ollama providers)")
    parser.add_argument("--provider", type=str, choices=["api", "ollama", "local"], default="api",
                        help="Model provider")
    parser.add_argument("--ollama-base-url", type=str, default="http://localhost:11434", help="Ollama API base URL")
    parser.add_argument("--local-model-path", type=str,
                        help="Path to local model (required when provider is 'local')")

    # 多智能体系统配置参数 (默认使用智能协调系统)
    parser.add_argument("--disable-dynamic-weights", action="store_true", default=False,
                        help="Disable dynamic weight allocation in multi-agent system")
    parser.add_argument("--disable-performance-tracking", action="store_true", default=False,
                        help="Disable performance tracking in multi-agent system")
    parser.add_argument("--disable-online-learning", action="store_true", default=False,
                        help="Disable online learning in multi-agent system")
    parser.add_argument("--learning-rate", type=float, default=0.01,
                        help="Learning rate for dynamic weight optimization")
    parser.add_argument("--coordination-method", type=str, default="dynamic_weighted",
                        choices=["static_weighted", "dynamic_weighted", "consensus_based"],
                        help="Coordination method for multi-agent system")

    args = parser.parse_args()

    # 处理智能协调系统的默认启用逻辑
    args.enable_dynamic_weights = not args.disable_dynamic_weights
    args.enable_performance_tracking = not args.disable_performance_tracking
    args.enable_online_learning = not args.disable_online_learning

    if args.provider == 'local':
        if not args.local_model_path:
            parser.error("--local-model-path is required when using a local provider.")
        # 当使用本地模型时，自动从路径解析模型名称
        args.model = os.path.basename(args.local_model_path.rstrip('/\\'))
        print(f"Using local model: {args.local_model_path}")
        print(f"Model name: {args.model}")
    
    # 1. Run single-agent evaluation
    single_agent_log_file = run_evaluation_script("run_single_agent_evaluation.py", args, args.local_model_path)
    if not single_agent_log_file:
        print("Single-agent evaluation failed. Aborting comparison.")
        return
        
    # 2. Run multi-agent evaluation with intelligent coordination system (默认配置)
    multi_agent_additional_args = [
        "--coordination-method", args.coordination_method,
        "--learning-rate", str(args.learning_rate)
    ]

    if args.enable_dynamic_weights:
        multi_agent_additional_args.append("--enable-dynamic-weights")

    if args.enable_performance_tracking:
        multi_agent_additional_args.append("--enable-performance-tracking")

    if args.enable_online_learning:
        multi_agent_additional_args.append("--enable-online-learning")

    print(f"Running multi-agent evaluation with intelligent coordination system")
    print(f"Configuration:")
    print(f"   - Dynamic weights: {args.enable_dynamic_weights}")
    print(f"   - Performance tracking: {args.enable_performance_tracking}")
    print(f"   - Online learning: {args.enable_online_learning}")
    print(f"   - Learning rate: {args.learning_rate}")
    print(f"   - Coordination method: {args.coordination_method}")
    print(f"Additional args: {multi_agent_additional_args}")

    multi_agent_log_file = run_evaluation_script(
        "run_multi_agent_evaluation.py",
        args,
        args.local_model_path,
        additional_args=multi_agent_additional_args
    )
    if not multi_agent_log_file:
        print("Multi-agent evaluation failed. Aborting comparison.")
        return

    # 3. Load metrics from log files
    with open(single_agent_log_file, 'r', encoding='utf-8') as f:
        single_agent_log = json.load(f)
    
    with open(multi_agent_log_file, 'r', encoding='utf-8') as f:
        multi_agent_log = json.load(f)
        
    # 从日志中获取run_id，用于命名对比图
    # 处理新的清理后格式和旧格式
    if "metadata" in single_agent_log:
        # 新的清理后格式
        run_id = single_agent_log["metadata"].get("run_id")
    else:
        # 旧格式
        run_id = single_agent_log.get("run_id")

    if not run_id:
        # 如果日志文件中没有run_id，则创建一个基于时间戳的备用ID
        run_id = datetime.now().strftime('%Y%m%d%H%M%S')
        print(f"Warning: 'run_id' not found in log file. Using fallback ID: {run_id}")

    # 4. Plot comparison
    plot_comparison(
        single_agent_metrics=single_agent_log["metrics"],
        multi_agent_metrics=multi_agent_log["metrics"],
        dataset_name=args.dataset,
        run_id=run_id
    )

if __name__ == "__main__":
    main() 