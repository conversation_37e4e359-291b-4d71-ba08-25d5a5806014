# 多智能体仇恨言论检测框架技术文档

## 概述

本文档介绍了一个多智能体协调的仇恨言论检测框架。该框架集成了三个不同类型的智能体：DeBERTa本地模型、LLM单智能体和LLM检索增强智能体，通过在线学习算法实现动态权重分配和协调决策。

## 1. 框架架构

### 1.1 整体架构

框架采用多智能体协调架构，包含三个核心智能体和一个协调引擎。每个智能体独立进行推理，协调引擎负责权重分配和最终决策。

### 1.2 组件结构

```
┌─────────────────────────────────────────────────────────────┐
│                    多智能体协调框架                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   DeBERTa   │  │ LLM单智能体  │  │  LLM检索增强智能体   │  │
│  │   本地模型   │  │             │  │                    │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
│           │              │                    │             │
│           └──────────────┼────────────────────┘             │
│                          │                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              在线学习协调引擎                            │ │
│  │  • 权重初始化                                           │ │
│  │  • 权重更新算法                                         │ │
│  │  • 决策融合机制                                         │ │
│  │  • 学习率调节                                           │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. 智能体组件

### 2.1 DeBERTa本地模型智能体

**实现方式:**
- 基于Microsoft DeBERTa-v3-base预训练模型
- 本地部署，使用PyTorch框架
- 针对仇恨言论检测任务进行微调
- 输出二分类结果和置信度分数

**技术规格:**
- 模型参数: 184M
- 输入长度: 最大512 tokens
- 推理时间: 平均0.06秒
- 输出格式: {"verdict": 0/1, "confidence": float}

### 2.2 LLM单智能体

**实现方式:**
- 基于GPT-3.5-turbo-0125模型
- 通过OpenAI API调用
- 使用结构化提示进行推理
- 返回分类结果和推理过程

**提示模板:**
```
任务：仇恨言论检测
输入文本：{text}
输出格式：JSON
要求：分析文本是否包含基于身份特征的攻击性、贬低性或煽动性内容
```

**技术规格:**
- API调用延迟: 1-2秒
- 上下文长度: 4096 tokens
- 输出格式: {"verdict": 0/1, "confidence": float, "reasoning": string}

### 2.3 LLM检索增强智能体

**实现方式:**
- 结合ChromaDB向量数据库和LLM推理
- 使用text-embedding-3-small生成文本嵌入
- 检索相似样本作为上下文
- 基于检索结果进行增强推理

**工作流程:**
1. 输入文本 → 嵌入向量生成
2. 向量数据库 → 相似性检索(top-k)
3. 检索结果 + 原文本 → LLM推理
4. 输出分类结果和置信度

**技术规格:**
- 嵌入维度: 1536
- 检索数量: 5个相似样本
- 总处理时间: 2-4秒
- 向量数据库: ChromaDB持久化存储

## 3. 协调机制

### 3.1 权重初始化

**初始化方法:**
- 使用随机数生成器分配初始权重
- 权重范围: [0.2, 0.5]
- 归一化处理确保权重和为1.0
- 每次运行使用不同的随机种子

**实现代码:**
```python
def _random_initialize_weights(self):
    weights = {}
    for agent in self.agents:
        weights[agent] = random.uniform(0.2, 0.5)

    # 归一化
    total = sum(weights.values())
    for agent in weights:
        weights[agent] /= total

    return weights
```

### 3.2 权重更新算法

**更新公式:**
```
gradient_i = learning_rate × (y_true - y_pred) × agent_prediction_i
momentum_i = 0.9 × momentum_i + gradient_i
weight_i = weight_i + momentum_i
```

**实现流程:**
1. 计算每个智能体的预测误差
2. 基于误差计算梯度
3. 应用动量优化
4. 更新权重并归一化

**代码实现:**
```python
def update_weights(self, agent_results, true_label):
    for agent_name, result in agent_results.items():
        if result["verdict"] in [0, 1]:
            error = true_label - result["verdict"]
            gradient = self.learning_rate * error * result["verdict"]

            self.momentum[agent_name] = (
                0.9 * self.momentum[agent_name] + gradient
            )
            self.weights[agent_name] += self.momentum[agent_name]

    self._normalize_weights()
```

### 3.3 学习率调节

**调节策略:**
- 初始学习率: 0.02
- 基于性能历史动态调整
- 调整范围: [0.01, 0.02]
- 每10个样本评估一次调整需求

## 4. 决策融合机制

### 4.1 加权融合算法

**融合公式:**
```
final_prediction = Σ(weight_i × prediction_i) / Σ(weight_i)
final_confidence = Σ(weight_i × confidence_i) / Σ(weight_i)
```

**决策规则:**
- 当加权预测值 ≥ 0.5 时，最终判定为仇恨言论(1)
- 当加权预测值 < 0.5 时，最终判定为非仇恨言论(0)
- 置信度为各智能体置信度的加权平均

### 4.2 实现代码

```python
def _weighted_coordination(self, agent_results, weights):
    valid_agents = {k: v for k, v in agent_results.items()
                   if v["verdict"] in [0, 1]}

    weighted_prediction = 0.0
    weighted_confidence = 0.0
    total_weight = 0.0

    for agent_name, result in valid_agents.items():
        agent_weight = weights.get(agent_name, 0.0)
        weighted_prediction += result["verdict"] * agent_weight
        weighted_confidence += result["confidence"] * agent_weight
        total_weight += agent_weight

    if total_weight > 0:
        weighted_prediction /= total_weight
        weighted_confidence /= total_weight

    final_verdict = 1 if weighted_prediction >= 0.5 else 0

    return {
        "verdict": final_verdict,
        "confidence": weighted_confidence,
        "weights": weights
    }
```

### 4.3 并行处理机制

**异步调用实现:**
```python
async def parallel_agent_inference(self, text):
    tasks = [
        self.deberta_agent.predict_async(text),
        self.llm_single_agent.predict_async(text),
        self.llm_rag_agent.predict_async(text)
    ]

    results = await asyncio.gather(*tasks, return_exceptions=True)
    return self._process_results(results)
```

**处理流程:**
1. 同时启动三个智能体的推理任务
2. 等待所有任务完成或超时
3. 收集各智能体的预测结果
4. 应用加权融合算法得出最终决策

## 5. 数据处理

### 5.1 数据加载

**支持的数据集格式:**
- CSV格式，包含text和label列
- JSON格式，包含文本和标签字段
- 自定义格式通过DataLoader类处理

**数据预处理:**
```python
class DataLoader:
    def __init__(self, dataset_name):
        self.dataset_name = dataset_name
        self.data = self._load_data()

    def _load_data(self):
        # 根据数据集名称加载对应数据
        # 进行文本清理和标准化
        # 返回处理后的数据
        pass

    def get_sample(self, index):
        return self.data[index]
```

### 5.2 向量数据库管理

**ChromaDB集成:**
```python
class VectorDatabase:
    def __init__(self, dataset_name):
        self.client = chromadb.PersistentClient(path="./vector_db")
        self.collection = self.client.get_or_create_collection(
            name=dataset_name,
            embedding_function=embedding_functions.OpenAIEmbeddingFunction(
                api_key=os.getenv("OPENAI_API_KEY"),
                model_name="text-embedding-3-small"
            )
        )

    def add_documents(self, texts, labels):
        self.collection.add(
            documents=texts,
            metadatas=[{"label": label} for label in labels],
            ids=[f"doc_{i}" for i in range(len(texts))]
        )

    def query_similar(self, query_text, n_results=5):
        results = self.collection.query(
            query_texts=[query_text],
            n_results=n_results
        )
        return results
```

## 6. 系统配置

### 6.1 环境依赖

```
Python >= 3.8
PyTorch >= 1.9.0
Transformers >= 4.20.0
ChromaDB >= 0.4.0
OpenAI >= 1.0.0
numpy >= 1.21.0
pandas >= 1.3.0
scikit-learn >= 1.0.0
```

### 6.2 配置参数

```python
class MultiAgentConfig:
    def __init__(self):
        self.learning_rate = 0.02
        self.momentum_factor = 0.9
        self.confidence_threshold = 0.6
        self.max_iterations = 1000
        self.random_seed = 42
        self.enable_logging = True
        self.log_interval = 10
```

### 6.3 使用接口

```python
# 初始化系统
system = MultiAgentSystem(config)

# 单样本预测
result = system.predict(text)

# 批量评估
results = system.evaluate(dataset, num_samples)

# 获取当前权重
weights = system.get_current_weights()
```

## 7. 日志与监控

### 7.1 日志记录格式

**单次预测日志:**
```json
{
  "sample_id": 1,
  "text": "输入文本内容",
  "true_label": 1,
  "agent_results": {
    "deberta": {"verdict": 1, "confidence": 0.95},
    "llm_single": {"verdict": 1, "confidence": 0.80},
    "llm_retrieval": {"verdict": 1, "confidence": 0.85}
  },
  "weights": {
    "deberta": 0.35,
    "llm_single": 0.25,
    "llm_retrieval": 0.40
  },
  "final_decision": {
    "verdict": 1,
    "confidence": 0.87
  },
  "processing_time": 2.45,
  "timestamp": "2025-01-29T23:41:16"
}
```

### 7.2 性能监控

**关键指标跟踪:**
- 各智能体的预测准确率
- 权重变化趋势
- 处理时间统计
- 系统整体性能指标

**监控实现:**
```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            "accuracy_history": [],
            "weight_history": [],
            "processing_times": [],
            "agent_performance": {}
        }

    def log_prediction(self, result, processing_time):
        self.metrics["processing_times"].append(processing_time)
        self.metrics["weight_history"].append(result["weights"])

    def get_current_stats(self):
        return {
            "avg_processing_time": np.mean(self.metrics["processing_times"]),
            "current_weights": self.metrics["weight_history"][-1],
            "total_predictions": len(self.metrics["processing_times"])
        }
```

---

**文档生成时间**: 2025年1月29日
**框架版本**: v2.0 (在线学习版本)
**技术栈**: Python, PyTorch, Transformers, ChromaDB, OpenAI API
