# Data Science & Core
numpy==1.26.4
pandas==2.2.2
scikit-learn==1.5.1
matplotlib==3.9.2

# NLP & ML Frameworks
# torch version inferred from the 'pytorch_2.6.0_cu124' directory
torch==2.6.0+cu124
transformers==4.50.0
# The version '1.8.1' from pip freeze seems incorrect, sticking to a known good version from original file.
accelerate==0.26.1
sentencepiece==0.2.0
# The following packages were in the original file but not found in 'pip freeze'. Keeping them.
einops==0.7.0
peft==0.7.1
bitsandbytes==0.41.3

# API & Web
openai==1.88.0
fastapi==0.115.9
uvicorn==0.34.2
requests==2.31.0

# Vector Database
chromadb==0.4.22

# Other NLP & utility libraries
nltk
gensim
tqdm 