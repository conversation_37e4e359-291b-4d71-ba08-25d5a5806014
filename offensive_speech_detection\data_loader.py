import os
import pandas as pd
import json

class DataLoader:
    """数据集加载器基类"""
    def __init__(self, dataset_path):
        self.dataset_path = dataset_path
    
    def load_data(self):
        """加载数据集"""
        raise NotImplementedError("子类必须实现此方法")
    
    def get_sample(self, num_samples=10, start_idx=0):
        """获取样本"""
        data = self.load_data()
        if start_idx >= len(data):
            raise ValueError(f"起始索引 {start_idx} 超出数据集大小 {len(data)}")
        
        end_idx = min(start_idx + num_samples, len(data))
        return data[start_idx:end_idx]

class DynamicallyHateDatasetLoader(DataLoader):
    """Dynamically-Generated-Hate-Speech-Dataset数据集加载器"""
    def __init__(self, dataset_path="datasets/Dynamically-Generated-Hate-Speech-Dataset"):
        super().__init__(dataset_path)
        # 使用test.csv作为测试文件
        self.test_file = os.path.join(dataset_path, "dynamically_hate_test.csv")
        
    def load_data(self):
        """加载数据集"""
        df = pd.read_csv(self.test_file)
        
        # 转换为标准格式
        data = []
        for _, row in df.iterrows():
            # 将标签转换为数字，hate -> 1, nothate -> 0
            label = 1 if row["label"] == "hate" else 0
            
            data.append({
                "text": row["text"],
                "label": label,
                "original_label": row["label"],
                "type": row["type"],
                "target": row["target"]
            })
        
        return data

class HateSpeechOffensiveDatasetLoader(DataLoader):
    """Hate-Speech-and-Offensive-Language数据集加载器"""
    def __init__(self, dataset_path="datasets/hate-speech-and-offensive-language"):
        super().__init__(dataset_path)
        # 使用test.csv作为测试文件
        self.test_file = os.path.join(dataset_path, "hate_speech_offensive_test.csv")
        
    def load_data(self):
        """加载数据集"""
        df = pd.read_csv(self.test_file)
        
        # 转换为标准格式
        data = []
        for _, row in df.iterrows():
            # 将标签转换为数字，class=0(仇恨言论)或class=1(攻击性语言)转为1，class=2(无攻击性)转为0
            label = 0 if row["class"] == 2 else 1
            
            data.append({
                "text": row["tweet"],
                "label": label,
                "original_label": int(row["class"]),
                "hate_speech_count": row["hate_speech"],
                "offensive_language_count": row["offensive_language"],
                "neither_count": row["neither"]
            })
        
        return data

class HateSpeechStormfrontDatasetLoader(DataLoader):
    """Hate-Speech-Dataset (Stormfront)数据集加载器"""
    def __init__(self, dataset_path="datasets/hate-speech-dataset"):
        super().__init__(dataset_path)
        # 使用test.csv作为测试文件
        self.test_file = os.path.join(dataset_path, "hate_speech_stormfront_test.csv")
        
    def load_data(self):
        """加载数据集"""
        df = pd.read_csv(self.test_file)
        
        # 转换为标准格式
        data = []
        for _, row in df.iterrows():
            # 将标签转换为数字，hate -> 1, noHate -> 0
            label = 1 if row["label"] == "hate" else 0
            
            data.append({
                "text": row["text"],
                "label": label,
                "original_label": row["label"],
                "file_id": row["file_id"]
            })
        
        return data

class ImplicitHateDatasetLoader(DataLoader):
    """Implicit-Hate-Corpus数据集加载器"""
    def __init__(self, dataset_path="datasets/implicit-hate-corpus"):
        super().__init__(dataset_path)
        # 使用test.csv作为测试文件
        self.test_file = os.path.join(dataset_path, "implicit_hate_test.csv")

    def load_data(self):
        """加载数据集"""
        df = pd.read_csv(self.test_file)

        # 转换为标准格式
        data = []
        for _, row in df.iterrows():
            # 将标签转换为数字，explicit_hate/implicit_hate -> 1, not_hate -> 0
            label = 0 if row["class"] == "not_hate" else 1

            data.append({
                "text": row["post"],
                "label": label,
                "original_label": row["class"]
            })

        return data

class CHSDDatasetLoader(DataLoader):
    """CHSD (中文仇恨言论侦测数据集) 数据集加载器"""
    def __init__(self, dataset_path="datasets/CHSD"):
        super().__init__(dataset_path)
        # 使用test.csv作为测试文件
        self.test_file = os.path.join(dataset_path, "test.csv")

    def load_data(self):
        """加载数据集"""
        df = pd.read_csv(self.test_file)

        # 转换为标准格式
        data = []
        for _, row in df.iterrows():
            # 标签已经是数字格式：0=安全，1=仇恨言论
            label = int(row["label"])

            data.append({
                "text": row["text"],
                "label": label,
                "original_label": label
            })

        return data

class COLDatasetLoader(DataLoader):
    """COLDataset (中文冒犯语言检测数据集) 数据集加载器"""
    def __init__(self, dataset_path="datasets/COLDataset"):
        super().__init__(dataset_path)
        # 使用test.csv作为测试文件
        self.test_file = os.path.join(dataset_path, "test.csv")

    def load_data(self):
        """加载数据集"""
        df = pd.read_csv(self.test_file)

        # 转换为标准格式
        data = []
        for _, row in df.iterrows():
            # 使用label列：0=safe，1=offensive
            label = int(row["label"])

            # 构建数据项
            data_item = {
                "text": row["TEXT"],
                "label": label,
                "original_label": label,
                "topic": row["topic"]
            }

            # 如果有细粒度标签，也保存
            if "fine-grained-label" in row:
                data_item["fine_grained_label"] = int(row["fine-grained-label"])

            data.append(data_item)

        return data

class ToxiCNDatasetLoader(DataLoader):
    """ToxiCN (中文毒性语言检测数据集) 数据集加载器"""
    def __init__(self, dataset_path="datasets/ToxiCN"):
        super().__init__(dataset_path)
        # 使用test.csv作为测试文件
        self.test_file = os.path.join(dataset_path, "toxicn_test.csv")

    def load_data(self):
        """加载数据集"""
        df = pd.read_csv(self.test_file)

        # 转换为标准格式
        data = []
        for _, row in df.iterrows():
            # 使用toxic列：0=non-toxic，1=toxic
            label = int(row["toxic"])

            data.append({
                "text": row["content"],
                "label": label,
                "original_label": label,
                "platform": row["platform"],
                "topic": row["topic"],
                "toxic_type": row.get("toxic_type", None),
                "expression": row.get("expression", None),
                "target": row.get("target", None)
            })

        return data

def get_dataset_loader(dataset_name):
    """获取数据集加载器"""
    # 支持新的数据集名称格式
    if dataset_name == "Dynamically-Generated-Hate-Speech-Dataset" or dataset_name == "DynamicallyHate":
        return DynamicallyHateDatasetLoader()
    elif dataset_name == "hate-speech-and-offensive-language" or dataset_name == "HateSpeechOffensive":
        return HateSpeechOffensiveDatasetLoader()
    elif dataset_name == "hate-speech-dataset" or dataset_name == "HateSpeechStormfront":
        return HateSpeechStormfrontDatasetLoader()
    elif dataset_name == "implicit-hate-corpus" or dataset_name == "ImplicitHate":
        return ImplicitHateDatasetLoader()
    elif dataset_name == "CHSD":
        return CHSDDatasetLoader()
    elif dataset_name == "COLDataset":
        return COLDatasetLoader()
    elif dataset_name == "ToxiCN":
        return ToxiCNDatasetLoader()
    else:
        raise ValueError(f"不支持的数据集: {dataset_name}")