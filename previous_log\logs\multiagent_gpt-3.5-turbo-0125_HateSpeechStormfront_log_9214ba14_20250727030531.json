{"run_id": "9214ba14", "dataset": "HateSpeechStormfront", "model": "gpt-3.5-turbo-0125", "detector_type": "intelligent_coordination", "system_type": "intelligent_coordination", "num_samples": 100, "metrics": {"accuracy": 0.86, "precision": 0.8214285714285714, "recall": 0.92, "f1": 0.8679245283018868, "confusion_matrix": [[40, 10], [4, 46]], "avg_processing_time": 4.85714361667633}, "results": [{"verdict": 0, "confidence": 0.5549334324896336, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.94); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9394674599170685, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.939 confidence", "processing_time": 0.6881043910980225}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it is a neutral question about a scientific topic.", "processing_time": 1.4749970436096191}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it is a question about a scientific topic without any derogatory or harmful language.", "processing_time": 3.264249563217163, "retrieved_examples": "Example 1: \"Why have n't we thought about that over here ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"We are far closer to being neanderthals than apes .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"You 're telling me that there 's something we Swedes have done right ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 3.2669923305511475, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 1, "dynamic_weight_predictions": 1, "consensus_predictions": 0, "avg_coordination_time": 3.2669923305511475, "system_type": "intelligent_coordination"}, "text": "And hey , we have not talked about women 's mitochondriat at all ?", "true_label": 0, "processing_time": 3.2669923305511475, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5494351461529732, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8954811692237854, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.895 confidence", "processing_time": 0.07072305679321289}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply describes the location of a boat without containing any insults, threats, or derogatory content.", "processing_time": 1.1961579322814941}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply describes a situation without containing any offensive language or intent.", "processing_time": 2.67546010017395, "retrieved_examples": "Example 1: \"It 's there .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"This is the one my neighbors have .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"And so friendly too .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.681518793106079, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 2, "dynamic_weight_predictions": 2, "consensus_predictions": 0, "avg_coordination_time": 2.9742555618286133, "system_type": "intelligent_coordination"}, "text": "My buddy 's boat is anchored beside it .", "true_label": 0, "processing_time": 2.681518793106079, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5013730749487877, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.51); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.50)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.5109845995903015, "reasoning": "Hybrid classification using best_zero_shot with 0.511 confidence", "processing_time": 0.07012414932250977}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive speech, no explicit insults, threats, or derogatory content", "processing_time": 0.8341903686523438}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply expresses a long absence from the platform without any offensive language or intent.", "processing_time": 2.551027297973633, "retrieved_examples": "Example 1: \"Long time no post , <PERSON> , good to see you back .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Hello to all my friends here on the forum .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Mostly gone .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.5566186904907227, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 3, "dynamic_weight_predictions": 3, "consensus_predictions": 0, "avg_coordination_time": 2.83504327138265, "system_type": "intelligent_coordination"}, "text": "Hi i have not been on here in ages .", "true_label": 0, "processing_time": 2.5566186904907227, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5546621084213257, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.94); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9372968673706055, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.937 confidence", "processing_time": 0.06380963325500488}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain explicit insults, threats, hatred, or derogatory content. It merely mentions the absence of a mugshot picture on a page and refers to watching a video report.", "processing_time": 1.0523676872253418}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech with racial connotations (TNB). It implies a negative stereotype and is derogatory towards a specific group of people.", "processing_time": 2.1863784790039062, "retrieved_examples": "Example 1: \"``Confrontingthe Girl Who Shot Me In the Face '' -- Dr. <PERSON> - <PERSON> The female negro is making excuses like its not her fault but we all know its TNB , I feel for that poor white woman\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Did you see the video .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Fox news reported last night that this beast was caught eating parts of this young man when the police got onto the bus to apprehend him !\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.191892147064209, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 4, "dynamic_weight_predictions": 4, "consensus_predictions": 0, "avg_coordination_time": 2.6742554903030396, "system_type": "intelligent_coordination"}, "text": "Oh yeah , no mugshot picture on the page of course , you have to watch the video report to find out it was yet another case of TNB .", "true_label": 1, "processing_time": 2.191892147064209, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5520710404962301, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.92); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.916568323969841, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.917 confidence", "processing_time": 0.0605316162109375}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains explicit support for genocide, which is offensive and hateful.", "processing_time": 0.7761003971099854}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it promotes the idea of genocide against a specific group of people.", "processing_time": 2.171532392501831, "retrieved_examples": "Example 1: \"They are supporting the destruction of their future generations .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"They have caused their own hate from the nations by their hate for everyone else .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"They mutilate their own , and they do their best to see to it that those whom they see as their goyim slaves are also marked ....\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.1743574142456055, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 5, "dynamic_weight_predictions": 5, "consensus_predictions": 0, "avg_coordination_time": 2.5742758750915526, "system_type": "intelligent_coordination"}, "text": "By their words and deeds , they declare themselves to be staunchly in favor of the genocide of our people .", "true_label": 1, "processing_time": 2.1743574142456055, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5542313195765018, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.93); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9338505566120148, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.934 confidence", "processing_time": 0.07236313819885254}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and anti-Semitic remarks, making it offensive.", "processing_time": 2.2340664863586426}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech with derogatory remarks towards white people and Jewish individuals.", "processing_time": 2.040994882583618, "retrieved_examples": "Example 1: \"This is why libtards and Jews call us white supremacists and get away with it .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"He is just telling the truth as usual the filthy jews need to clamp down as soon as possible .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I wonder why the Jewish Media always pretends that all the blood thirsty third world savages are victims and it is all <PERSON><PERSON> 's fault ?\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.2367935180664062, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 6, "dynamic_weight_predictions": 6, "consensus_predictions": 0, "avg_coordination_time": 2.518028815587362, "system_type": "intelligent_coordination"}, "text": "No need to allow FACTS to get in the way of a good White guilt message ! ... and the jew laughs loudly !", "true_label": 1, "processing_time": 2.2367935180664062, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5449202861636877, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.86); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8593622893095016, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.859 confidence", "processing_time": 0.06404256820678711}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and promotes racial division by referring to 'white guilt'", "processing_time": 0.9652910232543945}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain explicit offensive language or derogatory remarks.", "processing_time": 2.2567451000213623, "retrieved_examples": "Example 1: \"When most whites today have so much white guilt laid upon them that they feel they have to act black or have sexual relations and children with blacks just to be accepted there is a problem with the white race memo .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"The powers that be want all Whites dumbed down with Negro blood .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"They do it because they know it pisses the whites off , by showing disrespect as such for other people shows they have no respect for themselves .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.262456178665161, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 7, "dynamic_weight_predictions": 7, "consensus_predictions": 0, "avg_coordination_time": 2.48151843888419, "system_type": "intelligent_coordination"}, "text": "Its simple , they want to create white guilt , .", "true_label": 1, "processing_time": 2.262456178665161, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5542913526296616, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9343308210372925, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.934 confidence", "processing_time": 0.06815409660339355}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive speech, expressing doubt about visibility based on registration status", "processing_time": 1.6692941188812256}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is not offensive as it does not contain any explicit language or derogatory remarks.", "processing_time": 2.3990235328674316, "retrieved_examples": "Example 1: \"I registered to make comment but there appear to have a critical error , o dear .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"This is a new version that has not been available before now .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"I have no idea of how many might be here .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.404360055923462, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 8, "dynamic_weight_predictions": 8, "consensus_predictions": 0, "avg_coordination_time": 2.471873641014099, "system_type": "intelligent_coordination"}, "text": "If you are not registered I think that maybe you will not be able to see this .", "true_label": 0, "processing_time": 2.404360055923462, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5445233695209026, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.86); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8561869561672211, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.856 confidence", "processing_time": 0.07234787940979004}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains derogatory and discriminatory language towards ethnic groups, implying they are responsible for turning neighborhoods into slums.", "processing_time": 0.8842592239379883}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by making derogatory remarks about people of different ethnicities and implying that they are responsible for turning a neighborhood into a slum.", "processing_time": 2.3089616298675537, "retrieved_examples": "Example 1: \"Such a shàme how we 've allowed blacks and browns to destroy every last square inch of our cities .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"One reason we moved out of our ( at one time 99 % white ) neighborhood was because massive numbers of Chinese moved in and i swear to you a literal rotten smell came with them .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Once the negros start coming in bunches it will be a hellhole and the whites will have to leave again and start a new community .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.313559055328369, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 9, "dynamic_weight_predictions": 9, "consensus_predictions": 0, "avg_coordination_time": 2.4542831314934626, "system_type": "intelligent_coordination"}, "text": "We all know that when their are too many ethnics , the neighbourhood soon turn 's into a slum .", "true_label": 1, "processing_time": 2.313559055328369, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5556179881095886, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.94); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.944943904876709, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.945 confidence", "processing_time": 0.06380915641784668}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit threats and violence towards a specific group, which is highly offensive and inappropriate.", "processing_time": 1.3753752708435059}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains violent and discriminatory language towards a specific group of people.", "processing_time": 2.307398796081543, "retrieved_examples": "Example 1: \"I guess I 'll just stick to homeschooling my kids , when and if I have them ...\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Went outside for recess , and noticed two niglet boys being hostile and ready to rumble , and I figured right that they jumped on each other after returning to class , grabbing Niglet Number One , hauling him outside and pressed that panic button again .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"The reason y i dropped out of school was because either , i drop out or i got kicked out. i always fought with the packi gang at my school. and i was never going to back down. in auto class i handed the n * igger a monkey wrench and told him the name suited him aswell. all the non-whites hated me because i wore a `` nazi punk '' and a `` white pride '' patch on my coat and backpack. a got in a fight with some black chick and i won. the next day her butchy sister caught up with me in the hall way and said that after school her and i where gonna fight. i told her `` no if u wanna fight , we 're gonna fight right here right now. not after school where u can gather the rest of u monkies up so it 's an uneven fight '' . she huffed and stormed away. - we neevr did get to fight the last straw was the first day of school i had a forgein substitute teacher. so i was talkin and and this guy was on my case about turning around and getting my work done. he had to ask me this a few times and then finially i stood up and said , `` look , i 'm white ur black , i should be telling u whut to do ! '' ohhh man , my guidance conslour did n't like that nor did my principal .... ( this was n't at school this happened a month or so ago but these people went to my school ) i had a n * igger try to rob me at knife point in the forest while his gf sat back and watched. my friend came by and jumped him and i personally went after the chick. she ran and i caught up to her and smashed her head into a tree. thats only some of my stories ....... i got a few more but i dont have the time\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.310615062713623, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 10, "dynamic_weight_predictions": 10, "consensus_predictions": 0, "avg_coordination_time": 2.4399163246154787, "system_type": "intelligent_coordination"}, "text": "Perhaps I should walk back into my H.S. when it starts Monday and get on the P.A. system and say I want to tie all * * * * * * s to the back of my car and drag them across the ground till they die , and then say I 'm joking .", "true_label": 1, "processing_time": 2.310615062713623, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.552602281793952, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.92); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9208182543516159, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.921 confidence", "processing_time": 0.06068992614746094}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains a derogatory and racist remark towards white people, inciting hatred and promoting harmful stereotypes.", "processing_time": 0.9048328399658203}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it promotes violence and derogatory stereotypes against a specific racial group.", "processing_time": 5.3747334480285645, "retrieved_examples": "Example 1: \"We have to kill off all of the Whites so we can go back to eating cow dung ! \"\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Once the negros start coming in bunches it will be a hellhole and the whites will have to leave again and start a new community .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Were Whites to leave in large numbers leaving few Whites behind in South Africa the country would start going down hill and then maybe more Whites would begin to see what losers Negros are and how they cannot even maintain what is given to them .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 5.3789496421813965, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 11, "dynamic_weight_predictions": 11, "consensus_predictions": 0, "avg_coordination_time": 2.7071011716669258, "system_type": "intelligent_coordination"}, "text": "They will eventually kill off the whites and go back to the jungle ways .", "true_label": 1, "processing_time": 5.3789496421813965, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5484960402051609, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.89); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8879683216412863, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.888 confidence", "processing_time": 0.06662654876708984}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language and hatred towards a specific group", "processing_time": 0.937659740447998}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech referring to individuals as 'scumbags'.", "processing_time": 2.4249427318573, "retrieved_examples": "Example 1: \"The thing that concerns me is that he got a house off the council thats a house that a Irish family should have got .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Yeah they are buying a lot of houses around my area to probz so they can house there massive inbred family when they come over to the UK .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Seen some girl about 6 years old begging on grafton street once and we were coming out of a shop a few mins later when her father pulls up in a decent car. scum. the lot of them .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.4293324947357178, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 12, "dynamic_weight_predictions": 12, "consensus_predictions": 0, "avg_coordination_time": 2.6839537819226584, "system_type": "intelligent_coordination"}, "text": "Its a disgrace my sister has been on the waiting list for 6 years and their giving away houses to these scumbags", "true_label": 1, "processing_time": 2.4293324947357178, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5572809129953384, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.96); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9582473039627075, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.958 confidence", "processing_time": 0.05960845947265625}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text contains a reference to a news event, without any explicit offensive content.", "processing_time": 0.820396900177002}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive.", "processing_time": 2.148165464401245, "retrieved_examples": "Example 1: \"YouTube - Black mugger sentenced to 75 years in prison for hate crimes against elderly women YouTube - <PERSON><PERSON> 's mace vs black guy 's kung fu YouTube - African American man sprays semen on white woman in supermarket YouTube - White guy beaten nearly to death by Blacks for dating a Black woman , no hate crime there YouTube - Broadcast Yourself .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"More negro savagery : YouTube - Detroit : 3 Black Males Murder pregnant white woman .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Here is a video of the event that is worth watching just to see a beautiful White lady screaming `` scum , scum , scum , f * * * ing scum !!! '' at the muslim filth .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.1532392501831055, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 13, "dynamic_weight_predictions": 13, "consensus_predictions": 0, "avg_coordination_time": 2.643129587173462, "system_type": "intelligent_coordination"}, "text": "Seattle bus attack on a blind woman : YouTube - Broadcast Yourself .", "true_label": 0, "processing_time": 2.1532392501831055, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5501533895730972, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.90); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9012271165847778, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.901 confidence", "processing_time": 0.06619095802307129}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory terms and promotes hatred towards specific racial and religious groups.", "processing_time": 0.8022432327270508}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by using derogatory terms and promoting negative stereotypes about different racial and ethnic groups.", "processing_time": 2.132856607437134, "retrieved_examples": "Example 1: \"`` Those evil Whites .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Negros are so dumb .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"She may or may not be a Jew but she 's certainly stupid , she seems to think the Blacks wo n't kill her alongside every other White they can get their dirty hands on , what a muppet !\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.137681007385254, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 14, "dynamic_weight_predictions": 14, "consensus_predictions": 0, "avg_coordination_time": 2.60702611718859, "system_type": "intelligent_coordination"}, "text": "Well, at least these dumb negroes got the fact that Jews are evil right ; though I suspect whitey and the jews are one in the same to their primitive minds ...", "true_label": 1, "processing_time": 2.137681007385254, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5188916325569153, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.65); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.52)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.6511330604553223, "reasoning": "Hybrid classification using best_zero_shot with 0.651 confidence", "processing_time": 0.06045842170715332}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain explicit insults, threats, or derogatory content. It simply advises against initiating conflict.", "processing_time": 0.8690052032470703}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it does not contain any derogatory language or intent to harm.", "processing_time": 2.101984739303589, "retrieved_examples": "Example 1: \"On second thought they wo n't report it at all .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Besides who would even bother to help them ?\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Do n't sit around and argue with them , just act like they are pathetic ( which they are ) and try to shame them .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.107473373413086, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 15, "dynamic_weight_predictions": 15, "consensus_predictions": 0, "avg_coordination_time": 2.5737226009368896, "system_type": "intelligent_coordination"}, "text": "First off they probably wont do anything , second off dont start anything .", "true_label": 0, "processing_time": 2.107473373413086, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5501126479357481, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9009011834859848, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.901 confidence", "processing_time": 0.06402015686035156}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement expresses racial prejudice and implies that black players are inferior representatives of their nations, which is offensive and discriminatory.", "processing_time": 0.8779153823852539}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it expresses discriminatory views based on race.", "processing_time": 2.559650182723999, "retrieved_examples": "Example 1: \"The best way to prevent Black on White rape is to succeed in our goal of removing them all from white countries .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"It's a shame that universities of Canada import black and brown people .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Were Whites to leave in large numbers leaving few Whites behind in South Africa the country would start going down hill and then maybe more Whites would begin to see what losers Negros are and how they cannot even maintain what is given to them .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.564643144607544, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 16, "dynamic_weight_predictions": 16, "consensus_predictions": 0, "avg_coordination_time": 2.5731551349163055, "system_type": "intelligent_coordination"}, "text": "Seeing the teams of white nations have black players in their ranks is a depressing site to see.They should be representing the best of our nations not the worst", "true_label": 1, "processing_time": 2.564643144607544, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5614173859357834, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.99); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9913390874862671, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.991 confidence", "processing_time": 0.05932474136352539}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit offensive language and derogatory term directed at an individual", "processing_time": 0.7547316551208496}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language and a derogatory term, 'buckwheat', which is racially insensitive and offensive.", "processing_time": 2.152336835861206, "retrieved_examples": "Example 1: \"String ' em up !\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"X ) `` Stone Family Band : with new in-law at far left and new future in-law at far right .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Remember to not make it just about being anti-everyone else , but to also make it about them being proud of their own family heritage .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.1554903984069824, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 17, "dynamic_weight_predictions": 17, "consensus_predictions": 0, "avg_coordination_time": 2.5485866210039925, "system_type": "intelligent_coordination"}, "text": "Do ` F * ck with our families , buckwheat !!", "true_label": 0, "processing_time": 2.1554903984069824, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5472911931574345, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.88); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8783295452594757, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.878 confidence", "processing_time": 0.05873870849609375}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it refers to an Indian symbol for good fortune and a book title.", "processing_time": 0.8369848728179932}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it discusses an Indian symbol for good fortune and mentions the People of Walmart.", "processing_time": 2.0957601070404053, "retrieved_examples": "Example 1: \"I feel like I should pull out my passport when I want into walmart with all the * * * * ing pakis and * * * * in there\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Great job guys .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Such is the sign of our time ...\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.0993175506591797, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 18, "dynamic_weight_predictions": 18, "consensus_predictions": 0, "avg_coordination_time": 2.5236272282070584, "system_type": "intelligent_coordination"}, "text": "Seriously Guys , It 's An Indian Symbol For Good Fortune - People Of Walmart : People Of Walmart", "true_label": 0, "processing_time": 2.0993175506591797, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5396330207586288, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.82); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8170641660690308, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.817 confidence", "processing_time": 0.05943465232849121}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language and expresses hatred towards a specific group", "processing_time": 0.7804582118988037}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by referring to a group of people as 'imported filth'. This dehumanizing language is derogatory and offensive.", "processing_time": 2.4936439990997314, "retrieved_examples": "Example 1: \"They have been taking advantage of our over-welcoming nature for far to long .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"But unfortunately , Maine 's become the dumping ground for Somalis and other African trash .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"The only thing manipulated here are the minds of few foreigners , who reckon they can live amongest superiour , pagan blood .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.5002617835998535, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 19, "dynamic_weight_predictions": 19, "consensus_predictions": 0, "avg_coordination_time": 2.522397467964574, "system_type": "intelligent_coordination"}, "text": "Thanks to the imported filth that , like our land under its current occupation .", "true_label": 1, "processing_time": 2.5002617835998535, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.541855126619339, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.83); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8348410129547119, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.835 confidence", "processing_time": 0.06302118301391602}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains a suggestion to cause harm and discomfort to individuals (chimps) by using stink bombs, which is offensive and inappropriate.", "processing_time": 0.9792139530181885}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to individuals as 'chimps', which is dehumanizing and derogatory.", "processing_time": 5.690107822418213, "retrieved_examples": "Example 1: \"Major chimp-out at hip hop awards .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Apes even have the instinct to basically leave these for dead .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"When she goes outside , the subhuman ape thing is usually out there .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 5.696652889251709, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 20, "dynamic_weight_predictions": 20, "consensus_predictions": 0, "avg_coordination_time": 2.6811102390289308, "system_type": "intelligent_coordination"}, "text": "Dude stink bomb this event , when it gets dark , chuck a bunch of them on the floor , different spots , when the chimps start jiving and grooving , they will step on them .", "true_label": 1, "processing_time": 5.696652889251709, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5492592714726925, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.89); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8940741717815399, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.894 confidence", "processing_time": 0.06850790977478027}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive speech stating a historical fact.", "processing_time": 0.7702009677886963}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it discusses historical migration patterns.", "processing_time": 2.011256694793701, "retrieved_examples": "Example 1: \"I believe it started to become cooler not long after they arrived but the Nordic settlement was in fact expanding ( to the north ) until they encountered the Inuits .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"The Nordic Angelic race came from the Pleiadian 's Star Cluster , and mixed with Iberian-Aryan 's that live in Germanic Countries and Scandinavia .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"The native hunter-gatherers , on the other hand , show 100 % incidence of blue eyes but do not have all the same mutations for light skin as the farmer and Steppe populations .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.016808271408081, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 21, "dynamic_weight_predictions": 21, "consensus_predictions": 0, "avg_coordination_time": 2.6494768119993664, "system_type": "intelligent_coordination"}, "text": "The Neolithic farmers who migrated into Europe originated in Anatolia .", "true_label": 0, "processing_time": 2.016808271408081, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5535290762782097, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9282326102256775, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.928 confidence", "processing_time": 0.0629432201385498}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language and racial stereotypes towards black children", "processing_time": 0.7930138111114502}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech with derogatory language towards black children.", "processing_time": 1.8815298080444336, "retrieved_examples": "Example 1: \"When I am around young people and rap music or negro culture comes up I make gestures implying the savagery of blacks .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"When I see this stuff nowadays it makes me wa n't to punch the white people who fall for it .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I am disgusted by the amount of white hate and brown love that teachers try to pump into my 16 year olds head .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 1.8857629299163818, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 22, "dynamic_weight_predictions": 22, "consensus_predictions": 0, "avg_coordination_time": 2.614762544631958, "system_type": "intelligent_coordination"}, "text": "i do this and the looks i get out of disgust are regular but i happily carry on walking and laugh them off , they think showing me a few pictures of negro kids on a poster without a burger is going to make me want to give money to them ?", "true_label": 0, "processing_time": 1.8857629299163818, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5412680730223656, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.83); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8301445841789246, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.830 confidence", "processing_time": 0.060959815979003906}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it merely expresses impatience towards others' self-care actions.", "processing_time": 0.8927483558654785}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is considered offensive as it implies a lack of care or responsibility towards others.", "processing_time": 3.280641794204712, "retrieved_examples": "Example 1: \"Besides who would even bother to help them ?\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Hell , the way we are deliberately poisoning ourselves I 'm surprised we have lasted this long !\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"How can others be so blind to this ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 3.2852163314819336, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 23, "dynamic_weight_predictions": 23, "consensus_predictions": 0, "avg_coordination_time": 2.643912709277609, "system_type": "intelligent_coordination"}, "text": "How much longer are we going to wait for them to take care of themselves ?", "true_label": 1, "processing_time": 3.2852163314819336, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5419100932776928, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.84); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8352807462215424, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.835 confidence", "processing_time": 0.062378883361816406}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory stereotypes and promotes racial discrimination.", "processing_time": 0.7657465934753418}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains a comparison between different races, suggesting that one race lacks self-control, which can be considered offensive and perpetuates negative stereotypes.", "processing_time": 1.9199237823486328, "retrieved_examples": "Example 1: \"Not even other races mixing it up with each other , its whites mixing with all .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"The whites and the asians evoluated , and the blacks are still behind .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Once again , blacks showing they cannot control themselves .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 1.9256079196929932, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 24, "dynamic_weight_predictions": 24, "consensus_predictions": 0, "avg_coordination_time": 2.6139833430449166, "system_type": "intelligent_coordination"}, "text": "Both Asians and whites are composed and calm and blacks seem to have no self control .", "true_label": 1, "processing_time": 1.9256079196929932, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5573907531797886, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.96); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9591260254383087, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.959 confidence", "processing_time": 0.06098031997680664}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive speech, expressing a suggestion to search for information online", "processing_time": 0.8288919925689697}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply suggests using a search engine to understand the speaker's point.", "processing_time": 2.291991949081421, "retrieved_examples": "Example 1: \"Hope you will find what you are looking for .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"You 're bang on .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"It 's plainly there , all you need to do is act on it .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.296957492828369, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 25, "dynamic_weight_predictions": 25, "consensus_predictions": 0, "avg_coordination_time": 2.601302309036255, "system_type": "intelligent_coordination"}, "text": "Just Google it and you 'll see what I mean .", "true_label": 0, "processing_time": 2.296957492828369, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5496358424425125, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8970867395401001, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.897 confidence", "processing_time": 0.06258726119995117}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content and racial stereotypes towards black individuals", "processing_time": 0.8052377700805664}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it refers to discriminatory actions based on race and promotes stereotypes.", "processing_time": 2.1612374782562256, "retrieved_examples": "Example 1: \"Hell, if he had been white they would have thrown his ass in jail , suspended him from school , and it would have been all over CNN ABC NBC and CBS .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"If it was the other way around and it was white on black we would never of heard the end of it\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"If you think that 's funny , you should have seen the black guy that used to run around my home town wearing a kkk emblem.But people like that are usualy not that bright .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.167670488357544, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 26, "dynamic_weight_predictions": 26, "consensus_predictions": 0, "avg_coordination_time": 2.584624162087074, "system_type": "intelligent_coordination"}, "text": "I bet they wouldnt have done that crap to a black person that had black power or those black fists that the panthers use written on his back pack .", "true_label": 1, "processing_time": 2.167670488357544, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5405900329351425, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.82); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8247202634811401, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.825 confidence", "processing_time": 0.05978965759277344}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content towards Jewish people, implying they use money to deceive others", "processing_time": 0.7809164524078369}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech targeting the Jewish community by associating them with negative stereotypes about money and deceit.", "processing_time": 2.1144535541534424, "retrieved_examples": "Example 1: \"So the Jews are Gods chosen people and we are the dumb cattle used to bring the Jews back to Gods fold .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Jewish unfortunately .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"They have no allegiance to Ukraine , their allegiance is to their god of money , power and the eternal enslavement of the hated goyim .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.1190435886383057, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 27, "dynamic_weight_predictions": 27, "consensus_predictions": 0, "avg_coordination_time": 2.567380437144527, "system_type": "intelligent_coordination"}, "text": "<PERSON> 's own divine intervention , aided by Jewish money swindled from the goyim .", "true_label": 1, "processing_time": 2.1190435886383057, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5369928535073996, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.80); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7959428280591965, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.796 confidence", "processing_time": 0.06512784957885742}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is positive and does not contain any offensive speech or content.", "processing_time": 0.8002619743347168}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it expresses hope for growth and success in organizing events.", "processing_time": 2.1614513397216797, "retrieved_examples": "Example 1: \"Thankfully, the C.W. Camp-Out will go ahead this year .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"I 'm glad that you 're planning on getting out there to do it .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"In fact , this year this `` event '' is going to be this weekend .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.166837453842163, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 28, "dynamic_weight_predictions": 28, "consensus_predictions": 0, "avg_coordination_time": 2.5530753305980136, "system_type": "intelligent_coordination"}, "text": "I 'm hoping things will continue to grow with our smaller meets and will make for a good turn out at our events this year !", "true_label": 0, "processing_time": 2.166837453842163, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5346836075186729, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.78); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.7774688601493835, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.777 confidence", "processing_time": 0.14812874794006348}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by singling out individuals from the Black community and accusing them of knowingly transmitting HIV/AIDS, which can incite hatred and discrimination.", "processing_time": 0.9113283157348633}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it discusses individuals who have been convicted of knowingly transmitting HIV/AIDS, using racial identifiers like 'Blacks'. This can be considered offensive and discriminatory.", "processing_time": 2.631821393966675, "retrieved_examples": "Example 1: \"We've had a couple of similar cases in Finland , when a nig purposefully tried to give dozens of Finnish women hiv/aids .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I've heard that men with AIDS in Africa think having sex with a virgin will cure them .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Yup, Africans breeding like cockroaches , AIDs is not killing them off fast enough to stop their growth !\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.6359827518463135, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 29, "dynamic_weight_predictions": 29, "consensus_predictions": 0, "avg_coordination_time": 2.5559342071927826, "system_type": "intelligent_coordination"}, "text": "I'm not sure what sort of information you 're seeking but I recently found a number of Blacks who have been convicted for knowingly transmitting HIV/AIDS : <PERSON><PERSON><PERSON> http://news.bbc.co.uk/2/hi/uk_news/e...de/3389735.stmJohnsonAzigahttp://en.wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-Yondohttp://en.wikipedia.org/wiki/Stan<PERSON><PERSON>_<PERSON>-YondjoTrevisSmithhttp://en.wikipedia.org/wiki/Trevis_SmithAdrienSylverNduwayohttp://www.thenownews.com/issues06/0...031206nn2.htmlWillieAtkinshttp://www.dallasnews.com/sharedcont....32a4d9fd.htmlNushawnWilliamshttp://en.wikipedia.org/wiki/Nushawn_WilliamsSimonMolhttp://en.wikipedia.org/wiki/<PERSON>_<PERSON>onBandahttp://www.dailymail.co.uk/pages/liv...=5&expand=truePeterMwaihttp://www.crime.co.nz/c-files.asp?ID=36WilliamKaranjahttp://www.amren.com/mtnews/archives...n_rapist_j.phpLinkoyMuhurihttp://news.bbc.co.uk/2/hi/uk_news/e...on/3256352.stmMohammedDicahttp://news.bbc.co.uk/2/hi/uk_news/e...on/3190626.stmFestonKonzanihttp://news.bbc.co.uk/2/hi/uk_news/e...es/4267433.stmGabrielVengesaihttp://www.dailymail.co.uk/pages/liv ... n _ page _ id = 1770", "true_label": 1, "processing_time": 2.6359827518463135, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5559051781892776, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.95); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9472414255142212, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.947 confidence", "processing_time": 0.059511423110961914}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text contains a reference to a website providing educational material on National Socialism and Fascism, without displaying offensive content.", "processing_time": 1.1656827926635742}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains references to National Socialist and Fascist ideologies, which are historically associated with offensive and discriminatory beliefs.", "processing_time": 2.288724899291992, "retrieved_examples": "Example 1: \"There are many interesting listings on this web site , including Stormfront 's own Advance Scout forum which teaches the principles of Pioneer Little Europe : http://www.angelfire.com/nv/micronations/usa.html\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Hitlerjugend Quex Der Ewige Jude Jude Suss Those are some movies the Nazi 's made and are very good and informative .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Might I suggest this documentary series for getting rid of europhobic tendencies ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.292940139770508, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 30, "dynamic_weight_predictions": 30, "consensus_predictions": 0, "avg_coordination_time": 2.547167738278707, "system_type": "intelligent_coordination"}, "text": "You could try looking at the National socialist and Fascist Resource Centre at http://www.ufc.co.uk They cover a whole range of educational material .", "true_label": 0, "processing_time": 2.292940139770508, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5430308145781357, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.84); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8442465166250864, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.844 confidence", "processing_time": 0.06410741806030273}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains a derogatory term 'jigs' which is offensive and derogatory towards a specific group of people", "processing_time": 1.0605354309082031}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive due to the use of a derogatory term 'jigs'.", "processing_time": 2.2341134548187256, "retrieved_examples": "Example 1: \"You 're bang on .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"You are going to have to make this decision on your own .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"I agree .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.2402255535125732, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 31, "dynamic_weight_predictions": 31, "consensus_predictions": 0, "avg_coordination_time": 2.537266377479799, "system_type": "intelligent_coordination"}, "text": "This guy seems to get it but otherwise : Sorry jigs , you 're on your own", "true_label": 1, "processing_time": 2.2402255535125732, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5589299574494362, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.97); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9714396595954895, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.971 confidence", "processing_time": 0.06301093101501465}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and offensive language towards a specific racial group and gender", "processing_time": 0.7794017791748047}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by using derogatory terms and promoting negative stereotypes based on race.", "processing_time": 3.344846487045288, "retrieved_examples": "Example 1: \"I hate to see such beautiful white females become victims of these sick , violent merciless animals .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Blacks seem to be hell bent on embarrassing themselves and acting like animals everywhere they go .. this is pretty typical behaviour ..\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"That's vile , but I ca n't say it 's a big shock , their culture seems to glorify that sort of behaviour ...\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 3.350576877593994, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 32, "dynamic_weight_predictions": 32, "consensus_predictions": 0, "avg_coordination_time": 2.562682330608368, "system_type": "intelligent_coordination"}, "text": "The kind of savagery against females that has become expected from negroids in todays society ...", "true_label": 1, "processing_time": 3.350576877593994, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5527297146618366, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.92); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.921837717294693, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.922 confidence", "processing_time": 0.06551384925842285}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive speech expressing confusion and uncertainty, without any explicit insults, threats, or derogatory content", "processing_time": 0.8342962265014648}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it expresses confusion and uncertainty without containing any explicit offensive language or intent.", "processing_time": 2.648735523223877, "retrieved_examples": "Example 1: \"Now I just need to figure out what you are doing here .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"I 've only recently came to new conclusions ; or found old ones .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Now I watch no TV at all .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.6542322635650635, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 33, "dynamic_weight_predictions": 33, "consensus_predictions": 0, "avg_coordination_time": 2.565456571000995, "system_type": "intelligent_coordination"}, "text": "Now i just dont know what to do or think anymore .", "true_label": 0, "processing_time": 2.6542322635650635, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5611665099859238, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.99); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9893320798873901, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.989 confidence", "processing_time": 0.0665278434753418}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and racist language towards specific groups based on their race and physical appearance.", "processing_time": 0.8224489688873291}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech with derogatory and racist remarks towards a specific group of people.", "processing_time": 2.385037660598755, "retrieved_examples": "Example 1: \"I hate to see such beautiful white females become victims of these sick , violent merciless animals .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"It bothers me in as much it makes me want to vomit , any white woman who lay down with the beast need not try and come back because `` once you go black you 're not wanted back !\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"When ever I see these white children murdered by these sub humans it makes me so angry I could cry .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.3900439739227295, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 34, "dynamic_weight_predictions": 34, "consensus_predictions": 0, "avg_coordination_time": 2.5602973769692814, "system_type": "intelligent_coordination"}, "text": "It makes my flesh crawl when I see white girls having to be felt up as they walk down the hallways by big lipped spear chuckers. : attack", "true_label": 1, "processing_time": 2.3900439739227295, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5052367225289345, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.54); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.51)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.5418937802314758, "reasoning": "Hybrid classification using best_zero_shot with 0.542 confidence", "processing_time": 0.05939054489135742}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The provided text is a URL link and does not contain any offensive speech.", "processing_time": 0.7938780784606934}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it is a link to a forum discussion.", "processing_time": 2.2762696743011475, "retrieved_examples": "Example 1: \"http://www.newnation.tv/forums/showthread.php ? t = 207286\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"http://www.youtube.com/watch ? v = vaEZZnW _ MJo\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"https : //www.stormfront.org/forum/group.php ? groupid = 257\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.2825934886932373, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 35, "dynamic_weight_predictions": 35, "consensus_predictions": 0, "avg_coordination_time": 2.5523629801613943, "system_type": "intelligent_coordination"}, "text": "http://www.amv3.com/forum/viewtopic.php ? p = 5542#5542", "true_label": 0, "processing_time": 2.2825934886932373, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5537508726119995, "reasoning": "激进优化协调决策: deberta(原权重:0.45→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.25→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9300069808959961, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.930 confidence", "processing_time": 0.06283950805664062}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory terms ('Jew' and 'libtard') and expresses hatred towards specific groups, making it offensive.", "processing_time": 0.9920501708984375}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language targeting Jews and using a derogatory term 'libtard'. It implies blame and negativity towards these groups based on their race or political beliefs.", "processing_time": 2.5534749031066895, "retrieved_examples": "Example 1: \"The problem is there 's so many white liberal self hating traitors on the same side as the Jews and nonwhites .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Your both our enemies plain and simple , jews try to destroy us with their immigration and the jew would not be able to get as far as they did if many of them did not look white .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"This is why libtards and Jews call us white supremacists and get away with it .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.44999999999999996, "llm_single": 0.3, "llm_retrieval": 0.25}, "coordination_time": 2.5578415393829346, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 36, "dynamic_weight_predictions": 36, "consensus_predictions": 0, "avg_coordination_time": 2.552515162361993, "system_type": "intelligent_coordination"}, "text": "In the eyes of the Jew and the libtard there is a race problem and the problem is us !", "true_label": 1, "processing_time": 2.5578415393829346, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5304006412625313, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.74); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.53)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7432051301002502, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.743 confidence", "processing_time": 0.06968474388122559}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text contains no offensive speech, just a description of food preparation and cooking activities.", "processing_time": 0.833751916885376}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply describes the process of cooking various dishes for a meal.", "processing_time": 2.1007890701293945, "retrieved_examples": "Example 1: \"Besides Thanksgiving , to me , is just an excuse to mess around and eat as much turkey , potatoes , corn , stuffing , and cranberry sauce as I can .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Next week is Thanksgiving .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"I went to Oklahoma for thanksgiving and spent over a $ 100.00 on their ( Amish ) pies , turkey and other food .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.107513189315796, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 37, "dynamic_weight_predictions": 37, "consensus_predictions": 0, "avg_coordination_time": 2.540488082009393, "system_type": "intelligent_coordination"}, "text": "We are cooking a duck on the rotisserie , catch the fat drippings and cook little potatoes in that , its delicious , we 're also cooking a small turkey , mashed potatoes , carrots , pumpkin pie and butternut squash cut in half and baked with butter and brown sugar .", "true_label": 0, "processing_time": 2.107513189315796, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5393179543316364, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.81); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8145436346530914, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.815 confidence", "processing_time": 0.06037282943725586}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains references to violence and physical harm, making it offensive and inappropriate.", "processing_time": 0.8408780097961426}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it discusses a practical advantage of having a shaved head and recounts a past incident without promoting or condoning violence.", "processing_time": 2.4007251262664795, "retrieved_examples": "Example 1: \"Hair freshly cut ...\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"dont screw your life up because of some stupid little fight by using a weapon .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"The problem is in blacks ' heads , but hey , you have already known that .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.4070074558258057, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 38, "dynamic_weight_predictions": 38, "consensus_predictions": 0, "avg_coordination_time": 2.53697543395193, "system_type": "intelligent_coordination"}, "text": "it is better with a shaved head.A friend of mine was beaten really bad a year ago because they could have pulled him by his hair .", "true_label": 0, "processing_time": 2.4070074558258057, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5431183241307735, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.84); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8449465930461884, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.845 confidence", "processing_time": 0.07200312614440918}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Expresses frustration with deception, not offensive speech", "processing_time": 0.9617619514465332}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it criticizes the dishonesty of a group without targeting any specific individual or group.", "processing_time": 2.7207884788513184, "retrieved_examples": "Example 1: \"It is necessary for them to hide behind lies and false information because face it , the truth is not on their side .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"How can others be so blind to this ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"You find they are worse since they will able to blend more and fool people into thinking they 're genuine .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.7257864475250244, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 39, "dynamic_weight_predictions": 39, "consensus_predictions": 0, "avg_coordination_time": 2.5418167419922657, "system_type": "intelligent_coordination"}, "text": "Their lies are so thick and many that people do n't see the truth when it is in front of them !", "true_label": 1, "processing_time": 2.7257864475250244, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5331553369760513, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.77); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.7652426958084106, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.765 confidence", "processing_time": 0.05887341499328613}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and offensive language towards Filipinos, Indians, and Asians", "processing_time": 0.8210718631744385}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory and offensive language towards Filipinos, Indians, and Asians based on their ethnicity.", "processing_time": 2.2098915576934814, "retrieved_examples": "Example 1: \"We have such a huge Asian problem .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"They have been taking advantage of our over-welcoming nature for far to long .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I was beaten and robbed by two natives last year and over the last couple of years I have watched downtown turn into black , brown and yellow town. the food court at Portage place has started to remind me of the creature cantina from star wars .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.213435411453247, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 40, "dynamic_weight_predictions": 40, "consensus_predictions": 0, "avg_coordination_time": 2.53360720872879, "system_type": "intelligent_coordination"}, "text": "All of our tim hortons are staffed by those nast filipinos and every single town is full of Indians and asians .", "true_label": 1, "processing_time": 2.213435411453247, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5516679063439369, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.91); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9133432507514954, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.913 confidence", "processing_time": 0.06548547744750977}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content, anti-Semitic remarks, and insults towards specific groups and individuals", "processing_time": 0.****************}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech targeting Jewish people by using derogatory terms and spreading hateful stereotypes.", "processing_time": 2.****************, "retrieved_examples": "Example 1: \"In these dark days of jewish/Marxist/mafia dominated materialistic societies where everyone and anyone is only a bank note to be traded .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Ukraine needs to rid itself of these Jewish Oligarchs .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"There are really few Jews and millions of those bastards on the body of our country .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.****************, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 41, "dynamic_weight_predictions": 41, "consensus_predictions": 0, "avg_coordination_time": 2.***************, "system_type": "intelligent_coordination"}, "text": "somebody``hcf''calledputin``ahero''!lololol!This``hero''finallytransformedformersovietjewnion in ``russian''judea!In this misery-torn country-are there-70 officially billionaries , tens f thousands millionaries , and tens of millions of poor and miserable who look for their pittance at the garbage places !", "true_label": 1, "processing_time": 2.****************, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5339832901954651, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.77); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.7718663215637207, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.772 confidence", "processing_time": 0.0683598518371582}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of the term 'savages' is derogatory and dehumanizing, inciting violence and hatred towards a specific group of people.", "processing_time": 0.9457964897155762}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it dehumanizes and refers to a group of people as 'savages'. It also implies a call for violent justice.", "processing_time": 2.5487329959869385, "retrieved_examples": "Example 1: \"May they be put away for good .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I think it 's time for some old west justice ... like takin ' these two sombitches out and ..... well , I think you get my point .....\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I wish everyone would leave them to their own demise so we can be done with it .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.553633451461792, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 42, "dynamic_weight_predictions": 42, "consensus_predictions": 0, "avg_coordination_time": 2.5418812888009206, "system_type": "intelligent_coordination"}, "text": "May they just need to turn these savages loose , justice will take care of itself .", "true_label": 1, "processing_time": 2.553633451461792, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5437308251857758, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.85); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.849846601486206, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.850 confidence", "processing_time": 0.06801748275756836}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains offensive language and expresses hatred towards an individual, referring to them as a 'creature' and dismissing their thoughts.", "processing_time": 0.9127562046051025}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains dehumanizing language ('creature') and expresses a dismissive and offensive attitude ('to hell with').", "processing_time": 2.1281678676605225, "retrieved_examples": "Example 1: \"When she goes outside , the subhuman ape thing is usually out there .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Not to mention that they do n't belong here anyway .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"He is a pathetic little chimp .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.131305694580078, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 43, "dynamic_weight_predictions": 43, "consensus_predictions": 0, "avg_coordination_time": 2.5323330191678775, "system_type": "intelligent_coordination"}, "text": "To hell with what this creature thinks anyway .", "true_label": 1, "processing_time": 2.131305694580078, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5522421952337027, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.92); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9179375618696213, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.918 confidence", "processing_time": 0.06209921836853027}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Expresses disappointment with being taken to a Chinese restaurant, but does not contain explicit insults, threats, or hatred", "processing_time": 0.8926753997802734}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it expresses disgust towards a Chinese restaurant, implying a negative stereotype.", "processing_time": 2.1598424911499023, "retrieved_examples": "Example 1: \"Two days ago I tapped out a half asian , half White mongrel who was bigger than me .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I havent been in Mayo in around three years but back then it was one of the last good Irish places left .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"One reason we moved out of our ( at one time 99 % white ) neighborhood was because massive numbers of Chinese moved in and i swear to you a literal rotten smell came with them .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.168792486190796, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 44, "dynamic_weight_predictions": 44, "consensus_predictions": 0, "avg_coordination_time": 2.5240707343274895, "system_type": "intelligent_coordination"}, "text": "After knocking back a few guinness we got pretty hungry and he said he knew a great place to go get some food , i was disgusted when he brought me to a chinese restaurant .", "true_label": 0, "processing_time": 2.168792486190796, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5389063376933336, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.81); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.811250701546669, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.811 confidence", "processing_time": 0.0604555606842041}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory and offensive language towards specific racial groups", "processing_time": 1.3004140853881836}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech with derogatory references to different racial groups.", "processing_time": 1.8311851024627686, "retrieved_examples": "Example 1: \"It should come as no surprise that the Jews start embracing the degeneracy that they promote ; what goes around comes around .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Because behaving like civilized and educated person is `` to white '' for negroes .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"It 's annoying to live in a time where a subhuman monkey coon thinks it 's okay to hit an Aryan white blond , blue-eyed child .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 1.833864450454712, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 45, "dynamic_weight_predictions": 45, "consensus_predictions": 0, "avg_coordination_time": 2.5087328169080947, "system_type": "intelligent_coordination"}, "text": "Then again , in this modern age of white youth emulating negroes and white adults groveling at the feet of the juden , perhaps it 's an appropriate word after all .", "true_label": 1, "processing_time": 1.833864450454712, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5438201762735844, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.85); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8505614101886749, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.851 confidence", "processing_time": 0.057775020599365234}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The statement does not contain explicit insults, threats, or derogatory content. It merely expresses a comparison between two groups based on a specific trait.", "processing_time": 1.0343947410583496}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it implies superiority based on virility, which can be considered discriminatory and offensive.", "processing_time": 2.4849486351013184, "retrieved_examples": "Example 1: \"Oh, yes they will skin us alive if given the chance .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I guess they would not want to offend an ape .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Just because we can `` breed '' does n't mean we should .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.489832639694214, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 46, "dynamic_weight_predictions": 46, "consensus_predictions": 0, "avg_coordination_time": 2.5083219434904014, "system_type": "intelligent_coordination"}, "text": "Of course , they are more virile than us , so that would n't happen would it ?!!!", "true_label": 1, "processing_time": 2.489832639694214, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5350289170940716, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.78); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7802313367525736, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.780 confidence", "processing_time": 0.06209373474121094}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text expresses skepticism towards media accuracy, but does not contain explicit insults, threats, or hatred. It merely discusses media misinformation.", "processing_time": 0.8383815288543701}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it promotes the idea of media deception and conspiracy theories, which can incite distrust and division.", "processing_time": 2.369746685028076, "retrieved_examples": "Example 1: \"Check it out this is disturbing but insightfull and revealing information that are people here need to see .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"The raw footage of these beasts in their moments of candor is so much more revealing and persuasive for helping our fellow white brothers and sisters see the truth than a thousand books could ever be .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Thanks for posting .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.3768489360809326, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 47, "dynamic_weight_predictions": 47, "consensus_predictions": 0, "avg_coordination_time": 2.5055246454604125, "system_type": "intelligent_coordination"}, "text": "great posts. see how the media has been lying to us for decades about what actually happened and what it was actually all about .", "true_label": 0, "processing_time": 2.3768489360809326, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5346841141581535, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.78); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7774729132652283, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.777 confidence", "processing_time": 0.06362295150756836}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory content towards homosexuals, implying their behavior is unjustifiable and bad.", "processing_time": 0.8055734634399414}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by demeaning and invalidating the arguments of homosexuals based on their sexual orientation.", "processing_time": 2.1409034729003906, "retrieved_examples": "Example 1: \"Homosexuals practice a perverse lifestyle that destroys the body , mind , and soul just as drug use does ; merely two paths to the same destructive end .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"It is important for people to realize that gays want gay marriage ..... not because they desire some sort of government paperwork , but because they are seeking the right to adopt defenseless children .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Homosexuals should stay in the closet .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.145164728164673, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 48, "dynamic_weight_predictions": 48, "consensus_predictions": 0, "avg_coordination_time": 2.498017147183418, "system_type": "intelligent_coordination"}, "text": "This is the argument homosexuals use to justify their behavior , that because there are no `` red letters '' about it , then homosexuality is not a bad thing .", "true_label": 1, "processing_time": 2.145164728164673, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5372252389788628, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.80); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7978019118309021, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.798 confidence", "processing_time": 0.0665581226348877}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is friendly and positive, expressing a desire to communicate with the recipient. It does not contain any offensive content.", "processing_time": 1.026015043258667}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is non-offensive as it contains polite and friendly language, expressing appreciation and positivity.", "processing_time": 2.6322102546691895, "retrieved_examples": "Example 1: \"hi there , well thank you very much for your reply. it means alot that someone took the time to. i really envy your job. i want it lol. so were abouts are you from ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Thank you for your kind words , hope to meet you in the new white world one day soon .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Hello to all my friends here on the forum .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.6398534774780273, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 49, "dynamic_weight_predictions": 49, "consensus_predictions": 0, "avg_coordination_time": 2.500911766169022, "system_type": "intelligent_coordination"}, "text": "hi read your post and my name is jam<PERSON> from australia , and would like to well write to such a pleasent person as yourself have a nice day !", "true_label": 0, "processing_time": 2.6398534774780273, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5413509160280228, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.83); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8308073282241821, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.831 confidence", "processing_time": 0.0638127326965332}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory and offensive language towards a specific racial group, using a derogatory term and implying incompetence based on race.", "processing_time": 0.9274637699127197}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language referring to a racial group in a derogatory and demeaning manner.", "processing_time": 3.06528902053833, "retrieved_examples": "Example 1: \"Negros are so dumb .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"The only thing Negroes have is wishful want .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Typical Negro .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 3.070075273513794, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 50, "dynamic_weight_predictions": 50, "consensus_predictions": 0, "avg_coordination_time": 2.5122950363159173, "system_type": "intelligent_coordination"}, "text": "Negroids could n't put together a bake sale properly much less a work force .", "true_label": 1, "processing_time": 3.070075273513794, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5172557905316353, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.64); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.52)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.6380463242530823, "reasoning": "Hybrid classification using best_zero_shot with 0.638 confidence", "processing_time": 0.06722378730773926}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory terms and expressions targeting specific groups, indicating offensive speech.", "processing_time": 0.7690341472625732}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language targeting individuals based on their sexual orientation and race.", "processing_time": 2.20963716506958, "retrieved_examples": "Example 1: \"Always nice to see a negro on the receiving end of a beat down from a white for a change .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Hopefully they do n't get intimidated by the enemies of our land and of our freedom - those antifa scum .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I do n't think that any white person with brown hair brown eye 's is infirior to me .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.2160661220550537, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 51, "dynamic_weight_predictions": 51, "consensus_predictions": 0, "avg_coordination_time": 2.506486626232371, "system_type": "intelligent_coordination"}, "text": "I am not going to be beat down by a dyke , fag , or coon .", "true_label": 1, "processing_time": 2.2160661220550537, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5248727686703205, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.70); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.52)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.6989821493625641, "reasoning": "Hybrid classification using best_similarity with 0.699 confidence", "processing_time": 0.061739444732666016}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory language and insults towards a specific individual, <PERSON><PERSON>, referring to him as a 'useless savage'.", "processing_time": 0.9249553680419922}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive language by referring to a group of people as 'useless savages'. This dehumanizing language is derogatory and offensive.", "processing_time": 2.3642196655273438, "retrieved_examples": "Example 1: \"It is n't enough that we support mesico now we have to support africa .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"We need to make sure every white child and adult is fed before we worry about helpless people who have to eat cow crap in order to survive .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"And this is why SA went from First World under <PERSON><PERSON> 's rule ( <PERSON><PERSON>lerk does n't count ) to a Third World begger horde .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.3720295429229736, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 52, "dynamic_weight_predictions": 52, "consensus_predictions": 0, "avg_coordination_time": 2.5039009130918055, "system_type": "intelligent_coordination"}, "text": "The problem is that through western elites we have to feed and look after useless savages like malema .", "true_label": 1, "processing_time": 2.3720295429229736, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5495133126775424, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.896106501420339, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.896 confidence", "processing_time": 0.07196903228759766}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory and offensive language directed at a specific individual, referring to them as 'filthy sons and daughters of Satan'.", "processing_time": 0.9296832084655762}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by referring to individuals as 'filthy sons and daughters of Satan'. It is derogatory and disrespectful.", "processing_time": 2.649202823638916, "retrieved_examples": "Example 1: \"These ' people ' are just scum .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"The only thing manipulated here are the minds of few foreigners , who reckon they can live amongest superiour , pagan blood .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"It should come as no surprise that the Jews start embracing the degeneracy that they promote ; what goes around comes around .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.6546518802642822, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 53, "dynamic_weight_predictions": 53, "consensus_predictions": 0, "avg_coordination_time": 2.5067452709629845, "system_type": "intelligent_coordination"}, "text": "Except , of course , for those that pander to the filthy sons and daughters of <PERSON> like ' ol <PERSON> here .", "true_label": 1, "processing_time": 2.6546518802642822, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5529741942882538, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.92); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9237935543060303, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.924 confidence", "processing_time": 0.05871462821960449}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory content and promotes racial stereotypes and fear of violence towards specific groups based on ethnicity.", "processing_time": 1.0705528259277344}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it promotes racial stereotypes and incites violence based on ethnicity.", "processing_time": 2.5892891883850098, "retrieved_examples": "Example 1: \"The rag-heads dont have the numbers to kick off in Ireland yet , but when they do , you will know all about it .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Being a smaller country , Ireland will quicker fill up with ethnic scum .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Glad it made news your way the truth must be told but have you noticed it says it is not a racist attack , Oh but we all know the story shall it of been some poor black bastard bashed to near death in Dublin eh\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.5925159454345703, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 54, "dynamic_weight_predictions": 54, "consensus_predictions": 0, "avg_coordination_time": 2.5083336167865324, "system_type": "intelligent_coordination"}, "text": "I heard from friends in Boston this is true.Blacks will attack an Irish America with a shamrock tattoo.How long before this happens in Ireland ?", "true_label": 1, "processing_time": 2.5925159454345703, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5381743460893631, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.81); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8053947687149048, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.805 confidence", "processing_time": 0.06775975227355957}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains derogatory content towards Mexicans and sexism towards females", "processing_time": 1.0847997665405273}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it targets Mexicans and implies discrimination based on ethnicity.", "processing_time": 2.7085297107696533, "retrieved_examples": "Example 1: \"A lot of white women are trying to create danger by drawing these other people into our countries .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Hey guys please go here to help us send the Mexicans back to Mexico .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"If the International Drug Cartels are causing so much DEATH and DESTRUCTION in Mexico , then why allow them to just walk across our Border ?\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.712822437286377, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 55, "dynamic_weight_predictions": 55, "consensus_predictions": 0, "avg_coordination_time": 2.512051595341075, "system_type": "intelligent_coordination"}, "text": "We have two things wrong here , Mexcicans in a White homeland and a Pizza company sending a female in harms way .", "true_label": 1, "processing_time": 2.712822437286377, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5475948229432106, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.88); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8807585835456848, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.881 confidence", "processing_time": 0.07357501983642578}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive speech expressing a personal opinion on dietary choices.", "processing_time": 0.795926570892334}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it discusses a personal choice without using any offensive language.", "processing_time": 2.1774778366088867, "retrieved_examples": "Example 1: \"You are going to have to make this decision on your own .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Who the hell cares if its not kosher ...\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"You may as well think of it as the blood of an animal .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.1816372871398926, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 56, "dynamic_weight_predictions": 56, "consensus_predictions": 0, "avg_coordination_time": 2.5061513398374826, "system_type": "intelligent_coordination"}, "text": "Whether or not one chooses to eat meat is a very personal decision .", "true_label": 0, "processing_time": 2.1816372871398926, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5412185601890087, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.83); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8297484815120697, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.830 confidence", "processing_time": 0.05911588668823242}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement implies satisfaction or approval of harm or punishment inflicted on someone, indicating offensive content.", "processing_time": 0.869560718536377}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it implies that someone received a fair outcome or consequence.", "processing_time": 2.189528465270996, "retrieved_examples": "Example 1: \"They all deserve an ass kicking .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"He is a pathetic little chimp .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Good work .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.19370436668396, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 57, "dynamic_weight_predictions": 57, "consensus_predictions": 0, "avg_coordination_time": 2.5006698139926837, "system_type": "intelligent_coordination"}, "text": "He got what he deserved .", "true_label": 1, "processing_time": 2.19370436668396, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5537617206573486, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.93); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9300937652587891, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.930 confidence", "processing_time": 0.0632009506225586}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it is a neutral statement about sharing pictures on MSN.", "processing_time": 0.8435180187225342}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply mentions sharing pictures with people on MSN without any explicit offensive language or intent.", "processing_time": 2.127668857574463, "retrieved_examples": "Example 1: \"If i sent you some pic wound use be able to put them up cuz use dont have many also i have lots of links to give use\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"A few weeks ago i started looking into doing that and have got a few pictures and LOADS of mobile numbers so far , reds are just SO naive !\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I put you on my msn. dont accept if you dont want too\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.1321828365325928, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 58, "dynamic_weight_predictions": 58, "consensus_predictions": 0, "avg_coordination_time": 2.494316590243372, "system_type": "intelligent_coordination"}, "text": "Everyone on my MSN gets all manner of pics ( ) but I 've never put any up here so here goes Some pics from about a year ago and a small anigif from a week ago heh Robin", "true_label": 0, "processing_time": 2.1321828365325928, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5414545051753521, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.83); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8316360414028168, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.832 confidence", "processing_time": 0.06234931945800781}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of a racial slur 'niggas' is offensive and derogatory towards a specific group of individuals.", "processing_time": 0.8937816619873047}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains a derogatory term ('niggas') which is offensive and disrespectful.", "processing_time": 2.000053644180298, "retrieved_examples": "Example 1: \"Niggas do n't wait in no lines .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"These ' people ' are just scum .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Crippin nigga .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.0077643394470215, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 59, "dynamic_weight_predictions": 59, "consensus_predictions": 0, "avg_coordination_time": 2.48606994192479, "system_type": "intelligent_coordination"}, "text": "These ' niggas ' .", "true_label": 1, "processing_time": 2.0077643394470215, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5456181429326534, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.86); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8649451434612274, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.865 confidence", "processing_time": 0.06132698059082031}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement expresses a discriminatory attitude towards non-white individuals, indicating offensive speech.", "processing_time": 0.7666361331939697}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it promotes segregation based on race.", "processing_time": 2.9113352298736572, "retrieved_examples": "Example 1: \"Both me and my wife would be very involved in every aspect of their schooling and the area we are moving to is very very white so will have to find out more from white parents already there that send their children to public school .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Looking at your guys situation makes me realise how lucky I am that my high school is 100 % white however even in my remote countryside community there are more and more black 's moving into the area , and their kids .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"The white population must have a place it calls its own , segregated .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.9174723625183105, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 60, "dynamic_weight_predictions": 60, "consensus_predictions": 0, "avg_coordination_time": 2.493259982268015, "system_type": "intelligent_coordination"}, "text": "I have one word for you ....... MOVE . We chose this area because we wanted our children to go to all white schools .", "true_label": 0, "processing_time": 2.9174723625183105, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5591036677360535, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.97); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9728293418884277, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.973 confidence", "processing_time": 0.05733513832092285}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The statement describes a personal observation without containing explicit insults, threats, or derogatory content. It does not target specific groups or individuals.", "processing_time": 0.8398036956787109}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech as it simply describes observing people being angry after seeing shots with a .45.", "processing_time": 2.4296441078186035, "retrieved_examples": "Example 1: \"I 'm surprised they 're not holding the gun back to front .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Blacks can shoot , funny thing is they only seem to be able to hit the little innocent law abiding child , walking home from church to help take of their sick mother .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"A guy could shoot anything in that $ 140 Leinad 410 double barrel .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.4336278438568115, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 61, "dynamic_weight_predictions": 61, "consensus_predictions": 0, "avg_coordination_time": 2.492282406228487, "system_type": "intelligent_coordination"}, "text": "I 've seen shots with a .45 , where people walked around angry .", "true_label": 0, "processing_time": 2.4336278438568115, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5423603691160679, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.84); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8388829529285431, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.839 confidence", "processing_time": 0.06661653518676758}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain explicit insults, threats, hatred, or derogatory content. It appears to be a casual observation and speculation.", "processing_time": 0.9703788757324219}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it does not contain any derogatory language or discriminatory remarks.", "processing_time": 2.625121831893921, "retrieved_examples": "Example 1: \"Typical i suppose in the eyes of the law he is just another tourist beaten on sydneys streets oh yeah and we all know if it was a poor black fella bashed it would be on the news for weeks on end and mr whitey would be branded a big bad evil racist\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I 've seen negroes who look ( and undoubtedly act ) like him .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Anyone white person would if they knew the truth .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.631312131881714, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 62, "dynamic_weight_predictions": 62, "consensus_predictions": 0, "avg_coordination_time": 2.494524821158378, "system_type": "intelligent_coordination"}, "text": "now that ye mention it he does look a bit like this traveller i knew. i wonder if we gave him a wash would he really be a white guy with red hair ?", "true_label": 0, "processing_time": 2.631312131881714, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5366021543741226, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.79); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.792817234992981, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.793 confidence", "processing_time": 0.06516027450561523}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain any offensive speech, it is a statement about interpreting someone's feelings based on their actions.", "processing_time": 0.8449051380157471}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it talks about romantic interest and does not contain any explicit offensive language or intent.", "processing_time": 2.488412380218506, "retrieved_examples": "Example 1: \"Make a visit here , lad . .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"It 's there .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"The subtle message .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.491030216217041, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 63, "dynamic_weight_predictions": 63, "consensus_predictions": 0, "avg_coordination_time": 2.494469351238674, "system_type": "intelligent_coordination"}, "text": "If her eyes linger ................................... she 's yours .", "true_label": 0, "processing_time": 2.491030216217041, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5442429048319657, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.85); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8539432386557261, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.854 confidence", "processing_time": 0.06290173530578613}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit suggestion of a violent and destructive act, along with derogatory language towards specific groups", "processing_time": 0.8563921451568604}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech advocating for a violent and destructive act involving nuclear weapons in Kashmir, which is highly inappropriate and insensitive.", "processing_time": 4.062957048416138, "retrieved_examples": "Example 1: \"I 'd like to see Iran wipe Israel off the map .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Seriously Canada would have been better off if they left the pakis at home , im like you I do n't object to asians because they at least try to fit in .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Pakis are all about forcing their culture on us .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 4.065562009811401, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 64, "dynamic_weight_predictions": 64, "consensus_predictions": 0, "avg_coordination_time": 2.519017674028873, "system_type": "intelligent_coordination"}, "text": "I always thought it would be cool if somebody covertly set off a nuke in Kashmir , and then we all could sit back and watch as the Paki 's and Indians nuke the scheiss out of each other .", "true_label": 1, "processing_time": 4.065562009811401, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5152063183486462, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.62); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.52)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.6216505467891693, "reasoning": "Hybrid classification using best_similarity with 0.622 confidence", "processing_time": 0.06544065475463867}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text provided is neutral and does not contain any offensive speech.", "processing_time": 0.8088858127593994}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply refers to a press home page without containing any offensive language or content.", "processing_time": 2.0028164386749268, "retrieved_examples": "Example 1: \"http://www.righttomarch.com/\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"www.natall.com\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"This is the main logo when you sign on to their website\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.009356737136841, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 65, "dynamic_weight_predictions": 65, "consensus_predictions": 0, "avg_coordination_time": 2.5111767365382263, "system_type": "intelligent_coordination"}, "text": "Invictus Press Home Page", "true_label": 0, "processing_time": 2.009356737136841, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5054184943437576, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.54); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.51)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.543347954750061, "reasoning": "Hybrid classification using best_zero_shot with 0.543 confidence", "processing_time": 0.06606125831604004}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is neutral and does not contain any offensive content.", "processing_time": 0.8915979862213135}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply acknowledges agreement or correctness.", "processing_time": 122.9058587551117, "retrieved_examples": "Example 1: \"Your right .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Precisely!\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"I agree .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 122.90954566001892, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 66, "dynamic_weight_predictions": 66, "consensus_predictions": 0, "avg_coordination_time": 4.335394447500056, "system_type": "intelligent_coordination"}, "text": "Right.", "true_label": 0, "processing_time": 122.90954566001892, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5469432696700096, "reasoning": "激进优化协调决策: deberta(原权重:0.45→优化权重:0.12, 预测:0, 置信度:0.88); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.25→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8755461573600769, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.876 confidence", "processing_time": 0.07605528831481934}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text provides demographic statistics about Miami, Florida, without containing any offensive content. It is purely factual information.", "processing_time": 1.0533490180969238}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it refers to Hispanic, Black, White, Asian, and other races in a derogatory manner.", "processing_time": 2.254182815551758, "retrieved_examples": "Example 1: \"These are the latest statistics , Ethnic groups : Irish 87.4 % , other white 7.5 % , Asian 1.3 % , black 1.1 % , mixed 1.1 % , unspecified 1.6 % ( 2006 census ) , so even if that seven percent was all poles , if you add up all the mud races , it would be equivalent .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"There are few if any Blacks .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Very good school with alot of whites , very few hispanics and very few blacks but many asians .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.44999999999999996, "llm_single": 0.3, "llm_retrieval": 0.25}, "coordination_time": 2.2572529315948486, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 67, "dynamic_weight_predictions": 67, "consensus_predictions": 0, "avg_coordination_time": 4.304377409949232, "system_type": "intelligent_coordination"}, "text": "Miami,( I do n't know how <PERSON> can stand living down there ) Hispanic - 279,456 ( 70.0 % ) Black alone - 64,993 ( 16.3 % ) White alone - 47,622 ( 11.9 % ) Asian alone - 3,649 ( 0.9 % ) Two or more races - 2,642 ( 0.7 % ) Other race alone - 676 ( 0.2 % ) American Indian alone - 361 ( 0.09 % ) Native Hawaiian and Other Pacific Islander alone - 58 ( 0.01 % ) Read more : Miami , Florida ( FL ) profile : population , maps , real estate , averages , homes , statistics , relocation , travel , jobs , hospitals , schools , crime , moving , houses , news , sex offenders", "true_label": 1, "processing_time": 2.2572529315948486, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5382594242691994, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.81); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.806075394153595, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.806 confidence", "processing_time": 0.06206655502319336}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it is a statement about the presence of oil below 40,000 feet.", "processing_time": 0.9484143257141113}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it discusses the presence of oil in holes below 40,000 feet.", "processing_time": 2.6517887115478516, "retrieved_examples": "Example 1: \"Our dams are overflowing except for the Cape .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"With lots of file work and 3 inches of hundreds .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"A mud is a mud is a mud that 's all there is to it .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.6557772159576416, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 68, "dynamic_weight_predictions": 68, "consensus_predictions": 0, "avg_coordination_time": 4.280133289449355, "system_type": "intelligent_coordination"}, "text": "And all those holes below 40,000 feet are filled with oil instead of water .", "true_label": 0, "processing_time": 2.6557772159576416, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5545049160718918, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.94); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9360393285751343, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.936 confidence", "processing_time": 0.06705904006958008}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive speech expressing positive opinion about a documentary", "processing_time": 0.7489919662475586}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it expresses admiration for a documentary.", "processing_time": 2.0780038833618164, "retrieved_examples": "Example 1: \"It 's massive .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"I was there and I must say it 's great museum .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Really interesting .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.080193519592285, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 69, "dynamic_weight_predictions": 69, "consensus_predictions": 0, "avg_coordination_time": 4.248250104378963, "system_type": "intelligent_coordination"}, "text": "It 's a pretty powerful documentary that blew me away .", "true_label": 0, "processing_time": 2.080193519592285, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5577875897288322, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.96); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.962300717830658, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.962 confidence", "processing_time": 0.0639951229095459}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains information about a murder suspect shooting at officers and killing his estranged wife, which is offensive and violent.", "processing_time": 0.8606595993041992}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text describes a crime incident involving a murder suspect being taken into custody. It does not contain any offensive language or content.", "processing_time": 2.11358904838562, "retrieved_examples": "Example 1: \"Los Angeles County sheriff 's officials arrested 53-year-old <PERSON> on Saturday night , just two days after the killings .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Fatal shooting victim Police find murder victim <PERSON> woman 's death ruled a homicide\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Negro Parolee St<PERSON>les Elderly <PERSON>uple In Their Home , Suspected In Another Murder ...\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.117692232131958, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 70, "dynamic_weight_predictions": 70, "consensus_predictions": 0, "avg_coordination_time": 4.217813563346863, "system_type": "intelligent_coordination"}, "text": "Richland County deputies say <PERSON><PERSON><PERSON> killed his estranged wife , 38-year-old <PERSON><PERSON><PERSON> , at an apartment on Smallwood Road in Northeast Richland County Deputies : Murder Suspect Shoots at Officers , is Taken Into Custody wltx.com | Columbia , SC News , Weather and Sports |", "true_label": 0, "processing_time": 2.117692232131958, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5444358885288239, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.86); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8554871082305908, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.855 confidence", "processing_time": 0.06716561317443848}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain any explicit insults, threats, hatred, or derogatory content. It expresses an opinion about the consequences of withdrawing support.", "processing_time": 0.8838887214660645}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it implies harm or danger towards a group of people.", "processing_time": 2.29150652885437, "retrieved_examples": "Example 1: \"They ca n't survive without us , but they do us nothing but harm .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"They are supporting the destruction of their future generations .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"The soldiers of today may face this same treatment before they die if things keep going the way it has been .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.297516107559204, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 71, "dynamic_weight_predictions": 71, "consensus_predictions": 0, "avg_coordination_time": 4.1907671203076, "system_type": "intelligent_coordination"}, "text": "The above poster is correct when we stop supporting then they 're in terrible danger .", "true_label": 0, "processing_time": 2.297516107559204, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5204614326357841, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.66); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.52)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.6636914610862732, "reasoning": "Hybrid classification using best_similarity with 0.664 confidence", "processing_time": 0.0640416145324707}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The statement expresses a preference against assimilation, which is a non-offensive opinion.", "processing_time": 0.8273000717163086}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it expresses a preference against assimilation.", "processing_time": 2.3733527660369873, "retrieved_examples": "Example 1: \"There should be no integration of immigrants with Irish culture .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"They sound so naive , when will they learn that these people do not want to intergrate just accumulate .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"If we are White and wish to save this country rather than have it turn into a disorderly non-White hegemony , then we are all at a loss for what to do .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.379976749420166, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 72, "dynamic_weight_predictions": 72, "consensus_predictions": 0, "avg_coordination_time": 4.165617254045274, "system_type": "intelligent_coordination"}, "text": "We do not want `` assimilation '' .", "true_label": 1, "processing_time": 2.379976749420166, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5490083619952202, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.89); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8920668959617615, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.892 confidence", "processing_time": 0.061246633529663086}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it expresses concern for the well-being of monkeys.", "processing_time": 0.8106954097747803}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it shows concern for the monkeys and questions the actions taken against them.", "processing_time": 2.2952613830566406, "retrieved_examples": "Example 1: \"I was going to say - before you added that last line - they look like a bunch of frightened monkeys with guns ....\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I guess they would not want to offend an ape .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"If monkeys are on my property i call animal control and defend my family and property with whatever means possible , simply talking sense to a monkey and pointing at it will not stop it from misbehaving !\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.3001976013183594, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 73, "dynamic_weight_predictions": 73, "consensus_predictions": 0, "avg_coordination_time": 4.140063560172302, "system_type": "intelligent_coordination"}, "text": "What did those poor monkeys ever do to you ?", "true_label": 0, "processing_time": 2.3001976013183594, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5353495143353939, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.78); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7827961146831512, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.783 confidence", "processing_time": 0.061585426330566406}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains offensive racial stereotypes and derogatory language towards African people", "processing_time": 0.7614052295684814}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by using derogatory terms and perpetuating stereotypes about race.", "processing_time": 2.0430235862731934, "retrieved_examples": "Example 1: \"Whites in all their glory against impossibly large hordes of non-whites .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Any kind of civilization Negroes have today is a gift from the White race .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Innocent whites including white children all murdered by heartless groid savages .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.0472681522369385, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 74, "dynamic_weight_predictions": 74, "consensus_predictions": 0, "avg_coordination_time": 4.111782541146149, "system_type": "intelligent_coordination"}, "text": "White town saved by the magic negroes of Afreakkaland .", "true_label": 1, "processing_time": 2.0472681522369385, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5600113943219185, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.98); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9800911545753479, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.980 confidence", "processing_time": 0.0615842342376709}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text expresses agreement and a warning about dealing with certain individuals, without containing explicit insults, threats, or derogatory content.", "processing_time": 0.8301999568939209}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech inciting violence and aggression towards others.", "processing_time": 2.2158665657043457, "retrieved_examples": "Example 1: \"If I feel threatened especially by these fools you can bet I 'll be just as quick to kill as they are .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"i've felt anger when i 've read it on forum , but we have to do our part and speaking at loud voice this fact , because , for example , i 'm from europe and this fact is not pased on tv news or in the news paper ... knows your enemies is the first pass for vicory on them !\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Do n't sit around and argue with them , just act like they are pathetic ( which they are ) and try to shame them .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.2216074466705322, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 75, "dynamic_weight_predictions": 75, "consensus_predictions": 0, "avg_coordination_time": 4.0865802065531405, "system_type": "intelligent_coordination"}, "text": "Amen brother ..... you did say it like you been there ... they talk crap and if you show fear they 'll be on ya fast .", "true_label": 0, "processing_time": 2.2216074466705322, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5428264755755663, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.84); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8426118046045303, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.843 confidence", "processing_time": 0.05843520164489746}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text expresses doubt about the accuracy of reported numbers, followed by an opinion. It does not contain offensive speech.", "processing_time": 1.033297061920166}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text expresses doubt about the accuracy of numbers and shares an opinion, but it does not contain any explicit offensive language or intent.", "processing_time": 3.1106669902801514, "retrieved_examples": "Example 1: \"And im sure theres a lot more people that would do the same .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"It is necessary for them to hide behind lies and false information because face it , the truth is not on their side .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"I have read a 70 -/ + million figure , 92.5 million figure and an estimate of over 100 million figure So I compromised at 90 .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 3.1150355339050293, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 76, "dynamic_weight_predictions": 76, "consensus_predictions": 0, "avg_coordination_time": 4.073796724018297, "system_type": "intelligent_coordination"}, "text": "I was sure that they were lying about the numbers and it 's must be the same in every country IMO .", "true_label": 0, "processing_time": 3.1150355339050293, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5362428016960621, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.79); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.7899424135684967, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.790 confidence", "processing_time": 0.06656146049499512}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it is a neutral question seeking clarification.", "processing_time": 0.8679423332214355}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is not offensive as it does not contain any language or context that is derogatory or harmful.", "processing_time": 2.035465717315674, "retrieved_examples": "Example 1: \"That negro 's place is in Africa , and the teacher did n't send him there .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"She 's never had a lesson !\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Maybe this was mentioned in an earlier post but what about `` online '' home schooling ?\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.041771650314331, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 77, "dynamic_weight_predictions": 77, "consensus_predictions": 0, "avg_coordination_time": 4.0474067879961675, "system_type": "intelligent_coordination"}, "text": "I'm too lazy to reread , did he say ' Teacher `` or '' Teacher 's asst . \" ?", "true_label": 0, "processing_time": 2.041771650314331, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5164038427174091, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.63); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.52)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.6312307417392731, "reasoning": "Hybrid classification using best_similarity with 0.631 confidence", "processing_time": 0.05956292152404785}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains dehumanizing language and expresses a derogatory attitude towards a specific group of people, suggesting they have no value.", "processing_time": 0.8449742794036865}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text dehumanizes a group of people by referring to them as 'things' and suggests that they serve no purpose in the civilized world, which is offensive.", "processing_time": 2.420653820037842, "retrieved_examples": "Example 1: \"Why should we try to civilize that which cannot be domesticated or civil ?\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Put the mudraces in their own countries , build a fence around them and wait a decade or two , and soon they 'll be back to the state we found them in , with their numbers decimated by their diseases and wars .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"They 're a waste of space and they 're filthy , tax payer robbing , mentally challenged , primitive , unintelligent cultural trash .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.4249517917633057, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 78, "dynamic_weight_predictions": 78, "consensus_predictions": 0, "avg_coordination_time": 4.026606082916259, "system_type": "intelligent_coordination"}, "text": "Let mother nature evolve these people naturally because no amount of money or aid is going to drag these ' things ' into the civilized world where they serve absolutely no purpose .", "true_label": 1, "processing_time": 2.4249517917633057, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5421175522108872, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.84); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8369404176870981, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.837 confidence", "processing_time": 0.07138967514038086}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech contains offensive content as it refers to a derogatory term and objectifies individuals based on their sexual status.", "processing_time": 0.8649866580963135}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it refers to a person who is new to shooting.", "processing_time": 123.32297992706299, "retrieved_examples": "Example 1: \"An Ar15 .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Shoot me a message .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Crippin nigga .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 123.32749152183533, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 79, "dynamic_weight_predictions": 79, "consensus_predictions": 0, "avg_coordination_time": 5.536743873282323, "system_type": "intelligent_coordination"}, "text": "Ya got a virgin shooter .", "true_label": 0, "processing_time": 123.32749152183533, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5471805296838284, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.88); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8774442374706268, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.877 confidence", "processing_time": 0.0618283748626709}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain any offensive speech, it simply mentions following someone's adventures.", "processing_time": 0.9956181049346924}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it simply mentions following someone's adventures through a specific platform.", "processing_time": 2.8789925575256348, "retrieved_examples": "Example 1: \"I was recently in Zaporojie , Ukraine , which has a famous Cossack enclave , where I had the opportunity to dine at a Cossack restaurant and listen to traditional Cossack music played live .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"He far excelled <PERSON><PERSON><PERSON> and <PERSON> .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Tell your friend he wins ; I 've now decided to make a video documentary about the Jewish Ritual Murder of <PERSON> .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.8838682174682617, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 80, "dynamic_weight_predictions": 80, "consensus_predictions": 0, "avg_coordination_time": 5.503582927584647, "system_type": "intelligent_coordination"}, "text": "I've followed Mr<PERSON> 's adventures through VNN for the last couple years .", "true_label": 0, "processing_time": 2.8838682174682617, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5168462172150612, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.63); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.52)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.6347697377204895, "reasoning": "Hybrid classification using best_zero_shot with 0.635 confidence", "processing_time": 0.06591534614562988}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive speech, expressing a personal achievement without any offensive content.", "processing_time": 0.9816269874572754}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply mentions receiving a score on a paper.", "processing_time": 2.446977138519287, "retrieved_examples": "Example 1: \"I just took that test .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"I also , unfortuantely , spent 7-8th grade in a 95 % monkey house .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"88 !\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.451256275177002, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 81, "dynamic_weight_predictions": 81, "consensus_predictions": 0, "avg_coordination_time": 5.465899882493194, "system_type": "intelligent_coordination"}, "text": "Nothing happened except I got an 88 % on the paper .", "true_label": 0, "processing_time": 2.451256275177002, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5501311247547467, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.901048998037974, "reasoning": "Hybrid classification using weighted_consensus_3_methods with 0.901 confidence", "processing_time": 0.0651402473449707}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it does not contain any explicit insults, threats, hatred, or derogatory content.", "processing_time": 1.1793386936187744}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it expresses interest and comparison to a science fiction novel.", "processing_time": 2.1256496906280518, "retrieved_examples": "Example 1: \"Really interesting .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"The Book of Revelation !\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"It is quite farcical actually .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.132061004638672, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 82, "dynamic_weight_predictions": 82, "consensus_predictions": 0, "avg_coordination_time": 5.425243310812042, "system_type": "intelligent_coordination"}, "text": "It almost reads like something out of a science fiction novel .", "true_label": 0, "processing_time": 2.132061004638672, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5508948564529419, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.91); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9071588516235352, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.907 confidence", "processing_time": 0.0619966983795166}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech contains derogatory and offensive language towards individuals based on their race, referring to them as 'non WHITE garbage'.", "processing_time": 1.3018465042114258}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it dehumanizes individuals based on their race.", "processing_time": 1.9711990356445312, "retrieved_examples": "Example 1: \"The non white homelands have so much trash and crime in them that non whites mainly choose to stay away .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"The raw footage of these beasts in their moments of candor is so much more revealing and persuasive for helping our fellow white brothers and sisters see the truth than a thousand books could ever be .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I hate to see such beautiful white females become victims of these sick , violent merciless animals .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 1.977388620376587, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 83, "dynamic_weight_predictions": 83, "consensus_predictions": 0, "avg_coordination_time": 5.383702892854988, "system_type": "intelligent_coordination"}, "text": "Their stories have been told before , so instead of showing the pics of the non WHITE garbage that took from them , I am just going to post their pics so they will not be forgotten .", "true_label": 1, "processing_time": 1.977388620376587, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5495007038116455, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.90); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.8960056304931641, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.896 confidence", "processing_time": 0.06777453422546387}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains a derogatory and discriminatory remark towards Africans, inciting hatred and promoting exclusion based on ethnicity.", "processing_time": 2.5255517959594727}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it suggests kicking out a group of people based on their ethnicity.", "processing_time": 2.1232118606567383, "retrieved_examples": "Example 1: \"Send them all to Afreaka And sink the farkin boat !\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"What about all the tens of millions of negroes that are nothing more than criminals and parasites that do nothing but breed more criminals and parasites ?\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Are you asking the Africans in South Africa why they hate White people so much that they have murdered over 40 thousand of them since the end of White rule ?\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.529524803161621, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 84, "dynamic_weight_predictions": 84, "consensus_predictions": 0, "avg_coordination_time": 5.3497245822634, "system_type": "intelligent_coordination"}, "text": "Why not just kick out all those Africans ?", "true_label": 1, "processing_time": 2.529524803161621, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5543148200958967, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.93); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9345185607671738, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.935 confidence", "processing_time": 0.06554865837097168}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The use of the term 'negroes' in a derogatory manner and the statement implying criminal behavior is offensive and discriminatory.", "processing_time": 2.0296194553375244}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it perpetuates negative stereotypes about a specific racial group.", "processing_time": 1.969804048538208, "retrieved_examples": "Example 1: \"Yes the negroes have been busy the last few weeks but we have to expect it because nothing is being done to them when a crime is committed\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Latest reported attack on students by the local Negroes was on a couple out getting a pizza in the late evening hours , 4 Blacks jumped them on their walk home .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Beware of the falling house prices that Negroes bring with them ( another reason why you 'll never find them living near politicans ) .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.0321505069732666, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 85, "dynamic_weight_predictions": 85, "consensus_predictions": 0, "avg_coordination_time": 5.310694299024693, "system_type": "intelligent_coordination"}, "text": "I reported some time ago that negroes will even strip the bricks off your home while you are away at work .", "true_label": 1, "processing_time": 2.0321505069732666, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5353065058588982, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.78); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7824520468711853, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.782 confidence", "processing_time": 0.08214354515075684}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply expresses difficulty in finding time to meet like-minded people for coffee.", "processing_time": 1.1226985454559326}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply discusses the challenge of meeting like-minded people for coffee.", "processing_time": 2.113724708557129, "retrieved_examples": "Example 1: \"looking for sisters and brothers to hang out with get to know ...... cant semm to find in in this area ?\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"We need to get ahold of everyone and plan something this weekend .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"If I was back home the only reason I would go was to meet up with some of you guys for lunch or something .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.1191627979278564, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 86, "dynamic_weight_predictions": 86, "consensus_predictions": 0, "avg_coordination_time": 5.27358346761659, "system_type": "intelligent_coordination"}, "text": "I've tried to meet for coffee with a few people but everybody is really spread out , it 's tough to find the time to go out and meet like minded people .", "true_label": 0, "processing_time": 2.1191627979278564, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5462120268493891, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.87); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.8696962147951126, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.870 confidence", "processing_time": 0.06119656562805176}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit encouragement of neglect and endangerment of children, along with derogatory language towards a specific individual", "processing_time": 1.0178070068359375}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech by suggesting harm towards <PERSON><PERSON><PERSON>'s children and using derogatory language.", "processing_time": 2.0559351444244385, "retrieved_examples": "Example 1: \"put the kids in a ophanage and arrest the whore of a mother to a point of not having more biracial kids 8 kids have to suffer all their lives to due a whore\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I hope all their farms burn down and their nappy headed children squandle in the flames .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"Wow, maybe next time you should leave your little sister at home especially since you live in a highly non-white populated area .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.0596702098846436, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 87, "dynamic_weight_predictions": 87, "consensus_predictions": 0, "avg_coordination_time": 5.236641935918522, "system_type": "intelligent_coordination"}, "text": "Just let <PERSON><PERSON><PERSON> leave her kids home to burn the house down while she goes to da club .", "true_label": 1, "processing_time": 2.0596702098846436, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5324885472655296, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.76); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7599083781242371, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.760 confidence", "processing_time": 0.07189679145812988}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The statement contains a derogatory and offensive tone by implying that the recipient should return to their place of origin.", "processing_time": 1.033665657043457}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains a directive for someone to go back to where they came from, which can be derogatory and xenophobic.", "processing_time": 2.1526176929473877, "retrieved_examples": "Example 1: \"One day when true people come to power in italy they will have to go back where they came from ! Italy to Italians !!\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"I wish they 'd all bugger off back to the Sudan , Nigeria , Ethiopia or where ever the hell it is they 're from and never come back .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"No more !\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.1562864780426025, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 88, "dynamic_weight_predictions": 88, "consensus_predictions": 0, "avg_coordination_time": 5.201637896624477, "system_type": "intelligent_coordination"}, "text": "Back to where ya came from !", "true_label": 1, "processing_time": 2.1562864780426025, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5424208268523216, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.84); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.839366614818573, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.839 confidence", "processing_time": 0.06698966026306152}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit hatred and derogatory content directed at the Jewish community.", "processing_time": 1.7320091724395752}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains derogatory and dehumanizing language towards a specific group of people.", "processing_time": 3.155250310897827, "retrieved_examples": "Example 1: \"Jews are not only insane , they are of literal demonic DNA .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"The Jew represents the snake .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"The true essence of divinity .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 3.161501169204712, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 89, "dynamic_weight_predictions": 89, "consensus_predictions": 0, "avg_coordination_time": 5.178715012046727, "system_type": "intelligent_coordination"}, "text": "In Eternal Combat with that Satanic Spirit - The physical incarnation , creation of the Demiurge : The Jews .", "true_label": 1, "processing_time": 3.161501169204712, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5564365275204182, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.95); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9514922201633453, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.951 confidence", "processing_time": 0.06421136856079102}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain any offensive speech, it simply discusses the change in desires of some individuals after living in Finland for a few years.", "processing_time": 1.301772117614746}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it does not contain any derogatory language or discriminatory remarks.", "processing_time": 2.006838083267212, "retrieved_examples": "Example 1: \"Yeah I think they 'd be much better off back home .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"hope one day i will be able to see finland withouth those * * * * ing monkey ass soumalian ... im glad .. because finland has lowest immigrate in europe ..... kick those * * * * ing soumalians ass .....\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I wish they 'd all bugger off back to the Sudan , Nigeria , Ethiopia or where ever the hell it is they 're from and never come back .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.0122671127319336, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 90, "dynamic_weight_predictions": 90, "consensus_predictions": 0, "avg_coordination_time": 5.143532257609896, "system_type": "intelligent_coordination"}, "text": "Some of them claim that they would like to go back home one day but after a few years in Finland they will no longer have this wish .", "true_label": 0, "processing_time": 2.0122671127319336, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.538190932944417, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.81); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.54)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.805527463555336, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.806 confidence", "processing_time": 0.06461191177368164}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive, friendly request for conversation", "processing_time": 0.9625997543334961}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it is a friendly and casual request for conversation.", "processing_time": 2.45771861076355, "retrieved_examples": "Example 1: \"Hey, I live in Illinois not too far from you. i 'm always trying to find new friends and people to hang out with , some email me sometime\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"throw me a message as well folks .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"whats up I thought I might put my pic up here I 'm looking for some friends theres too many negroes around here some times I feel like there is' int any white guy left in the world. so message me and let me know there is stll some white brothers out their .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.4603521823883057, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 91, "dynamic_weight_predictions": 91, "consensus_predictions": 0, "avg_coordination_time": 5.11404676227779, "system_type": "intelligent_coordination"}, "text": "hey whats up i 'm new on here and i would like to have someone to talk to so hit me back for whatever", "true_label": 0, "processing_time": 2.4603521823883057, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5264037922024727, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.71); llm_single(原权重:0.25→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.53)", "chosen_agent": "deberta", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.7112303376197815, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.711 confidence", "processing_time": 0.06325936317443848}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain offensive speech, it is a suggestion or opinion regarding a location.", "processing_time": 1.198639154434204}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it suggests considering different locations to move to.", "processing_time": 1.77122163772583, "retrieved_examples": "Example 1: \"You may want to consider the Harrison Arkansas area as well .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"You have to have your extreme papers to move here .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"I would move too but unfortunately there is nowhere that is untainted .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 1.7738032341003418, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 92, "dynamic_weight_predictions": 92, "consensus_predictions": 0, "avg_coordination_time": 5.077739767406297, "system_type": "intelligent_coordination"}, "text": "Maybe you should look there or just move here to miss. Or more specifically Tupel<PERSON> .", "true_label": 0, "processing_time": 1.7738032341003418, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5397235471755266, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.82); llm_single(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.54)", "chosen_agent": "llm_retrieval", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.817788377404213, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.818 confidence", "processing_time": 0.05532956123352051}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The term 'Zionist' can be associated with controversial political and religious views, potentially inciting hatred or discrimination.", "processing_time": 2.2088468074798584}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it contains derogatory language towards a specific group of people.", "processing_time": 2.609522581100464, "retrieved_examples": "Example 1: \"You weather the zionist storm well , my brother .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Your history teacher has to do what the zionist butt heads want them to do .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"It should come as no surprise that the Jews start embracing the degeneracy that they promote ; what goes around comes around .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.6138765811920166, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 93, "dynamic_weight_predictions": 93, "consensus_predictions": 0, "avg_coordination_time": 5.051246614866359, "system_type": "intelligent_coordination"}, "text": "They have properly learnt their Zionist lesson ...", "true_label": 0, "processing_time": 2.6138765811920166, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5252874791622162, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.70); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.53)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.7022998332977295, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.702 confidence", "processing_time": 0.06287717819213867}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain any offensive speech or content.", "processing_time": 0.9260654449462891}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it expresses purity or cleanliness.", "processing_time": 2.2911126613616943, "retrieved_examples": "Example 1: \"The true essence of divinity .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Oh .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"Literally !!!!\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.2982919216156006, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 94, "dynamic_weight_predictions": 94, "consensus_predictions": 0, "avg_coordination_time": 5.021959862810499, "system_type": "intelligent_coordination"}, "text": "Pure .", "true_label": 0, "processing_time": 2.2982919216156006, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5592476055026054, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.97); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.56)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.9739808440208435, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.974 confidence", "processing_time": 0.07254505157470703}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains derogatory remarks towards Mexicans and Hispanics, suggesting they are undercutting prices and implying they are not 'good working people'.", "processing_time": 1.271514892578125}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it discriminates against Mexicans and other Hispanics by implying they are undercutting prices and are not 'good working people'.", "processing_time": 2.287264108657837, "retrieved_examples": "Example 1: \"I have been trying to find a job but the mexicans take everything we had .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Hey guys please go here to help us send the Mexicans back to Mexico .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I wonder how they 'll paint them up plus the 1million or more illegals who are working in the service industry living in their bosses ' cupboards .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.2963783740997314, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 95, "dynamic_weight_predictions": 95, "consensus_predictions": 0, "avg_coordination_time": 4.9932695313503865, "system_type": "intelligent_coordination"}, "text": "Someone Please Tell me what can be done or which people to tell that the Mexicans and or other hispanics here are undercutting price quotes on Good working People .", "true_label": 1, "processing_time": 2.2963783740997314, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5129038617014885, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:1, 置信度:0.60); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.51)", "chosen_agent": "llm_retrieval", "agent_consensus": "no_consensus", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.603230893611908, "reasoning": "Hybrid classification using best_zero_shot with 0.603 confidence", "processing_time": 0.06565356254577637}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text does not contain any offensive speech.", "processing_time": 0.8765914440155029}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it does not contain any language or context that can be considered offensive.", "processing_time": 2.3614282608032227, "retrieved_examples": "Example 1: \"2508\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"2508\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"35109\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.366407632827759, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 96, "dynamic_weight_predictions": 96, "consensus_predictions": 0, "avg_coordination_time": 4.965906386574109, "system_type": "intelligent_coordination"}, "text": "2508", "true_label": 0, "processing_time": 2.366407632827759, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5257738083600998, "reasoning": "激进优化协调决策: deberta(原权重:0.45→优化权重:0.12, 预测:0, 置信度:0.71); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.25→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.53)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7061904668807983, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.706 confidence", "processing_time": 0.06991767883300781}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "The text contains personality assessment results and does not contain any offensive speech.", "processing_time": 0.9502544403076172}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The speech is not offensive as it is a description of personality traits based on a psychological test result.", "processing_time": 1.9991726875305176, "retrieved_examples": "Example 1: \"They probably have a 150 I.Q. with all three of them combined .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Sub 70 I.Q. I would say for sure .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"I believe , judging from your other posts , that you belong more towards the back .\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.44999999999999996, "llm_single": 0.3, "llm_retrieval": 0.25}, "coordination_time": 2.003918409347534, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 97, "dynamic_weight_predictions": 97, "consensus_predictions": 0, "avg_coordination_time": 4.935370428046, "system_type": "intelligent_coordination"}, "text": "INTJ Introvert ( 11 % ) iNtuitive ( 38 % ) iNtuitive Thinking ( 38 % ) Judging ( 22 % ) You have slight preference of Introversion over Extraversion ( 11 % ) You have moderate preference of Intuition over Sensing ( 38 % ) You have moderate preference of Thinking over Feeling ( 38 % ) You have slight preference of Judging over Perceiving ( 22 % )", "true_label": 0, "processing_time": 2.003918409347534, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5251872912049294, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:0, 置信度:0.70); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "deberta", "agent_consensus": "weak_majority", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.7014983296394348, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.701 confidence", "processing_time": 0.07268214225769043}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "Contains explicit derogatory content and incites hatred towards ethnic groups", "processing_time": 1.7901113033294678}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The speech is offensive as it promotes ethnic cleansing and discrimination based on ethnicity.", "processing_time": 2.3226661682128906, "retrieved_examples": "Example 1: \"I've never been to London but from what I 've heard it 's like spot the white man and every girl down there almost has a little niglet half breed .\"\nVerdict: 1 (offensive speech)\n\nExample 2: \"Maybe if you 'd have gone down to London you could have marched with hundreds of nationalists in the very centre of our nations capital ...\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"London is a dead zone as far as anyone White is concerned , even Hertfordshire where it 's closest to London is turning brown .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.32844614982605, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 98, "dynamic_weight_predictions": 98, "consensus_predictions": 0, "avg_coordination_time": 4.908769159900899, "system_type": "intelligent_coordination"}, "text": "You Anglo-Americans are all welcome to England if you can perform the ' small ' task of riding London of it 's ethnics !", "true_label": 1, "processing_time": 2.328968048095703, "dataset": "HateSpeechStormfront"}, {"verdict": 1, "confidence": 0.5335237197577953, "reasoning": "激进优化协调决策: deberta(原权重:0.40→优化权重:0.12, 预测:1, 置信度:0.77); llm_single(原权重:0.25→优化权重:0.12, 预测:1, 置信度:0.50); llm_retrieval(原权重:0.35→优化权重:0.75, 预测:1, 置信度:0.50). 最终预测: 1 (置信度: 0.53)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 1, "confidence": 0.7681897580623627, "reasoning": "Hybrid classification using weighted_consensus_2_methods with 0.768 confidence", "processing_time": 0.0599825382232666}, "llm_single": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it promotes racial preservation and spreads conspiracy theories about the deliberate extermination of the White race, which can incite hatred and discrimination.", "processing_time": 2.287750482559204}, "llm_retrieval": {"verdict": 1, "confidence": 0.5, "reasoning": "The text contains offensive speech as it promotes the idea of the deliberate extermination of the White race and the destruction of western civilization.", "processing_time": 2.349255323410034, "retrieved_examples": "Example 1: \"Racial preservation is the belief that the existence of a certain race must be preserved ( and well of course its culture ) .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Here is your logic : some whites are corrupt whores for Jewish supremacists , therefore Whites should not oppose Jewish supremacist extermination of all Whites and JS efforts to control humanity 's future .\"\nVerdict: 1 (offensive speech)\n\nExample 3: \"If we are White and wish to save this country rather than have it turn into a disorderly non-White hegemony , then we are all at a loss for what to do .\"\nVerdict: 1 (offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.4, "llm_single": 0.25, "llm_retrieval": 0.*****************}, "coordination_time": 2.3529374599456787, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 99, "dynamic_weight_predictions": 99, "consensus_predictions": 0, "avg_coordination_time": 4.88295267808317, "system_type": "intelligent_coordination"}, "text": "What does any of that have to do with racial preservation and the resistance to the deliberate and calculated extermination of the White race and the destruction of western civilization which 99 % of the members of this site are concerned with ?", "true_label": 0, "processing_time": 2.3529374599456787, "dataset": "HateSpeechStormfront"}, {"verdict": 0, "confidence": 0.5525524728000164, "reasoning": "激进优化协调决策: deberta(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.92); llm_single(原权重:0.30→优化权重:0.12, 预测:0, 置信度:0.50); llm_retrieval(原权重:0.40→优化权重:0.75, 预测:0, 置信度:0.50). 最终预测: 0 (置信度: 0.55)", "chosen_agent": "consensus", "agent_consensus": "unanimous", "valid_agents_count": 3, "total_agents_count": 3, "optimized_weights": {"deberta": 0.125, "llm_single": 0.125, "llm_retrieval": 0.75}, "agent_results": {"deberta": {"verdict": 0, "confidence": 0.9204197824001312, "reasoning": "Hybrid classification using weighted_consensus_1_methods with 0.920 confidence", "processing_time": 0.06786870956420898}, "llm_single": {"verdict": 0, "confidence": 0.5, "reasoning": "Non-offensive speech, expressing a positive opinion about <PERSON>'s assistance", "processing_time": 0.941443681716919}, "llm_retrieval": {"verdict": 0, "confidence": 0.5, "reasoning": "The text is non-offensive as it simply mentions hearing about <PERSON>'s coming to someone's aid.", "processing_time": 2.2989449501037598, "retrieved_examples": "Example 1: \"From what I hear .\"\nVerdict: 0 (non-offensive speech)\n\nExample 2: \"Heard about this a while back .\"\nVerdict: 0 (non-offensive speech)\n\nExample 3: \"I hope their is some sort off protest about this\"\nVerdict: 0 (non-offensive speech)\n\n"}}, "coordination_method": "dynamic_weighted", "agent_weights": {"deberta": 0.3, "llm_single": 0.3, "llm_retrieval": 0.4}, "coordination_time": 2.3015246391296387, "system_type": "intelligent_coordination", "performance_stats": {"total_predictions": 100, "dynamic_weight_predictions": 100, "consensus_predictions": 0, "avg_coordination_time": 4.857138397693634, "system_type": "intelligent_coordination"}, "text": "I hear that <PERSON><PERSON> is coming to their aid .", "true_label": 0, "processing_time": 2.3015246391296387, "dataset": "HateSpeechStormfront"}], "system_config": {"enable_dynamic_weights": true, "enable_performance_tracking": true, "enable_online_learning": true, "coordination_method": "dynamic_weighted", "learning_rate": 0.01, "confidence_threshold": 0.6}}